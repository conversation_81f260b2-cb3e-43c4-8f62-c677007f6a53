buildscript {
    repositories {
        mavenCentral()
        maven {
            setUrl("https://plugins.gradle.org/m2/")
        }
    }
}


plugins {
    id("maven-publish")
    id("idea")
}

val buildEnv: String = System.getProperty("buildEnv", "local")
val env: String = (project.findProperty("env") ?: "local") as String
val latestGaMajorMinorVersion = "2.3"
val projectVersion: String = if (project.property("version") != "unspecified") (project.property("version") as String) else "2.3.0-SNAPSHOT"
val versionMajorMinor = projectVersion.split(".")[0] + "." + projectVersion.split(".")[1]
val isLatestGaMajorMinorVersion = versionMajorMinor == latestGaMajorMinorVersion
val isTested = project.findProperty("tested") == "true"

println("=============================================================== PROJECT (root build.gradle.kts) ====")
println("environment:               ${env}")
println("version:                   ${projectVersion} (${versionMajorMinor} is ${if (isLatestGaMajorMinorVersion) "" else "not "}latest GA version)")
println("tested:                    ${isTested}")
println("====================================================================================================")

allprojects {
    ext {
        set("buildEnv", buildEnv)
        set("env", env)
        set("latestGaMajorMinorVersion", latestGaMajorMinorVersion)
        set("versionMajorMinor", versionMajorMinor)
        set("version", projectVersion)
        set("isLatestGaMajorMinorVersion", isLatestGaMajorMinorVersion)
        set("isTested", isTested)
    }

    group = "cz.kpsys.portaro"
    version = projectVersion

    apply(plugin = "idea")
    idea {
        module {
            isDownloadJavadoc = true
            isDownloadSources = true
        }
    }

    dependencyLocking {
        lockAllConfigurations()
    }

    tasks.register("resolveAndLockAll") {
        notCompatibleWithConfigurationCache("Filters configurations at execution time")
        doFirst {
            require(gradle.startParameter.isWriteDependencyLocks) { "$path must be run from the command line with the `--write-locks` flag" }
        }
        doLast {
            configurations.filter {
                // Add any custom filtering on the configurations to be resolved
                it.isCanBeResolved
            }.forEach { it.resolve() }
        }
    }
}

subprojects {
    apply(plugin = "java-library")
    apply(plugin = "checkstyle")

    configure<CheckstyleExtension> {
        toolVersion = "10.19.0"
    }

    configure<JavaPluginExtension> {
        if (project.name != "portaro-runner") { // portaro-runner:sourcesJar task fails, so we skip it
            withSourcesJar()
        }
    }

    tasks.withType<JavaCompile> {
        sourceCompatibility = JavaVersion.VERSION_22.toString()
        targetCompatibility = JavaVersion.VERSION_22.toString()
        options.encoding = "UTF-8"
        options.compilerArgs.add("-parameters")
    }

    tasks.withType<Test> {
        useJUnitPlatform {
            if (ext.get("buildEnv") == "ci") {
                includeTags("ci")
            }
        }
    }

    repositories {
        maven {
            url = uri(System.getProperty("mavenRepositoryFetchUrl"))
            isAllowInsecureProtocol = true
        }
        mavenLocal()
    }

    configurations.all {
        exclude(module = "log4j") // we use logback
        exclude("org.apache.velocity", "velocity")
        exclude("org.bouncycastle", "bcprov-jdk15on") // we use bcprov-jdk18on
        exclude("org.bouncycastle", "bcpkix-jdk15on") // we use bcpkix-jdk18on
        resolutionStrategy {
            force("org.opensaml:opensaml-core:4.0.1") // fixing portaro-auth-saml2-sp > org.springframework.security:spring-security-saml2-service-provider:6.0.0 -> org.opensaml:opensaml-saml-core:4.1.1
            force("org.opensaml:opensaml-saml-api:4.0.1") // fixing portaro-auth-saml2-sp > org.springframework.security:spring-security-saml2-service-provider:6.0.0 -> org.opensaml:opensaml-saml-api:4.1.1
            force("org.opensaml:opensaml-saml-impl:4.0.1") // fixing portaro-auth-saml2-sp > org.springframework.security:spring-security-saml2-service-provider:6.0.0 -> org.opensaml:opensaml-saml-impl:4.1.1
            force("org.bouncycastle:bcpkix-jdk18on:1.72") // fixing bouncycastle versions conflict (on version 1.73) resulting in not working letsencrypt
            force("org.bouncycastle:bcprov-jdk18on:1.72") // fixing bouncycastle versions conflict (on version 1.73) resulting in not working letsencrypt
        }
    }

    apply(plugin = "maven-publish")
    publishing {
        publications {
            create<MavenPublication>("maven") {
                if (project.name != "portaro-test-e2e" && project.name != "portaro-test-integration") {
                    from(components["java"])
                }
            }
        }

        repositories {
            if (project.name != "portaro-test-environment" && project.name != "portaro-runner") {
                maven {
                    name = "mavenKpsys"
                    url = uri(System.getProperty("mavenRepositoryPublishUrl"))
                    isAllowInsecureProtocol = true
                    credentials {
                        //set repo username & password, see readme.md
                        username = project.findProperty("mavenUsername") as String?
                        password = project.findProperty("mavenPassword") as String?
                    }
                }
            }
        }
    }
}
