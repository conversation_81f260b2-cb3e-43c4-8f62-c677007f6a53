package cz.kpsys.portaro.commons.object;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

@Tag("ci")
@Tag("unit")
public class RangeTest {

    public void test(Range range, String...expected) {
        List<String> list = List.of("a", "b", "c", "d", "e");
        List<String> result = range.applyTo(list);
        Assertions.assertArrayEquals(result.toArray(), expected);
    }

    @Test
    public void shouldGetFirstListPage() {
        test(new Range(1, 2), "a", "b");
    }

    @Test
    public void shouldGetSecondListPage() {
        test(new Range(2, 2), "c", "d");
    }

    @Test
    public void shouldGetPartialyOverflownListPage() {
        test(new Range(1, 10), "a", "b", "c", "d", "e");
    }

    @Test
    public void shouldGetOnEdgeOverflownListPage() {
        test(new Range(2, 3), "d", "e");
    }

    @Test
    public void shouldGetFullyOverflownListPage() {
        test(new Range(2, 10));
    }
}