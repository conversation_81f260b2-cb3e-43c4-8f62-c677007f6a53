package cz.kpsys.portaro.business.function;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OverridableFunction<E, R> implements Function<@NonNull E, @NonNull R> {

    @NonNull Function<E, R> fallback;
    @NonNull List<ConditionalFunction<E, R>> overrides = new ArrayList<>();

    public void overrideWhen(@NonNull Function<E, @NonNull Boolean> enabledResolver,
                             @NonNull Function<E, R> function,
                             @NonNull String featureName) {
        overrides.add(new ConditionalFunction<>(enabledResolver, function, featureName));
    }

    @Transactional
    @Override
    public @NonNull R apply(@NonNull E input) {
        for (ConditionalFunction<E, R> override : overrides) {
            if (override.isEnabled(input)) {
                return override.apply(input);
            }
        }

        return fallback.apply(input);
    }

}
