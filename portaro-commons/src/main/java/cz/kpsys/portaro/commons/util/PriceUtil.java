package cz.kpsys.portaro.commons.util;

import lombok.NonNull;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

public class PriceUtil {

    public static Integer toMinorUnitsInteger(@NonNull BigDecimal amount) {
        return amount
                .multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.DOWN)
                .intValue();
    }

    public static Long toMinorUnitsLong(@NonNull BigDecimal amount) {
        return amount
                .multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.DOWN)
                .longValue();
    }

    public static BigInteger toMinorUnitsBigInteger(@NonNull BigDecimal amount) {
        return amount
                .multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.DOWN)
                .toBigInteger();
    }

}
