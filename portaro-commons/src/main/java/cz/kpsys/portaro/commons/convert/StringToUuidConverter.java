package cz.kpsys.portaro.commons.convert;

import cz.kpsys.portaro.id.UuidGenerator;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StringToUuidConverter implements Converter<String, UUID>, Serializable {

    public static final StringToUuidConverter INSTANCE = new StringToUuidConverter();
    public static final Converter<String, List<UUID>> DELIMITED_LIST_CONVERTER = new ChainingConverter<>(
            new StringToStringListConverter(",").throwOnBlankItems(),
            new ListToModifiedListConverter<>(StringToUuidConverter.INSTANCE)
    ).throwWhenNull();

    @NonNull
    @Override
    public UUID convert(@NonNull String source) {
        if (source.isEmpty()) {
            throw new EmptySourceForConversionException(this.getClass());
        }
        return fromString(source);
    }

    public static @NonNull UUID fromString(@NonNull String source) {
        if (!canBeUuidFast(source)) {
            throw new ConversionException("Cannot convert \"%s\" to UUID, given string is not UUID".formatted(source));
        }
        try {
            return UUID.fromString(source);
        } catch (IllegalArgumentException e) {
            throw new ConversionException("Cannot convert \"%s\" to UUID".formatted(source), e);
        }
    }

    public static Optional<UUID> tryFromString(@NonNull String source) {
        if (!isUuid(source)) {
            return Optional.empty();
        }
        return Optional.of(UUID.fromString(source));
    }

    public static boolean isUuid(@NonNull String source) {
        if (!canBeUuidFast(source)) {
            return false;
        }
        return source.matches(UuidGenerator.UUID_PATTERN_WHOLE);
    }

    public static @NonNull UUID tryConvertFromObject(@NonNull Object o) {
        if (o instanceof UUID uuid) {
            return uuid;
        }
        if (o instanceof String s) {
            return fromString(s);
        }
        throw new ConversionException("Cannot convert \"%s\" to UUID, it is of unknown class!".formatted(o));
    }

    private static boolean canBeUuidFast(@NonNull String source) {
        return source.length() == UuidGenerator.UUID_LENGTH && source.contains("-") && source.toLowerCase().equals(source);
    }

}
