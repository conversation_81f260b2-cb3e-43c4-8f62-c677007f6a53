package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.RIdentified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class AllValuesProvidedCodebook<E, ID> implements Codebook<E, ID> {

    @NonNull AllValuesProvider<E> allValuesProvider;
    @NonNull Function<E, ID> itemIdGetter;

    public static <E extends Identified<ID>, ID> AllValuesProvidedCodebook<E, ID> ofIdentified(@NonNull AllValuesProvider<E> allValuesProvider) {
        return new AllValuesProvidedCodebook<>(allValuesProvider, Identified::getId);
    }

    public static <E extends RIdentified<ID>, ID> AllValuesProvidedCodebook<E, ID> ofRidentified(@NonNull AllValuesProvider<E> allValuesProvider) {
        return new AllValuesProvidedCodebook<>(allValuesProvider, RIdentified::getRid);
    }

    public static <E extends Identified<ID>, ID> AllValuesProvidedCodebook<E, ID> ofEmptyIdentified() {
        return new AllValuesProvidedCodebook<E, ID>(List::of, Identified::getId);
    }

    public static <E, ID> AllValuesProvidedCodebook<E, ID> of(@NonNull AllValuesProvider<E> allValuesProvider, @NonNull Function<E, ID> itemIdGetter) {
        return new AllValuesProvidedCodebook<E, ID>(allValuesProvider, itemIdGetter);
    }

    @Override
    public List<E> getAll() {
        return allValuesProvider.getAll();
    }

    @Override
    public E getById(@NonNull ID id) {
        return findById(id)
                .orElseThrow(() -> ItemNotFoundException.ofResolvedDesiredItemType(getAll(), id));
    }

    @Override
    public Optional<E> findById(@NonNull ID id) {
        return getAll().stream()
                .filter(e -> ObjectUtil.nullSafeEquals(itemIdGetter.apply(e), id))
                .findFirst();
    }
}
