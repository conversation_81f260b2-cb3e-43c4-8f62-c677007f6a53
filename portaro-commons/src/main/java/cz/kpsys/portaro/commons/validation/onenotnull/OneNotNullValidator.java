package cz.kpsys.portaro.commons.validation.onenotnull;

import cz.kpsys.portaro.commons.validation.FieldValidator;
import cz.kpsys.portaro.commons.validation.ValidationUtils;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class OneNotNullValidator implements ConstraintValidator<OneNotNull, Object> {

    @NonFinal
    Set<String> fields;

    @Override
    public void initialize(OneNotNull constraintAnnotation) {
        fields = Arrays.stream(constraintAnnotation.of()).collect(Collectors.toSet());
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();

        if (object == null) {
            return true;
        }

        return new OneNotNullFieldValidator(object, fields)
                .validateFields(object, fields)
                .setFieldFailedValidationToContextWithMessage(context)
                .isValid();
    }


    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private static class OneNotNullFieldValidator implements FieldValidator {

        @NonNull Object object;
        @NonNull Set<String> allFields;

        @Override
        public boolean isValid(String fieldName, Object fieldValue) {
            Set<String> otherFields = getOtherFields(fieldName);

            boolean thisFieldHasValue = ValidationUtils.HAS_VALUE_VALIDATOR.isValid(fieldName, fieldValue);
            long otherFieldsWithValueCount = otherFields.stream().filter(otherFieldName -> ValidationUtils.HAS_VALUE_VALIDATOR.validateField(object, otherFieldName).isValid()).count();

            if (thisFieldHasValue) {
                return otherFieldsWithValueCount == 0;
            } else {
                return otherFieldsWithValueCount == 1;
            }
        }

        @Override
        public String getInvalidMessage(String fieldName) {
            return "One of %s is required".formatted(String.join(",", allFields));
        }

        private Set<String> getOtherFields(String fieldName) {
            HashSet<String> otherFields = new HashSet<>(allFields);
            otherFields.remove(fieldName);
            return otherFields;
        }
    }

}