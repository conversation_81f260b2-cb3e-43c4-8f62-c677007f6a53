package cz.kpsys.portaro.commons.object.repo;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class IdConvertingByIdLoadable<E, ORIGINAL_ID, ID> implements ByIdAndByIdOptLoadable<E, ORIGINAL_ID> {

    @NonNull Converter<ORIGINAL_ID, ID> idConverter;
    @NonNull ByIdLoadable<E, ID> loader;

    @Override
    public E getById(@NonNull ORIGINAL_ID originalId) throws ItemNotFoundException {
        ID id = requireNonNull(idConverter.convert(originalId));
        return loader.getById(id);
    }

    @Override
    public Optional<E> findById(@NonNull ORIGINAL_ID originalId) {
        ID id = requireNonNull(idConverter.convert(originalId));

        if (loader instanceof ByIdOptLoadable<?, ?>) {
            ByIdOptLoadable<E, ID> castedLoader = (ByIdOptLoadable<E, ID>) loader;
            return castedLoader.findById(id);
        }

        try {
            return Optional.of(loader.getById(id));
        } catch (ItemNotFoundException e) {
            return Optional.empty();
        }
    }

    @Override
    public String toString() {
        return "IdConvertingByIdLoadable{-> %s, idConverter=%s}".formatted(loader, idConverter);
    }
}
