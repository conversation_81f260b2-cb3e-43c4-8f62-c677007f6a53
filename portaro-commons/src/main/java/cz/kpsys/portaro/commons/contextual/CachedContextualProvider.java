package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CachedContextualProvider<CTX, V> implements ContextualProvider<CTX, V> {

    @NonNull ContextualProvider<CTX, V> delegate;
    @NonNull Map<CTX, CachedValue<V>> cache = new HashMap<>();
    @NonNull ReentrantLockLocker locker = new ReentrantLockLocker();

    @Override
    public V getOn(CTX ctx) throws ItemNotFoundException {
        return locker.lock(() -> {
            CachedValue<V> cachedValue = cache.computeIfAbsent(ctx, this::loadValue);
            return cachedValue.value;
        });
    }

    @NonNull
    private CachedValue<V> loadValue(CTX context) {
        V value = delegate.getOn(context);
        return new CachedValue<>(value);
    }

    public void clear() {
        locker.lock(cache::clear);
    }

    public void clearOn(CTX ctx) {
        locker.lock(() -> {
            cache.remove(ctx);
        });
    }

    private record CachedValue<V>(@Nullable V value) {}

    @Override
    public CachedContextualProvider<CTX, V> cached() {
        throw new IllegalStateException("Provider is already cached, cannot call 'contextualProvider.cached()' again");
    }

}
