package cz.kpsys.portaro.commons.contextual;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.util.ListUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FilteredAndSortedAllValuesContextualProvider<CTX, E extends LabeledIdentified<ID>, ID> implements ContextualProvider<CTX, List<E>> {

    @NonNull AllValuesProvider<E> allValuesProvider;
    @NonNull ContextualProvider<CTX, List<ID>> allowedIdsProvider;
    @NonFinal
    ContextualProvider<CTX, Boolean> showAllProvider = ContextIgnoringContextualProvider.of(false);

    public FilteredAndSortedAllValuesContextualProvider<CTX, E, ID> withConditionallyShowAll(ContextualProvider<CTX, Boolean> showAllProvider) {
        this.showAllProvider = showAllProvider;
        return this;
    }

    @Override
    public List<E> getOn(CTX ctx) {
        List<E> all = allValuesProvider.getAll();
        if (showAllProvider.getOn(ctx)) {
            return all;
        }
        List<ID> allowedIds = allowedIdsProvider.getOn(ctx);
        return ListUtil.filterAndSortByRule(all, allowedIds);
    }
}
