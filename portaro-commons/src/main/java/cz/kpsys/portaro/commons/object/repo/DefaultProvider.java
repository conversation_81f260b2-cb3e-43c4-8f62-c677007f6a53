package cz.kpsys.portaro.commons.object.repo;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.function.Predicate;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultProvider<E> implements Provider<@NonNull E> {

    @NonNull Provider<? extends Collection<E>> allValuesProvider;
    @NonNull DefaultResolver<E> defaultResolver;

    public static <E> Provider<@NonNull E> byFirst(@NonNull AllValuesProvider<E> allValuesProvider) {
        return new DefaultProvider<>(allValuesProvider::getAll, new DefaultResolverByFirst<>());
    }

    public static <E> Provider<E> byFirst(@NonNull Provider<? extends Collection<E>> allValuesProvider) {
        return new DefaultProvider<>(allValuesProvider, new DefaultResolverByFirst<>());
    }

    public static <E extends Identified<ID>, ID> Provider<@NonNull E> byId(@NonNull AllValuesProvider<E> allValuesProvider, @NonNull ID defaultId) {
        return new DefaultProvider<>(allValuesProvider::getAll, new DefaultResolverById<>(defaultId));
    }

    public static <E extends Identified<ID>, ID> Provider<@NonNull E> byPredicate(@NonNull AllValuesProvider<E> allValuesProvider, @NonNull Predicate<E> predicate) {
        return new DefaultProvider<>(allValuesProvider::getAll, new DefaultResolverByPredicate<>(predicate));
    }

    @NonNull
    @Override
    public E get() {
        Collection<E> all = allValuesProvider.get();
        return defaultResolver.resolveDefault(all);
    }
}
