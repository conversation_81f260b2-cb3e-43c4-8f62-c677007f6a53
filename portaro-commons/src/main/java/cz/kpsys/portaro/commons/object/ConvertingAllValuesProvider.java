package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class ConvertingAllValuesProvider<E, F> implements AllValuesProvider<F> {

    @NonNull AllValuesProvider<? extends E> valuesProvider;
    @NonNull Converter<List<? extends E>, List<F>> converter;

    public static <E, F> ConvertingAllValuesProvider<E, F> byListConverter(@NonNull AllValuesProvider<? extends E> valuesProvider, @NonNull Converter<List<? extends E>, List<F>> converter) {
        return new ConvertingAllValuesProvider<E, F>(valuesProvider, converter);
    }

    public static <E, F> ConvertingAllValuesProvider<E, F> byItemConverter(@NonNull AllValuesProvider<? extends E> valuesProvider, @NonNull Converter<E, F> converter) {
        return new ConvertingAllValuesProvider<E, F>(valuesProvider, new ListToModifiedListConverter<>(converter));
    }

    @Override
    public List<F> getAll() {
        List<? extends E> allValues = Objects.requireNonNull(valuesProvider.getAll());
        return converter.convert(allValues);
    }

    @Override
    public String toString() {
        return "ConvertingAllValuesProvider{valuesProvider=%s, converter=%s}".formatted(valuesProvider, converter);
    }
}
