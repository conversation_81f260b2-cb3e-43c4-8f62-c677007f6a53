package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;

import java.util.function.Function;

public record IdentifiedValue<ID, VALUE>(

        @NonNull
        ID id,

        @NonNull
        VALUE value

) implements IdentifiedRecord<ID> {

    public static <ID, VALUE> IdentifiedValue<ID, VALUE> of(@NonNull ID id, @NonNull VALUE value) {
        return new IdentifiedValue<>(id, value);
    }

    public <MAPPED_VALUE> IdentifiedValue<ID, MAPPED_VALUE> mapValue(Function<VALUE, MAPPED_VALUE> mapper) {
        MAPPED_VALUE mappedValue = mapper.apply(value);
        return of(id, mappedValue);
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equals(this, o, IdentifiedValue.class, IdentifiedValue::id);
    }

    public int hashCode() {
        return getId().hashCode();
    }
}
