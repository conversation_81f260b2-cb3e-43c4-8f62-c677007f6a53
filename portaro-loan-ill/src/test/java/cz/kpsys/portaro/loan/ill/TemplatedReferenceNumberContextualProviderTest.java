package cz.kpsys.portaro.loan.ill;

import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.util.PatternMatcher;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.stream.Stream;

@Tag("ci")
@Tag("unit")
class TemplatedReferenceNumberContextualProviderTest {

    @Test
    public void shouldLoadFromCommonWithByYearDisabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>,<RR>",
                null,
                false,
                Stream.of("1235,24", "asdf,24", "1234,24")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("1236,24", actual);
    }

    @Test
    public void shouldLoadFromCommonWithByYearEnabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>,<RR>",
                null,
                true,
                Stream.of("1235,23", "asdf,24", "1234,24")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("1235,24", actual);
    }

    @Test
    public void shouldLoadFromCommonWithYearAtStartAndByYearDisabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<RRRR>-<CC>",
                null,
                false,
                Stream.of("2024-12", "2024-asdf", "2024-11", "2025-1")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("2024-13", actual);
    }

    @Test
    public void shouldLoadFromCommonWithYearAtStartAndByYearEnabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<RRRR>-<CC>",
                null,
                true,
                Stream.of("2023-12", "2024-asdf", "2024-11", "2025-1")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("2024-12", actual);
    }

    @Test
    public void shouldLoadFromSpecificWithByYearDisabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>",
                "neco<CC> / <RRRR>",
                false,
                Stream.of("neco1235 / 2024", "neco1235konec", "neco1234 / 2024")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("neco1236 / 2024", actual);
    }

    @Test
    public void shouldLoadFromSpecificWithByYearEnabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>",
                "neco<CC> / <RRRR>",
                true,
                Stream.of("neco1235 / 2023", "neco1235konec", "neco1 / 2024")
        );
        String actual = provider.getOn(new Object());
        Assertions.assertEquals("neco2 / 2024", actual);
    }

    @Test
    public void shouldCreatePatternWithByYearDisabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>,<RR>",
                null,
                false,
                Stream.of()
        );

        PatternMatcher actual = provider.createPattern("LP-<RRRR>/000<CC>", new Object());
        Assertions.assertTrue(actual.matches("LP-2024/0001"));
        Assertions.assertTrue(actual.matches("LP-2024/000123"));
        Assertions.assertTrue(actual.matches("LP-2023/000123"));
        Assertions.assertFalse(actual.matches("LP-2023/00123"));
        Assertions.assertFalse(actual.matches("LP:2023/000123"));
        Assertions.assertFalse(actual.matches("xP:2023/000123"));
        Assertions.assertFalse(actual.matches("123"));
    }

    @Test
    public void shouldCreatePatternWithByYearEnabled() {
        TemplatedReferenceNumberContextualProvider<Object> provider = create(
                "<CC>,<RR>",
                null,
                true,
                Stream.of()
        );

        PatternMatcher actual = provider.createPattern("LP-<RRRR>/000<CC>", new Object());
        Assertions.assertTrue(actual.matches("LP-2024/0001"));
        Assertions.assertTrue(actual.matches("LP-2024/000123"));
        Assertions.assertFalse(actual.matches("LP-2023/000123"));
        Assertions.assertFalse(actual.matches("LP-2025/000123"));
        Assertions.assertFalse(actual.matches("LP-2023/00123"));
        Assertions.assertFalse(actual.matches("LP:2023/000123"));
        Assertions.assertFalse(actual.matches("xP:2023/000123"));
        Assertions.assertFalse(actual.matches("123"));
    }

    private static @NonNull TemplatedReferenceNumberContextualProvider<Object> create(String commonTemplate, @Nullable String specificTemplate, boolean referenceNumbersByYearEnabled, Stream<String> potentialValuesStream) {
        return new TemplatedReferenceNumberContextualProvider<>(
                ContextIgnoringContextualProvider.of(commonTemplate),
                specificTemplate == null ? ContextIgnoringContextualProvider.ofNull() : ContextIgnoringContextualProvider.of(specificTemplate),
                ContextIgnoringContextualProvider.of(referenceNumbersByYearEnabled),
                StaticProvider.of(2024),
                (prefixToSearch, ctx) -> potentialValuesStream
        );
    }
}