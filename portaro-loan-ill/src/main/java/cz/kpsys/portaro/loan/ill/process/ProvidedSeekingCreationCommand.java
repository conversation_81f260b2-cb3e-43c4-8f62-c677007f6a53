package cz.kpsys.portaro.loan.ill.process;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.loan.ill.SeekingCommand;
import cz.kpsys.portaro.loan.ill.SeekingProvisionDeliveryChannel;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Min;
import lombok.NonNull;
import lombok.With;

import java.time.Instant;

@With
public record ProvidedSeekingCreationCommand(

        @NonNull
        Department department,

        @Nullable
        BasicUser requester,

        @NonNull
        User seeker,

        @NonNull
        Record document,

        @NullableNotBlank
        String volume,

        @NullableNotBlank
        String page,

        @NullableNotBlank
        String article,

        @NonNull
        Boolean eventuallyPhotocopyable,

        @NonNull
        Boolean eventuallyReservable,

        @NonNull
        Boolean eventuallyOnSiteLendable,

        @NullableNotBlank
        String note,

        @Nullable
        @Min(0)
        Price price,

        @NullableNotBlank
        String seekerReferenceId,

        @NonNull
        BasicUser provider,

        @NonNull
        SeekingProvisionDeliveryChannel deliveryChannel,

        @Nullable
        Instant requesterObtainDeadlineDate,

        @NullableNotBlank
        String providerReferenceId,

        @NonNull
        Instant date

) implements SeekingCommand<ProvidedSeekingCreationCommand> {}
