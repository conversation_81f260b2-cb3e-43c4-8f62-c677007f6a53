package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.scan.Scannable;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.loan.Loan;
import cz.kpsys.portaro.loan.ill.*;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Objects;

import static cz.kpsys.portaro.commons.util.ObjectUtil.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IllSeekingSaver implements Saver<Seeking, Seeking> {

    @NonNull Saver<IllSeekingProvisionList, IllSeekingProvisionList> seekingProvisionListSaver;
    @NonNull Saver<MvsEntity, MvsEntity> mvsEntitySaver;

    @Override
    public @NonNull Seeking save(@NonNull Seeking seeking) {
        SeekingProvision activeProvision = seeking.getActiveProvision();

        boolean providersView = seeking.isProvidersView();
        boolean seekersView = seeking.isSeekersView();
        if (!seekersView) {
            Assert.state(seeking.hasActiveProvision(), "We must always have active seeking provision for provider's seeking view");
            activeProvision = seeking.getExistingActiveProvision();
        }

        mvsEntitySaver.save(new MvsEntity(
                Objects.requireNonNull(seeking.getRealizationId()),
                providersView,
                seeking.getDesiredExemplar().document().getKindedId(),
                seeking.getDesiredExemplar().document().getId(),
                seeking.getDesiredExemplar().volume(),
                seeking.getDesiredExemplar().page(),
                seeking.getDesiredExemplar().article(),
                seekersView
                        ? seeking.knownDepartment().getId()
                        : activeProvision.knownDepartment().getId(),
                mapSeekingStateToIllState(seeking.getCancelled(), seeking.state(), providersView).getId(),
                getIdOrNull(seeking.getRequester()),
                elvis(activeProvision, SeekingProvision::id),
                seekersView
                        ? elvis(activeProvision, seekingProvision -> seekingProvision.knownProvider().getId())
                        : seeking.knownSeeker().getId(),
                firstNotNull(elvis(activeProvision, seekingProvision -> seekingProvision.deliveryChannel().getId()), SeekingProvisionDeliveryChannel.DEFAULT_PROVIDER.get().getId()),
                seekersView
                        ? elvis(seeking, Seeking::getLoan, Loan::getLoanRealizationId)
                        : elvis(activeProvision.loan(), Loan::getLoanRealizationId),
                seekersView
                        ? seeking.getSeekerReferenceId()
                        : activeProvision.providerReferenceId(),
                seekersView
                        ? elvis(activeProvision, SeekingProvision::providerReferenceId)
                        : seeking.getSeekerReferenceId(),
                elvis(activeProvision, SeekingProvision::exemplarIdentifiers, exemplarIdentifiers -> exemplarIdentifiers.stream().findFirst().map(Scannable::value).orElseThrow()),
                elvis(activeProvision, SeekingProvision::exemplarSignature),
                seeking.getRequesterObtainDeadlineDate(),
                seeking.getNote(),
                seeking.isEventuallyPhotocopyable(),
                seeking.isEventuallyReservable(),
                seeking.isEventuallyOnSiteLendable(),
                seeking.isEventuallyAbroadDeliverable(),
                seeking.getCreateDate(),
                seeking.getCommenceDate(),
                seekersView
                        ? elvis(activeProvision, SeekingProvision::seekerActivateDate)
                        : activeProvision.providerReserveDate(),
                elvis(activeProvision, SeekingProvision::providerAcceptDate),
                elvis(activeProvision, SeekingProvision::providerAcceptCondition),
                elvis(activeProvision, SeekingProvision::seekerAcceptDate),
                elvis(activeProvision, SeekingProvision::providerSendDate),
                seekersView
                        ? elvis(activeProvision, SeekingProvision::seekerReceiveDate)
                        : activeProvision.providerSendDate(),
                seekersView
                        ? elvis(activeProvision, SeekingProvision::seekerSendDate)
                        : activeProvision.providerReceiveDate(),
                elvis(activeProvision, SeekingProvision::seekerSendDate),
                elvis(activeProvision, SeekingProvision::providerReceiveDate),
                elvis(activeProvision, SeekingProvision::seekerCancelDate),
                elvis(activeProvision, SeekingProvision::providerCancelDate),
                firstNotNull(elvis(activeProvision, SeekingProvision::price, Price::amount), BigDecimal.ZERO),
                seeking.getZiskejTicketId(),
                getProvisionSyncId(activeProvision)
        ));

        seekingProvisionListSaver.save(seeking.getProvisionList());

        return seeking;
    }

    @Nullable
    private static String getProvisionSyncId(@Nullable SeekingProvision activeProvision) {
        if (activeProvision == null) {
            return null;
        }
        if (activeProvision.ziskejSubticketSyncId() != null) {
            return activeProvision.ziskejSubticketSyncId();
        }
        return null;
    }

    @NonNull
    public static MvsState mapSeekingStateToIllState(@NonNull Boolean cancelled, @NonNull SeekingState state, boolean providersView) {
        if (cancelled) {
            return MvsState.CANCELLED;
        }
        return switch (state) {
            case REQUESTED -> MvsState.REQUEST;
            case COMMENCED -> MvsState.REQUEST_ACCEPTED;
            case ACTIVATED -> {
                if (providersView) {
                    // for active mvs verbis needs state not to be ORDERED
                    yield MvsState.REQUEST_ACCEPTED;
                }
                yield MvsState.ORDERED;
            }
            case ACCEPTED_BY_PROVIDER -> MvsState.ORDERED;
            case SENT_BY_PROVIDER -> {
                if (providersView) {
                    yield MvsState.DELIVERED;
                }
                yield MvsState.ORDERED;
            }
            case RECEIVED_PROM_PROVIDER, NOT_PICKED_BY_REQUESTER, LENT, RETURNED_BY_REQUESTER -> MvsState.DELIVERED;
            case SENT_BACK_TO_PROVIDER, RECEIVED_BACK_BY_PROVIDER -> MvsState.RETURNED;
        };
    }

}
