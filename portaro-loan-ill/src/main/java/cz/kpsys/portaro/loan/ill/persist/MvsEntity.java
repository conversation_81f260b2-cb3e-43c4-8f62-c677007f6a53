package cz.kpsys.portaro.loan.ill.persist;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.exemplar.Exemplar;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.LoanDb.MVS.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@With
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class MvsEntity implements Identified<Integer> {

    /**
     * No @GeneratedValue - create id explicitly always (by ContextualProvider idGenerator)
     */
    @Id
    @Column(name = ID_MVS)
    @EqualsAndHashCode.Include
    @NonNull
    Integer id;

    @Column(name = JE_AKTIV)
    @NonNull
    Boolean active;

    @Column(name = FK_ZAZ)
    @NonNull
    Integer documentKindedId;

    /**
     * Temporary nullable - not yet filled by all clients
     */
    @Column(name = RECORD_ID)
    @Nullable
    UUID recordId;

    @Column(name = ROCNIK)
    @Nullable
    String volume;

    @Column(name = STRANKA)
    @Nullable
    String page;

    @Column(name = CLANEK)
    @Nullable
    String article;

    @Column(name = FK_PUJC)
    @NonNull
    Integer departmentId;

    @Column(name = FK_STAVMVS)
    @NonNull
    Integer stateId;

    @Column(name = FK_UZIV_CTEN)
    @Nullable
    Integer requesterUserId;

    @Column(name = PROVISION_ID)
    @Nullable
    UUID provisionId;

    @Column(name = FK_UZIV_KNIH)
    @Nullable
    Integer seekedActiveProvisionProviderIdOrProvidedSeekerUserId;

    @Column(name = PROV_DELIVERY_CHANNEL)
    @NonNull
    @Size(min = 1, max = 20)
    @NotBlank
    String provisionDeliveryChannel;

    @Column(name = FK_VYPUC)
    @Nullable
    Integer loanId;

    @Column(name = MY_REFERENCE_ID)
    @Nullable
    String seekingSeekerReferenceIdOrProvisionProviderReferenceId;

    @Column(name = COUNTERPARTY_REFERENCE_ID)
    @Nullable
    String seekingProvisionProviderReferenceIdOrProvisionSeekerReferenceId;

    @Column(name = EXEMPLAR_IDENTIFIER)
    @Nullable
    String exemplarIdentifier;

    @Column(name = EXEMPLAR_SIGNATURE)
    @Size(min = 1, max = Exemplar.SIGNATURE_MAX_LENGTH)
    @Nullable
    String exemplarSignature;

    @Column(name = DAT_ZAJEMDO)
    @Nullable
    Instant requesterObtainDeadlineDate;

    @Column(name = POZNAMKA)
    @Nullable
    String note;

    @Column(name = LZE_FOTOKOPIE)
    @NonNull
    Boolean eventuallyPhotocopyable;

    @Column(name = LZE_REZERVACE)
    @NonNull
    Boolean eventuallyReservable;

    @Column(name = LZE_PREZENCNI)
    @NonNull
    Boolean eventuallyOnSiteLendable;

    @Column(name = LZE_ZAHRANICNI)
    @NonNull
    Boolean eventuallyAbroadDeliverable;

    @Column(name = REQUEST_CREATE_DATE)
    @Nullable
    Instant requestCreateDate;

    @Column(name = DAT_POZ)
    @Nullable
    Instant seekingCommenceDateOrProvisionCreateDate;

    @Column(name = DAT_OBJ)
    @Nullable
    Instant seekingProvisionSeekerActivateDateOrProvisionReservationDate;

    @Column(name = PROV_PROVIDER_ACCEPT_DATE)
    @Nullable
    Instant provisionProviderAcceptDate;

    @Column(name = PROV_PROVIDER_ACCEPT_COND)
    @NullableNotBlank
    String provisionProviderAcceptCondition;

    @Column(name = PROV_SEEKER_ACCEPT_DATE)
    @Nullable
    Instant provisionSeekerAcceptDate;

    @Column(name = PROV_PROVIDER_SEND_DATE)
    @Nullable
    Instant provisionProviderSendDate;

    @Column(name = DAT_VYR)
    @Nullable
    Instant seekingProvisionSeekerReceiveDateOrProvisionProviderSendDate;

    @Column(name = DAT_ODE)
    @Nullable
    Instant seekingProvisionSeekerSendDateOrProvisionProviderReceiveDate;

    @Column(name = PROV_SEEKER_SEND_DATE)
    @Nullable
    Instant provisionSeekerSendDate;

    @Column(name = PROV_PROVIDER_RECEIVE_DATE)
    @Nullable
    Instant provisionProviderReceiveDate;

    @Column(name = PROV_SEEKER_CANCEL_DATE)
    @Nullable
    Instant provisionSeekerCancelDate;

    @Column(name = PROV_PROVIDER_CANCEL_DATE)
    @Nullable
    Instant provisionProviderCancelDate;

    @Column(name = CENA)
    @NonNull
    BigDecimal price;

    @Column(name = SYNC_ID)
    @Nullable
    String syncId;

    @Column(name = PROV_SYNC_ID)
    @Nullable
    String provisionSyncId;

}
