dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.apache.commons:commons-lang3:3.+")
    testImplementation("org.mockito:mockito-core:+")
    testImplementation(testFixtures(project(":portaro-record")))

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    api(project(":portaro-commons"))
    api(project(":portaro-file"))
    api(project(":portaro-record"))
    api(project(":portaro-web"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-commons-image"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-user-impl"))

    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:3.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("com.fasterxml.jackson.core:jackson-databind:2.+")
    implementation("com.github.kagkarlsson:db-scheduler-spring-boot-starter:+")
    implementation("org.slf4j:slf4j-api:+")
}
