package cz.kpsys.portaro.ext.obalkyknih;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.ext.obalkyknih.web.ObalkyknihRequestParamFactory;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.TestData;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Tag("ci")
@Tag("unit")
class ObalkyknihRequestParamFactoryTest {

    private static final ObalkyknihRequestParamFactory paramFactory = new ObalkyknihRequestParamFactory(() -> List.of("PAD001"));
    private static final Record RECORD = TestData.createFullRecord();
    private static final Record EMPTY = TestData.createEmptyRecord();

    @Test
    void testMultiParams() throws JsonProcessingException {
        var params = paramFactory.getQueryParams(RECORD);
        String paramQuery = paramFactory.metadataQueryParam(params);
        System.out.println(paramQuery);

        Assertions.assertTrue(paramQuery.startsWith("[{"));
        Assertions.assertTrue(paramQuery.endsWith("}]"));

        ObjectMapper objectMapper = new ObjectMapper();
        var listOfMaps = objectMapper.readValue(paramQuery, new TypeReference<List<Map<String, List<String>>>>() {});
        assertEquals(1, listOfMaps.size());
        var map = listOfMaps.getFirst();
        // Otestujeme i zachování pořadí hodnot v rámci sekcí
        assertEquals("808595530X", map.get("isbn").get(0));
        assertEquals("8085955253", map.get("isbn").get(1));
        assertEquals("0046225X", map.get("isbn").get(2)); // ISSN

        assertEquals(2, map.get("ean").size());
        assertEquals("9780201379624", map.get("ean").get(0)); // z 024 má prioritu
        assertEquals("978020137962", map.get("ean").get(1));
        // urn:nbn:cz:aba012-00009y should be ignored because it is URN

        assertEquals("(OCoLC)51184532", map.get("oclc").get(0));

        assertEquals("cnb000085030", map.get("nbn").get(0));
        assertEquals("pad001-kpm01304368", map.get("nbn").get(1));
    }

    @Test
    void testMultiParamExpansion() {
        var params = paramFactory.getQueryParams(RECORD);
        var singleParams = params.expand();

        assertEquals(8, singleParams.size());
        // Test alespoň jednoho prvku
        int[] isbnIndices = IntStream.range(0, singleParams.size())
                .filter(i -> singleParams.get(i).queryData().containsKey("isbn"))
                .toArray();
        assertEquals(3, isbnIndices.length);
        assertEquals("808595530X", singleParams.get(isbnIndices[0]).params().get("isbn"));
        assertEquals("8085955253", singleParams.get(isbnIndices[1]).params().get("isbn"));
        assertEquals("0046225X", singleParams.get(isbnIndices[2]).params().get("isbn"));
    }

    @Test
    void testEmptyMultiParams() {
        var params = paramFactory.getQueryParams(EMPTY);
        System.out.println(params);
        assertTrue(params.isEmpty());
        String paramQuery = paramFactory.metadataQueryParam(params);
        assertEquals("[{}]", paramQuery);
    }

}