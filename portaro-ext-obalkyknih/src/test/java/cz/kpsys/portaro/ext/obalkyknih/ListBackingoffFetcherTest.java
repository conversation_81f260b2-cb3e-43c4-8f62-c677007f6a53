package cz.kpsys.portaro.ext.obalkyknih;

import lombok.*;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.Instant;
import java.time.InstantSource;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ListBackingoffFetcherTest {

    @Test
    public void testBackingoff() {
        Instant start = Instant.EPOCH;
        SettableInstanceSource timeSrc = new SettableInstanceSource(start);
        List<String> list = new ArrayList<>();
        list.add("ok");
        list.add("ok2");

        ListBackingoffFetcher<List<String>> fetcher = new ListBackingoffFetcher<>(timeSrc,
                Duration.ofMinutes(5), () -> list);

        // Získání dat OK
        var ret = fetcher.tryFetch();
        assertIterableEquals(List.of("ok", "ok2"), ret.orElseThrow());
        assertTrue(fetcher.isReady());

        // Načtená data jsou prázdná -> spuštěn backoff
        list.clear();
        ret = fetcher.tryFetch();
        assertTrue(ret.isEmpty());
        assertFalse(fetcher.isReady());

        // Mezitím přišla nová data, ale stále čekáme, protože nevypršel interval
        list.add("ok");
        list.add("ok2");
        timeSrc.setNow(start.plus(5, ChronoUnit.MINUTES));
        ret = fetcher.tryFetch();
        assertTrue(ret.isEmpty());
        assertFalse(fetcher.isReady());

        // Vypršel čekací interval, vyzvednou se nová data
        timeSrc.setNow(start.plus(5, ChronoUnit.MINUTES).plus(1, ChronoUnit.SECONDS));
        ret = fetcher.tryFetch();
        assertIterableEquals(List.of("ok", "ok2"), ret.orElseThrow());
        assertTrue(fetcher.isReady());
    }

    @Test
    public void testCountLimitedBackingoff() {
        Instant start = Instant.EPOCH;
        SettableInstanceSource timeSrc = new SettableInstanceSource(start);
        List<String> list = new ArrayList<>();
        list.add("ok");
        list.add("ok2");

        ListBackingoffFetcher<List<String>> fetcher = new ListBackingoffFetcher<>(timeSrc,
                Duration.ofMinutes(2), () -> list)
                .withBackoffAfterCountProcessed(1);

        // Získání dat OK, přepálen limit, spuštěn backoff
        var ret = fetcher.tryFetch();
        assertIterableEquals(List.of("ok", "ok2"), ret.orElseThrow());
        assertFalse(fetcher.isReady());

        // Přidáme další prvek, ale nenačte se nic, protože se nečekalo dostatečně dlouho
        list.add("ok3");
        ret = fetcher.tryFetch();
        assertTrue(ret.isEmpty());
        assertFalse(fetcher.isReady());

        // Mezitím přišla nová data, ale stále čekáme, protože nevypršel interval
        timeSrc.setNow(start.plus(2, ChronoUnit.MINUTES));
        ret = fetcher.tryFetch();
        assertTrue(ret.isEmpty());
        assertFalse(fetcher.isReady());

        // Vypršel čekací interval, vyzvednou se nová data
        timeSrc.setNow(start.plus(2, ChronoUnit.MINUTES).plus(1, ChronoUnit.SECONDS));
        ret = fetcher.tryFetch();
        assertIterableEquals(List.of("ok", "ok2", "ok3"), ret.orElseThrow());
        assertFalse(fetcher.isReady());
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    private static class SettableInstanceSource implements InstantSource {

        @NonNull
        Instant now = Instant.MIN;

        @Override
        public Instant instant() {
            return now;
        }
    }

}