package cz.kpsys.portaro.ext.obalkyknih.metadata;

import cz.kpsys.portaro.commons.io.*;
import cz.kpsys.portaro.commons.webclient.ExternalDownloader;
import cz.kpsys.portaro.commons.webclient.FailoveredExternalDownloader;
import cz.kpsys.portaro.ext.obalkyknih.TestMetadata;
import cz.kpsys.portaro.ext.obalkyknih.web.ObalkyknihRequestParamFactory;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.TestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.time.Duration;
import java.util.List;

import static cz.kpsys.portaro.ext.obalkyknih.ObalkyknihConfig.OBALKYKNIH_PATTERN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;

class ObalkyknihDocumentMetadataLoaderTest {

    private Failoverer failoverer;

    @BeforeEach
    void setUp() {
        failoverer = new ExternalServiceFailoverer(
                OBALKYKNIH_PATTERN,
                List.of(
                        new Failoverer.SourceSpec("http://cache.obalkyknih.cz", Duration.ofHours(1)),
                        new Failoverer.SourceSpec("http://cache2.obalkyknih.cz", Duration.ZERO)));
    }

    @Test
    void testHappyScenario() throws ExternalServiceException {
        ExternalDownloader happyDownloader = Mockito.mock(ExternalDownloader.class);
        Mockito.when(happyDownloader.download(anyString(), any())).thenReturn(array(TestMetadata.createFullMetadata()));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(happyDownloader);

        Record doc = TestData.createFullRecord();
        DocumentMetadata metadata = loader.download(doc);
        assertEquals("110122950", metadata.bookId());
    }

    @Test
    void testServerFailure() throws ExternalServiceException {
        ExternalDownloader grumpyDownloader = Mockito.mock(ExternalDownloader.class);
        Mockito.when(grumpyDownloader.download(anyString(), any())).thenThrow(new ExternalServiceAccessException("Server down!"));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(grumpyDownloader);

        Record doc = TestData.createFullRecord();
        assertThrows(ExternalServiceAccessException.class, () -> loader.download(doc));
    }

    @Test
    void testDataNotIndexed() throws ExternalServiceException {
        ExternalDownloader grumpyDownloader = Mockito.mock(ExternalDownloader.class);
        Mockito.when(grumpyDownloader.download(anyString(), any())).thenReturn(array(TestMetadata.createNotIndexedMetadata()));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(grumpyDownloader);

        Record doc = TestData.createFullRecord();
        assertThrows(NoDataException.class, () -> loader.download(doc));
    }

    @Test
    void testBadRequestFailure() throws ExternalServiceException { // If we receive bad request response, we fail instantly
        ExternalDownloader downloader = Mockito.mock(ExternalDownloader.class);
        Mockito.when(downloader.download(contains("cache.obalkyknih.cz"), any())).thenThrow(new ExternalServiceResponseException("Bad request"));
        Mockito.when(downloader.download(contains("cache2.obalkyknih.cz"), any())).thenReturn(array(TestMetadata.createFullMetadata()));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(downloader);

        Record doc = TestData.createFullRecord();
        assertThrows(ExternalServiceResponseException.class, () -> loader.download(doc));
    }

    @Test
    void testComplicatedCase() throws ExternalServiceException {
        ExternalDownloader downloader = Mockito.mock(ExternalDownloader.class);
        // Main mirror fails
        Mockito.when(downloader.download(contains("cache.obalkyknih.cz"), any())).thenThrow(new ExternalServiceAccessException("Cannot connect!"));
        // Secondary succeeds
        Mockito.when(downloader.download(contains("cache2.obalkyknih.cz"), any())).thenReturn(array(TestMetadata.createFullMetadata()));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(downloader);

        Record doc = TestData.createFullRecord();
        DocumentMetadata metadata = loader.download(doc);
        assertEquals("110122950", metadata.bookId());
    }

    @Test
    void testOneByOne() throws ExternalServiceException {
        ExternalDownloader downloader = Mockito.mock(ExternalDownloader.class);
        // Main mirror fails
        Mockito.when(downloader.download(contains("cache.obalkyknih.cz"), any())).thenThrow(new ExternalServiceAccessException("Cannot connect!"));
        // http://cache2.obalkyknih.cz/api/books?multi=%5B%7B%22ean%22:%5B%229780201379624%22,%22978020137962%22%5D,%22isbn%22:%5B%22808595530X%22,%228085955253%22,%220046225X%22%5D,%22oclc%22:%5B%22(OCoLC)51184532%22%5D,%22nbn%22:%5B%22cnb000085030%22,%22pad001-kpm01304368%22%5D%7D%5D
        // Multidotaz by měl vrátit neindexovanou odpověď
        Mockito.when(downloader.download(matches("cache2.obalkyknih.cz.*?multi="), any())).thenReturn(array(TestMetadata.createNotIndexedMetadata()));
        // http://cache2.obalkyknih.cz/api/books?multi=%5B%7B%22ean%22:%229780201379624%22%7D%5D
        // Po jednom prvku by se měla vrátit normální odpověď
        Mockito.when(downloader.download(matches("cache2.obalkyknih.cz.*?multi=%5B%7B%22.*?%22:%22.*?%22%7D%5D"), any())).thenReturn(array(TestMetadata.createFullMetadata()));
        ObalkyknihDocumentMetadataLoader loader = setUpLoader(downloader);

        Record doc = TestData.createFullRecord();
        DocumentMetadata metadata = loader.download(doc);
        assertEquals("110122950", metadata.bookId());
    }

    private static DocumentMetadata[] array(DocumentMetadata documentMetadata) {
        return new DocumentMetadata[] { documentMetadata };
    }

    private ObalkyknihDocumentMetadataLoader setUpLoader(ExternalDownloader downloader) {
        return new ObalkyknihDocumentMetadataLoader(
                new FailoveredExternalDownloader(downloader, failoverer),
                new ObalkyknihRequestParamFactory(() -> List.of("PAD001")));
    }

}