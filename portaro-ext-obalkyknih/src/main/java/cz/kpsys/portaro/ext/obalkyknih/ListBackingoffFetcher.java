package cz.kpsys.portaro.ext.obalkyknih;

import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.time.Duration;
import java.time.InstantSource;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

@FieldDefaults(level = AccessLevel.PRIVATE)
public class ListBackingoffFetcher<T extends List<?>> extends BackingoffFetcher<T> {

    @Nullable Integer backoffAfterCountProcessed = null;

    int countProcessed;

    public ListBackingoffFetcher(@NonNull InstantSource instantSource,
                                 @NonNull Duration backoffPeriodOnEmptyData,
                                 @NonNull Supplier<T> fetcher) {
        super(instantSource, backoffPeriodOnEmptyData, fetcher, List::isEmpty);
    }

    /// Create new backing off fetcher.
    ///
    /// @param backoffPeriodOnEmptyData how long to wait (backoff period) before trying to load new data after
    ///                                 all data were processed. It is used so data source is not under
    ///                                 unnecessary load when there is probably nothing new.
    /// @param fetcher data fetch function
    public ListBackingoffFetcher(@NonNull Duration backoffPeriodOnEmptyData,
                                 @NonNull Supplier<T> fetcher) {
        super(backoffPeriodOnEmptyData, fetcher, List::isEmpty);
    }

    /// Force backoff period after loading {@code backoffAfterCountProcessed} many items.
    ///
    /// @param backoffAfterCountProcessed how many items can be loaded before activating backoff
    /// @return fetcher that backs off after downloading required count of items.
    public ListBackingoffFetcher<T> withBackoffAfterCountProcessed(@Nullable Integer backoffAfterCountProcessed) {
        this.backoffAfterCountProcessed = backoffAfterCountProcessed;
        return this;
    }

    @Override
    public Optional<T> tryFetch() {
        if (! isReady()) {
            countProcessed = 0;
            return Optional.empty();
        }

        var result = super.tryFetch();
        // Teoreticky může countProcessed přetéct, ale asi nemá cenu to tu řešit v našem případě
        result.ifPresent(list -> countProcessed += list.size());

        if (backoffAfterCountProcessed != null && countProcessed > backoffAfterCountProcessed) {
            countProcessed = 0;
            backoff();
        }

        return result;
    }
}
