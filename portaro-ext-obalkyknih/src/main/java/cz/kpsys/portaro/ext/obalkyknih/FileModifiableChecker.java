package cz.kpsys.portaro.ext.obalkyknih;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.file.IdentifiedFile;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FileModifiableChecker {

    @NonNull Provider<@NonNull Integer> portaroUserIdProvider;
    @NonNull Provider<@NonNull Integer> appserverUserIdProvider;
    @NonNull Provider<@NonNull Integer> suUserIdProvider;

    public boolean isFileProgrammaticallyModifiable(@NonNull IdentifiedFile file) {
        return file.getService() != null || legacyIsFileModifiable(file);
    }

    private boolean legacyIsFileModifiable(IdentifiedFile file) {
        return lastChangeByApp(file) && (
                sourceFromObalkyknih(file) || linkFromObalkyknih(file)
                        || sourceFromGoogleBooks(file) || sourceFromOpenLibrary(file)
        );
    }

    private boolean linkFromObalkyknih(IdentifiedFile file) {
        if (file.getLink() == null) {
            return false;
        }
        return ObalkyknihConfig.OBALKYKNIH_PATTERN.matcher(file.getLink().getString()).matches();
    }

    private boolean sourceFromObalkyknih(IdentifiedFile file) {
        if (file.getSource() == null) {
            return false;
        }
        return file.getSource().contains("obalkyknih.cz");
    }

    private boolean sourceFromGoogleBooks(IdentifiedFile file) {
        if (file.getSource() == null) {
            return false;
        }
        return file.getSource().contains("books.google.com");
    }

    private boolean sourceFromOpenLibrary(IdentifiedFile file) {
        if (file.getSource() == null) {
            return false;
        }
        return file.getSource().contains("covers.openlibrary.org");
    }

    private boolean lastChangeByApp(IdentifiedFile file) {
        var lastModifierId = file.getLastModifierUserId();
        return lastModifierId == null
                || portaroUserIdProvider.get().equals(lastModifierId)
                || appserverUserIdProvider.get().equals(lastModifierId)
                || suUserIdProvider.get().equals(lastModifierId);
    }

}
