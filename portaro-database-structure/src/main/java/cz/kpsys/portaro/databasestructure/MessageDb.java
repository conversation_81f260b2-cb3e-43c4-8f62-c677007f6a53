package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class MessageDb {

    public static final class THREAD_PARTICIPANT {
        public static final String TABLE = "thread_participant";
        public static final String ID = "id";
        public static final String THREAD_ID = "thread_id";
        public static final String PARTICIPANT_ID = "participant_id";
        public static final String TYPE = "type";
        public static final String CREATE_DATE = "create_date";
        public static final String FIRST_UNREAD_MESSAGE_ID = "first_unread_message_id";
        public static final String MENTION_MESSAGE_ID = "mention_message_id";
        public static final String ADMINISTRATOR = "administrator";
    }

    public static final class MESSAGE {
        public static final String TABLE = "message";
        public static final String ID = "id";
        public static final String DEPARTMENT_ID = "department_id";
        public static final String MESSAGE_TOPIC_ID = "message_topic_id";
        public static final String MESSAGE_SEVERITY_ID = "message_severity_id";
        public static final String SENDER_USER_ID = "sender_user_id";
        public static final String TARGET_USER_ID = "target_user_id";
        public static final String CREATION_EVENT_ID = "creation_event_id";
        public static final String CONFIRMATION_NECESSARY = "confirmation_necessary";
        public static final String THREAD_RECORD_ID = "thread_record_id";
        public static final String MESSAGE_CONTENT = "message_content";
        public static final String CONTENT_TYPE = "content_type";
        public static final String DIRECTORY_ID = "directory_id";
        public static final String CREATION_DATE = "creation_date";
        public static final String ACTIVATION_DATE = "activation_date";
    }

    public static final class MESSAGE_SENDING {
        public static final String TABLE = "message_sending";
        public static final String ID = "id";
        public static final String MESSAGE_ID = "message_id";
        public static final String MESSAGE_MEDIUM_ID = "message_medium_id";
        public static final String SENDING_EVENT_ID = "sending_event_id";
        public static final String RECEIPTION_EVENT_ID = "receiption_event_id";
        public static final String CONFIRMATION_EVENT_ID = "confirmation_event_id";
    }

    public static final class MESSAGE_SENDING_EMAIL {
        public static final String TABLE = "message_sending_email";
        public static final String ID = "id";
        public static final String MESSAGE_SENDING_ID = "message_sending_id";
        public static final String SUBJECT = "subject";
        public static final String BODY = "body";
        public static final String ATTACHMENT_DIRECTORY_ID = "attachment_directory_id";
        public static final String ERROR_MESSAGE = "error_message";
    }

    public static final class MESSAGE_SENDING_EMAIL_ADDRESS {
        public static final String TABLE = "message_sending_email_address";
        public static final String ID = "id";
        public static final String MESSAGE_SENDING_ID = "message_sending_id";
        public static final String EMAIL_ADDRESS = "email_address";
        public static final String ADDRESS_TYPE = "address_type";
    }

    public static final class MESSAGE_SENDING_INTERNAL {
        public static final String TABLE = "message_sending_internal";
        public static final String ID = "id";
        public static final String MESSAGE_SENDING_ID = "message_sending_id";
        public static final String SUBJECT = "subject";
        public static final String BODY = "body";
    }

    public static final class MESSAGE_SENDING_POST {
        public static final String TABLE = "message_sending_post";
        public static final String ID = "id";
        public static final String MESSAGE_SENDING_ID = "message_sending_id";
        public static final String ADDRESS = "address";
        public static final String FK_FULLTEXT_CONTENT = "fk_fulltext_content";
        public static final String PRINTING_EVENT_ID = "printing_event_id";
    }

    public static final class MESSAGE_SENDING_SMS {
        public static final String TABLE = "message_sending_sms";
        public static final String ID = "id";
        public static final String MESSAGE_SENDING_ID = "message_sending_id";
        public static final String RECIPIENT_PHONE_NUMBER = "recipient_phone_number";
        public static final String CONTENT = "content";
        public static final String ERROR_MESSAGE = "error_message";
    }
}
