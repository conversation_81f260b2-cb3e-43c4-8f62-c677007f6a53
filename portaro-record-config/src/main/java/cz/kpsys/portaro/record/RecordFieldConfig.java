package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.TransactionalAllValuesProvider;
import cz.kpsys.portaro.config.CodebookLoaderBuilderFactory;
import cz.kpsys.portaro.database.JpaAllValuesProvider;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.data.AuthorityFieldTypeEntity;
import cz.kpsys.portaro.record.detail.data.DocumentFieldTypeEntity;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.def.NoLinkDef;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValueConverterFactory;
import cz.kpsys.portaro.record.edit.*;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;

@Configuration
@Import({
        RecordCodebookConfig.class
})
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordFieldConfig {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull QueryFactory queryFactory;
    @NonNull EntityManager entityManager;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull DatatypedAcceptableValuesRegistry allowedDatatypeToAllValuesProviderMap;
    @NonNull ByIdLoadable<LabeledValuesGroup<LabeledIdentified<String>, String>, Object> defValAcceptableValuesGroupLoader;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull Map<FieldTypeId, BiFunction<Fond, FieldType, Optional<EditableFieldType>>> customEditableFieldTypes;
    @NonNull CacheService cacheService;


    @Bean
    public PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader fieldTypeIdByConfigPathLoader() {
        return new PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader(prefabFieldTypeLoader());
    }

    @Bean
    public RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver() {
        return new DefaultRecordEntryFieldTypeIdResolver(fieldTypeIdByConfigPathLoader());
    }

    @Bean
    public UnknownFieldTypeFactory unknownFieldTypeFactory() {
        return new UnknownFieldTypeFactory();
    }

    @Bean
    public FieldTypeLoader fieldTypeLoader() {
        PrefabsToFieldTypesConvertingLoader prefabToFieldTypesConverter = new PrefabsToFieldTypesConvertingLoader(
                prefabFieldTypeLoader(),
                recordEntryFieldTypeIdResolver(),
                fieldValueConverterFactory(),
                fieldAcceptableValuesResolver(),
                enabledLoadableFondsExpander,
                fondedFieldSettingLoader()
        );
        Codebook<FieldType, String> fieldTypeCachedCodebook = codebookLoaderBuilderFactory.create()
                .providedBy(prefabToFieldTypesConverter)
                .staticCached(FieldType.class.getSimpleName())
                .fallbackWhenNotFoundById(fieldTypeId -> unknownFieldTypeFactory().createUnknownDatafieldType(FieldTypeId.parse(fieldTypeId)))
                .build();
        return new CodebookDelegatingFieldTypeLoader(fieldTypeCachedCodebook, unknownFieldTypeFactory());
    }

    @Bean
    public Codebook<PrefabFieldType, FieldTypeId> prefabFieldTypeLoader() {
        var documentFieldTypeEntityLoader = new JpaAllValuesProvider<>(new SimpleJpaRepository<>(DocumentFieldTypeEntity.class, entityManager));
        var authorityFieldTypeEntityLoader = new JpaAllValuesProvider<>(new SimpleJpaRepository<>(AuthorityFieldTypeEntity.class, entityManager));

        AllValuesProvider<PrefabFieldType> documentPrefabFieldTypeLoader = ConvertingAllValuesProvider.byListConverter(documentFieldTypeEntityLoader, new FieldTypeEntityToPrefabFieldTypeConverter(fondLoader, rootSerialCodeProvider));
        AllValuesProvider<PrefabFieldType> authorityPrefabFieldTypeLoader = ConvertingAllValuesProvider.byListConverter(authorityFieldTypeEntityLoader, new FieldTypeEntityToPrefabFieldTypeConverter(fondLoader, rootSerialCodeProvider));
        AllValuesProvider<PrefabFieldType> indicatorPrefabFieldTypeLoader = ConvertingAllValuesProvider.byListConverter(indicatorTypeLoader(), new ListToModifiedListConverter<>(indicatorType ->
                new PrefabFieldType(
                        indicatorType.getId(),
                        indicatorType.getId(),
                        indicatorType.getName(),
                        false,
                        false,
                        false,
                        new NoLinkDef(),
                        FieldGeneration.PHYSICAL,
                        FieldSource.common(),
                        TransferType.OVERWRITE,
                        PrefabFieldType.NO_LINK_ROOT_FOND,
                        IndicatorType.createIndicatorDatatype(indicatorType.getId()),
                        FieldExportSetting.toField(indicatorType.getId())
                )
        ));
        AllValuesProvider<PrefabFieldType> staticFieldTypeLoader = StaticAllValuesProvider.of(
                FieldTypes.FOND_PREFAB_FIELD_TYPE,
                FieldTypes.USER_PREFAB_FIELD_TYPE,
                FieldTypes.DOCUMENT_LEADER_PREFAB_FIELD_TYPE,
                FieldTypes.AUTHORITY_LEADER_PREFAB_FIELD_TYPE,
                FieldTypes.TOC_PREFAB_FIELD_TYPE,
                FieldTypes.TOC_VALUE_PREFAB_FIELD_TYPE,
                FieldTypes.TOC_URL_PREFAB_FIELD_TYPE
        );

        var compositeLoader = CompositeAllValuesProvider.of(
                documentPrefabFieldTypeLoader,
                authorityPrefabFieldTypeLoader,
                indicatorPrefabFieldTypeLoader,
                staticFieldTypeLoader
        );

        return codebookLoaderBuilderFactory.create()
                .providedBy(compositeLoader)
                .staticCached(FieldType.class.getSimpleName())
                .build();
    }

    @Bean
    public Codebook<IndicatorType, FieldTypeId> indicatorTypeLoader() {
        AllValuesProvider<IndicatorType> documentIndicatorTypesProvider = new SpringDbIndicatorTypeLoader(notAutoCommittingJdbcTemplate, queryFactory, false, RecordDb.DEF_DOKINDIK.DEF_DOKINDIK);
        AllValuesProvider<IndicatorType> authorityIndicatorTypesProvider = new SpringDbIndicatorTypeLoader(notAutoCommittingJdbcTemplate, queryFactory, true, RecordDb.DEF_AUTINDIK.DEF_AUTINDIK);
        var compositeProvider = CompositeAllValuesProvider.of(documentIndicatorTypesProvider, authorityIndicatorTypesProvider);
        var transactionalProvider = new TransactionalAllValuesProvider<>(compositeProvider, readonlyTransactionTemplateFactory.get());
        var onlyMeaningfulProvider = new FilteredAllValuesProvider<>(transactionalProvider, IndicatorType::hasAnyMeaningfulValue);

        return codebookLoaderBuilderFactory.create()
                .providedBy(onlyMeaningfulProvider)
                .staticCached(IndicatorType.class.getSimpleName())
                .build();
    }

    @Bean
    public FieldValueConverterFactory fieldValueConverterFactory() {
        return new FieldValueConverterFactory(
                fieldAcceptableValuesResolver(),
                indicatorTypeLoader(),
                recordEntryFieldTypeIdResolver()
        );
    }

    @Bean
    public FieldAcceptableValuesResolver fieldAcceptableValuesResolver() {
        return new FieldAcceptableValuesResolver(
                allowedDatatypeToAllValuesProviderMap,
                defValAcceptableValuesGroupLoader
        );
    }

    @Bean
    public FieldTypesByFondLoader pureEditableFieldTypesByFondLoader() {
        SpringDbEditableFieldTypesByFondLoader bean = new SpringDbEditableFieldTypesByFondLoader(
                fondedFieldSettingLoader(),
                fieldTypeLoader()
        );
        cacheService.registerCleaner(FieldType.class.getSimpleName(), bean);
        return bean;
    }

    @Bean
    public FondedFieldSettingLoader fondedFieldSettingLoader() {
        HierarchicalFondedFieldSettingLoader bean = new HierarchicalFondedFieldSettingLoader(
                new SpringDbFondedFieldTypeEntityByFondLoader(
                        notAutoCommittingJdbcTemplate,
                        queryFactory,
                        readonlyTransactionTemplateFactory.get()
                ),
                fieldTypeIdByConfigPathLoader(),
                enabledFondInheritanceLoader,
                recordEntryFieldTypeIdResolver(),
                fondLoader
        );
        cacheService.registerCleaner(FieldType.class.getSimpleName(), bean);
        return bean;
    }

    @Bean
    public FieldTypesByFondLoader fieldTypesByFondLoader() {
        return new CustomAddingFieldTypesByFondLoader(pureEditableFieldTypesByFondLoader(), fieldTypeLoader(), customEditableFieldTypes);
    }

}
