package cz.kpsys.portaro.user;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;

import java.util.List;
import static cz.kpsys.portaro.databasestructure.UserDb.OSOBY;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbNetIdLoader implements AllValuesProvider<String> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<String> getAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(OSOBY.NET_ID);
        sq.from(OSOBY.TABLE);
        sq.where().isNotNull(OSOBY.NET_ID);
        sq.orderBy().addAsc(OSOBY.NET_ID);
        return jdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), String.class);
    }
}
