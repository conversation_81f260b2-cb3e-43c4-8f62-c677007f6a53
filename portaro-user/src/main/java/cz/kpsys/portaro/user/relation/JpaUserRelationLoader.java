package cz.kpsys.portaro.user.relation;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static cz.kpsys.portaro.commons.util.ListUtil.getByIdOrThrow;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableSet;
import static java.util.stream.Stream.concat;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class JpaUserRelationLoader implements UserRelationLoader, CacheCleaner {

    public static final String CACHE_NAME = "userRelations";

    @NonNull JpaRepository<UserRelationEntity, UserRelationId> jpa;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;


    @Cacheable(value = CACHE_NAME, key = "#user.id", sync = true, condition = "#user.evided")
    @Override
    public List<UserRelation> getAllByUser(@NonNull BasicUser user) {
        if (!user.isEvided()) {
            return List.of();
        }

        Stream<UserRelationEntity> allBySource = getAllBySource(user.getId());
        Stream<UserRelationEntity> allByTarget = getAllByTarget(user.getId());
        List<UserRelationEntity> allDtos = concat(allBySource, allByTarget).collect(toList());

        return mapDtosToModels(allDtos);
    }


    private Stream<UserRelationEntity> getAllBySource(Integer sourceUserId) {
        UserRelationEntity groupMemberProbe = new UserRelationEntity((long) sourceUserId, null, null);
        List<UserRelationEntity> groupMembers = jpa.findAll(Example.of(groupMemberProbe));
        return groupMembers.stream();
    }


    private Stream<UserRelationEntity> getAllByTarget(Integer targetUserId) {
        UserRelationEntity superiorProbe = new UserRelationEntity(null, (long) targetUserId, null);
        List<UserRelationEntity> superiors = jpa.findAll(Example.of(superiorProbe));
        return superiors.stream();
    }


    private List<UserRelation> mapDtosToModels(List<UserRelationEntity> dtos) {
        Set<Integer> allOccurredUserIds = dtos.stream()
                .flatMap(userRelationEntity -> Stream.of(userRelationEntity.getSourceUserId(), userRelationEntity.getTargetUserId()))
                .map(Long::intValue)
                .collect(toUnmodifiableSet()); //set to remove potential duplicities

        List<BasicUser> allOccurredUsers = basicUserLoader.getAllByIds(new ArrayList<>(allOccurredUserIds));

        return dtos.stream()
                .map(userRelationEntity -> {
                    BasicUser sourceUser = getByIdOrThrow(allOccurredUsers, userRelationEntity.getSourceUserId().intValue());
                    BasicUser targetUser = getByIdOrThrow(allOccurredUsers, userRelationEntity.getTargetUserId().intValue());
                    RelationType relationType = RelationType.CODEBOOK.getById(userRelationEntity.getRoleType());
                    return new UserRelation(sourceUser, relationType, targetUser);
                })
                .toList();
    }


    @CacheEvict(value = CACHE_NAME, allEntries = true)
    @Override
    public void clearCache() {
    }
}
