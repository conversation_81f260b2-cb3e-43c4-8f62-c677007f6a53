package cz.kpsys.portaro.databasebackup.testing;

import cz.kpsys.portaro.databasebackup.DbDumper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.io.input.NullInputStream;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class EmptyDataDbDumper implements DbDumper {

    public static EmptyDataDbDumper start() {
        return new EmptyDataDbDumper(new EmptyDataProcess());
    }


    @NonNull Process process;

    @Override
    public InputStream getBackupStream() {
        return new BufferedInputStream(process.getInputStream());
    }

    @Override
    public CompletableFuture<Process> onExit() {
        return process.onExit();
    }

    @Override
    public String getBackupFilename() {
        return "empty.dump";
    }

    @Override
    public void close() {
        // NOOP
    }


    private static class EmptyDataProcess extends TestProcess {

        protected EmptyDataProcess() {
            super(0);
        }

        @Override
        public InputStream getInputStream() {
            return new NullInputStream();
        }

    }

}
