package cz.kpsys.portaro.auth.saml2.idp.attributeprovider;

import cz.kpsys.portaro.auth.saml2.idp.attribute.AttributeValueProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.Person;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CnAttributeValueProvider implements AttributeValueProvider<String> {

    @Override
    public List<String> getValue(@NonNull String requesterEntityId, @NonNull Person activeUser, @NonNull Department ctx) {
        return List.of(getFirstName(activeUser) + " " + getLastName(activeUser));
    }

    @NonNull
    private String getFirstName(@NonNull Person activeUser) {
        return Objects.requireNonNull(activeUser.getFirstName(), "Person first name is not set");
    }

    @NonNull
    private String getLastName(@NonNull Person activeUser) {
        return Objects.requireNonNull(activeUser.getLastName(), "Person last name is not set");
    }

}
