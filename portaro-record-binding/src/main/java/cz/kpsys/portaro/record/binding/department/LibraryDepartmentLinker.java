package cz.kpsys.portaro.record.binding.department;

import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.user.edit.UserEditationFactory;
import cz.kpsys.portaro.user.edit.command.LibraryLinkToDepartmentCommand;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LibraryDepartmentLinker {

    @NonNull RecordEditationCreator<Department> departmentRecordEditationCreator;
    @NonNull UserEditationFactory userEditationFactory;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;

    public void link(LibraryLinkToDepartmentCommand command) {
        RecordEditation recordEditation;
        if (command.department().getRid() == null) {
            recordEditation = departmentRecordEditationCreator.ofNewRecord(command.department(), List.of(command.ctx()), command.ctx(), command.currentAuth());
        } else {
            recordEditation = departmentRecordEditationCreator.ofExistingRecord(command.department(), List.of(command.ctx()), command.ctx(), command.currentAuth());
        }

        if (recordEditation.isDraft()) {
            recordEditation.publish(command.ctx(), command.currentAuth());
        } else {
            recordEditation.saveIfModified(command.ctx(), command.currentAuth());
        }

        userEditationFactory.ofExistingUser(command.library(), command.ctx(), command.currentAuth())
                .map(userEditationCommand -> userEditationCommand.withRecordEditation(recordEditation))
                .saveIfModified();

        command.department().setRid(recordEditation.getRecord().getId());
        departmentSaver.save(command.department());
    }
}
