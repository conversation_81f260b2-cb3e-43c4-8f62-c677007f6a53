package cz.kpsys.portaro.record.binding.department;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordWellKnownFields;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DepartmentRecordEditationCreator implements RecordEditationCreator<Department> {

    @NonNull Provider<@NonNull Fond> departmentFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;

    public RecordEditation ofNewRecord(@NonNull Department department, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        Fond fond = departmentFondProvider.get();

        RecordEditation recordEditation;
        if (department.getRid() != null) {
            recordEditation = recordEditationFactory
                    .on(ctx, holdingDepartments)
                    .ofNew(department.getRid(), fond)
                    .notCreatingContextualHolding()
                    .build(currentAuth);
        } else {
            recordEditation = recordEditationFactory
                    .on(ctx, holdingDepartments)
                    .ofNew(fond)
                    .notCreatingContextualHolding()
                    .build(currentAuth);
        }

        recordEditationHelper.setStringSubfieldValue(department.getName(), true, RecordWellKnownFields.AuthorityCorporationName.TYPE_ID, true, RecordWellKnownFields.AuthorityCorporationName.Name.TYPE_ID, recordEditation);
        return recordEditation;
    }

    public RecordEditation ofExistingRecord(@NonNull Department department, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        Assert.state(department.getRid() != null, "Department must have a rid");
        return recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofExisting(recordLoader.getById(department.getRid()))
                .notCreatingContextualHolding()
                .build(currentAuth);
    }
}
