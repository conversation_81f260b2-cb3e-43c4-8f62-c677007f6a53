package cz.kpsys.portaro.record.binding;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.provider.SingleMatchingProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.mail.MailService;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.binding.department.DepartmentRecordEditationCreator;
import cz.kpsys.portaro.record.binding.department.LibraryDepartmentLinker;
import cz.kpsys.portaro.record.binding.user.AdhocUserRecordCreatorOrGetter;
import cz.kpsys.portaro.record.binding.user.NewDiscountRequestMailSender;
import cz.kpsys.portaro.record.binding.user.UserFilesSaver;
import cz.kpsys.portaro.record.binding.user.UserRecordEditationCreator;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondType;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserSettingKeys;
import cz.kpsys.portaro.user.UserStringGenerator;
import cz.kpsys.portaro.user.discount.UserDiscountApprovalModifier;
import cz.kpsys.portaro.user.edit.UserEditationFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.UUID;
import java.util.concurrent.ExecutorService;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@Configuration
public class RecordBindingConfig {

    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull UserEditationFactory userEditationFactory;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull UserStringGenerator prettyUserNameGenerator;
    @NonNull Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull ByIdLoadable<FileAccessType, Integer> fileAccessTypeLoader;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull ExecutorService executorService;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;
    @NonNull UserDiscountApprovalModifier userDiscountApprovalModifier;
    @NonNull TemplateEngine templateEngine;
    @NonNull MailService mailService;
    @NonNull SettingLoader settingLoader;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;

    @Bean
    public AdhocUserRecordCreatorOrGetter adhocUserRecordCreator() {
        return new AdhocUserRecordCreatorOrGetter(userRecordEditationCreator(), userEditationFactory);
    }

    @Bean
    public RecordEditationCreator<User> userRecordEditationCreator() {
        return new UserRecordEditationCreator(userPersonAuthorityFondProvider(), recordEditationFactory, recordEditationHelper, recordLoader, prettyUserNameGenerator);
    }

    @Bean
    public LibraryDepartmentLinker libraryDepartmentLinker() {
        return new LibraryDepartmentLinker(departmentRecordEditationCreator(), userEditationFactory, departmentSaver);
    }

    @Bean
    public RecordEditationCreator<Department> departmentRecordEditationCreator() {
        return new DepartmentRecordEditationCreator(departmentFondProvider(), recordEditationFactory, recordEditationHelper, recordLoader);
    }

    @Bean
    public Provider<@Nullable Fond> userPersonAuthorityFondProvider() {
        return SingleMatchingProvider.of(fondLoader, FondType.USER_PERSON::matches);
    }

    @Bean
    public Provider<@Nullable Fond> departmentFondProvider() {
        return SingleMatchingProvider.of(fondLoader, FondType.DEPARTMENT::matches);
    }


    @Bean
    public UserFilesSaver userFilesSaver() {
        return new UserFilesSaver(adhocUserRecordCreator(), recordWithAttachmentSaver, fileAccessTypeLoader, new SideThreadAuthenticationIsolator(authenticationHolder, portaroUserProvider, executorService), defaultTransactionTemplateFactory.get(), fileCategoryBySystemTypeLoader, userDiscountApprovalModifier, newDiscountRequestMailSender());
    }

    @Bean
    public NewDiscountRequestMailSender newDiscountRequestMailSender() {
        return new NewDiscountRequestMailSender(templateEngine, mailService, prettyUserNameGenerator, settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE), settingLoader.getDepartmentedProvider(UserSettingKeys.USER_NEW_DISCOUNT_REQUEST_NOTIFY_EMAIL));
    }
}
