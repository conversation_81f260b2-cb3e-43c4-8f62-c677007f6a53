package cz.kpsys.portaro.batch;

import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.Assert;

import java.util.function.Consumer;

import static cz.kpsys.portaro.commons.db.QueryUtils.MAX;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SyncSequenceTask implements Runnable {

    @NonNull JdbcTemplate jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull String sequenceName;
    @NonNull String tableName;
    @NonNull String tableSequenceIdColumn;
    @NonNull Consumer<String> logger;

    @Override
    public void run() {
        Integer newSequenceValue = newSequenceValue();
        jdbcTemplate.execute("alter sequence %s restart with %d".formatted(sequenceName, newSequenceValue));
        logger.accept("Sequence %s restarted with new value %d".formatted(sequenceName, newSequenceValue));
    }

    private Integer newSequenceValue() {
        SelectQuery maxTableIdQuery = queryFactory.newSelectQuery();
        maxTableIdQuery.from(tableName);
        maxTableIdQuery.select().add(MAX(tableSequenceIdColumn));
        int maxTableId = NumberUtil.zeroIfNull(jdbcTemplate.queryForObject(maxTableIdQuery.getSql(), Integer.class));

        SelectQuery lastSequenceValueQuery = queryFactory.newSelectQuery();
        lastSequenceValueQuery.from(sequenceName);
        lastSequenceValueQuery.select("last_value");
        Integer lastSequenceValue = jdbcTemplate.queryForObject(lastSequenceValueQuery.getSql(), Integer.class);
        Assert.state(lastSequenceValue != null, "Cannot query last value for sequence %s (%s)".formatted(sequenceName, maxTableIdQuery.getSql()));

        return Math.max(maxTableId, lastSequenceValue);
    }
}
