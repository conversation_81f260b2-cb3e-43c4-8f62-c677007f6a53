package cz.kpsys.portaro.form.valueeditor.record;

import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.searchoredit.StaticSearchParams;
import lombok.NonNull;
import lombok.With;

@With
public record RecordValueEditorOptions(

        @NonNull
        StaticSearchParams searchParams,

        @NonNull
        RecordCreateParams createParams,

        @NonNull
        RecordCreateParams editParams

) implements ValueEditorOptions {

    public static RecordValueEditorOptions getEmptyOptions() {
        return new RecordValueEditorOptions(StaticSearchParams.createEmpty(), RecordCreateParams.createEmpty(), RecordCreateParams.createEmpty());
    }
}
