package cz.kpsys.portaro.form.valueeditor.searchoredit;

import com.fasterxml.jackson.annotation.JsonInclude;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import lombok.NonNull;
import lombok.With;
import org.springframework.lang.Nullable;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@With
public record StaticSearchParams(

        @Nullable
        String type,

        @Nullable
        String kind,

        @Nullable
        String subkind,

        @Nullable
        List<? extends LabeledIdentified<Integer>> rootFond,

        @Nullable
        String datasource,

        @Nullable
        String datasourceGroup,

        @Nullable
        String fondType

) {

    public static StaticSearchParams createEmpty() {
        return new StaticSearchParams(null, null, null, null, null, null, null);
    }

    public static StaticSearchParams createOfKindAndType(@NonNull String kind, @NonNull String type) {
        return createEmpty().withKind(kind).withType(type);
    }

}
