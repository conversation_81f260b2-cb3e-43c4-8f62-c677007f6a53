package cz.kpsys.portaro.form.formfield;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.ValueEditorValidations;
import cz.kpsys.portaro.form.valueeditor.object.HintAwareObjectValueEditorField;
import cz.kpsys.portaro.form.valueeditor.object.ObjectValueEditorField;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.util.ArrayList;

public class FormFieldList extends ArrayList<FormField<?, ?>> {

    @JsonIgnore
    private final String labelsMessageCodePrefix;

    public FormFieldList() {
        this(null);
    }

    public FormFieldList(String objectName) {
        this.labelsMessageCodePrefix = objectName;
    }


    public <OPTIONS extends ValueEditorOptions, VALIDATIONS extends ValueEditorValidations, EDITOR extends ValueEditor<OPTIONS, VALIDATIONS, EDITOR>> FormFieldList add(String fieldName, EDITOR valueEditor) {
        Assert.notNull(labelsMessageCodePrefix, "Cannot call add without label directly when labelsMessageCodePrefix is not defined");

        return add(fieldName, Texts.ofObjectPropertyMessageCode(labelsMessageCodePrefix, fieldName), valueEditor);
    }

    public <OPTIONS extends ValueEditorOptions, VALIDATIONS extends ValueEditorValidations, EDITOR extends ValueEditor<OPTIONS, VALIDATIONS, EDITOR>> FormFieldList addIfNotContains(String fieldName, EDITOR valueEditor) {
        Assert.notNull(labelsMessageCodePrefix, "Cannot call add without label directly when labelsMessageCodePrefix is not defined");

        if (stream().noneMatch(editedProperty -> editedProperty.getFieldName().equals(fieldName))) {
            return add(fieldName, Texts.ofObjectPropertyMessageCode(labelsMessageCodePrefix, fieldName), valueEditor);
        }

        return this;
    }

    public <OPTIONS extends ValueEditorOptions, VALIDATIONS extends ValueEditorValidations, EDITOR extends ValueEditor<OPTIONS, VALIDATIONS, EDITOR>> FormFieldList add(String fieldName, Text label, EDITOR valueEditor) {
        add(new ObjectValueEditorField<>(label, fieldName, valueEditor));

        return this;
    }

    public <OPTIONS extends ValueEditorOptions, VALIDATIONS extends ValueEditorValidations, EDITOR extends ValueEditor<OPTIONS, VALIDATIONS, EDITOR>> FormFieldList add(String fieldName, Text label, @Nullable Text hint, EDITOR valueEditor) {
        add(new HintAwareObjectValueEditorField<>(label, fieldName, valueEditor, hint));

        return this;
    }

}
