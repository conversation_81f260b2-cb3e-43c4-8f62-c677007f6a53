package cz.kpsys.portaro.form.valueeditor.inlinerecordsearch;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import cz.kpsys.portaro.form.valueeditor.AnnotationsAwareValueEditorResolver;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.ValueEditorDescriptionResolver;
import cz.kpsys.portaro.form.valueeditor.searchoredit.StaticSearchParams;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.inlinerecordsearch.InlineRecordSearchEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.StaticSearchParameters;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

import static cz.kpsys.portaro.commons.util.StringUtil.notEmptyString;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class InlineRecordSearchValueEditorResolver implements AnnotationsAwareValueEditorResolver {

    @NonNull ValidationsResolver validationsResolver;

    @Override
    public Optional<? extends ValueEditor<?, ?, ?>> resolveByTypeAnnotation(Object formObject, String fieldName, Department currentDepartment) {
        return resolveProperty(formObject, fieldName, currentDepartment, true);
    }

    @Override
    public Optional<InlineRecordSearchValueEditor> resolve(Object formObject, String fieldName, Department currentDepartment) {
        return resolveProperty(formObject, fieldName, currentDepartment, false);
    }

    public Optional<InlineRecordSearchValueEditor> resolveProperty(Object formObject, String fieldName, Department currentDepartment, boolean searchInTypeAnnotation) {

        if (!AnnotationUtil.hasAnnotationIncludingTypeAnnotations(formObject, fieldName, InlineRecordSearchEditor.class, searchInTypeAnnotation)) {
            return Optional.empty();
        }

        Optional<InlineRecordSearchEditor> annotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, InlineRecordSearchEditor.class, searchInTypeAnnotation);

        var inlineRecordSearchValueEditorOptions = InlineRecordSearchValueEditorOptions.getEmptyOptions().withSearchParams(
                annotation
                        .map(InlineRecordSearchEditor::staticSearchParameters)
                        .map(this::createStaticSearchParams)
                        .orElse(null));

        Optional<EditorOptions> editorOptions = annotation.map(InlineRecordSearchEditor::editorOptions);



        InlineRecordSearchValueEditor inlineRecordSearchValueEditor = InlineRecordSearchValueEditor
                .getEmptyEditor()
                .withEditorName(fieldName)
                .withOptions(inlineRecordSearchValueEditorOptions);

        inlineRecordSearchValueEditor = validationsResolver.processCommonValueEditorValidations(formObject, fieldName, inlineRecordSearchValueEditor, searchInTypeAnnotation);

        inlineRecordSearchValueEditor = ValueEditor.fillValueEditorWithOptions(inlineRecordSearchValueEditor, editorOptions);

        inlineRecordSearchValueEditor = ValueEditorDescriptionResolver.processDescription(inlineRecordSearchValueEditor, formObject, fieldName, searchInTypeAnnotation);

        return Optional.of(inlineRecordSearchValueEditor);
    }

    private StaticSearchParams createStaticSearchParams(StaticSearchParameters paramsFromAnnotations) {
        return StaticSearchParams
                .createEmpty()
                .withKind(notEmptyString(paramsFromAnnotations.kind()))
                .withSubkind(notEmptyString(paramsFromAnnotations.subkind()))
                .withType(notEmptyString(paramsFromAnnotations.type()))
                .withDatasource(notEmptyString(paramsFromAnnotations.datasource()))
                .withDatasourceGroup(notEmptyString(paramsFromAnnotations.datasourceGroup()))
                .withFondType(notEmptyString(paramsFromAnnotations.fondType()));
    }
}
