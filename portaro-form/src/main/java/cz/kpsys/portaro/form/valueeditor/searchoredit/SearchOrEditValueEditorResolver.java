package cz.kpsys.portaro.form.valueeditor.searchoredit;

import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.form.validation.ValidationsResolver;
import cz.kpsys.portaro.form.valueeditor.*;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.EditorOptions;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.CreateParameters;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.EditParameters;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.SearchOrEditEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.StaticSearchParameters;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;

import static cz.kpsys.portaro.commons.util.StringUtil.notEmptyString;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchOrEditValueEditorResolver implements AnnotationsAwareValueEditorResolver {

    @NonNull ValidationsResolver validationsResolver;
    @NonNull AnnotationValueEditorModifier<Department> annotationValueEditorModifier;

    @Override
    public Optional<SearchOrEditValueEditor> resolve(Object formObject, String fieldName, Department currentDepartment) {
        return this.resolveEditor(formObject, fieldName, currentDepartment, false);
    }

    @Override
    public Optional<SearchOrEditValueEditor> resolveByTypeAnnotation(Object formObject, String fieldName, Department currentDepartment) {
        return this.resolveEditor(formObject, fieldName, currentDepartment, true);
    }

    private Optional<SearchOrEditValueEditor> resolveEditor(Object formObject, String fieldName, Department department, boolean searchInTypeAnnotation) {
        if (!AnnotationUtil.hasAnnotationIncludingTypeAnnotations(formObject, fieldName, SearchOrEditEditor.class, searchInTypeAnnotation)) {
            return Optional.empty();
        }

        Optional<SearchOrEditEditor> annotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, SearchOrEditEditor.class, searchInTypeAnnotation);

        Optional<EditorOptions> editorOptionsAnnotations = annotation.map(SearchOrEditEditor::editorOptions);

        Optional<StaticSearchParameters> staticSearchParametersAnnotation = annotation.map(SearchOrEditEditor::staticSearchParameters);

        Optional<CreateParameters> createParametersAnnotation = annotation.map(SearchOrEditEditor::createParameters);

        Optional<EditParameters> editParametersAnnotation = annotation.map(SearchOrEditEditor::editParameters);

        Optional<Boolean> immediatelyTriggerSearch = annotation.map(SearchOrEditEditor::immediatelyTriggerSearch);

        SearchOrEditValueEditor editor = SearchOrEditValueEditor.getEmptyEditor().withEditorName(fieldName);

        editor = validationsResolver.processCommonValueEditorValidations(formObject, fieldName, editor, searchInTypeAnnotation);

        editor = ValueEditor.fillValueEditorWithOptions(editor, editorOptionsAnnotations);

        editor = ValueEditorDescriptionResolver.processDescription(editor, formObject, fieldName, searchInTypeAnnotation);

        editor = ValueEditorAliasHelper.aliasEditor(formObject, fieldName, searchInTypeAnnotation, editor);

        editor = annotationValueEditorModifier.modifyEditorIfModifierPresent(formObject, fieldName, searchInTypeAnnotation, editor, department);

        editor = staticSearchParametersAnnotation.map(this::createStaticSearchParams).map(editor::withStaticSearchParams).orElse(editor);
        editor = createParametersAnnotation.map(this::createCreateParams).map(editor::withCreateParams).orElse(editor);
        editor = editParametersAnnotation.map(this::createEditParams).map(editor::withEditParams).orElse(editor);
        editor = immediatelyTriggerSearch.map(editor::withImmediatelyTriggerSearch).orElse(editor);

        return Optional.of(editor);
    }

    private StaticSearchParams createStaticSearchParams(StaticSearchParameters paramsFromAnnotations) {
        return StaticSearchParams
                .createEmpty()
                .withKind(notEmptyString(paramsFromAnnotations.kind()))
                .withSubkind(notEmptyString(paramsFromAnnotations.subkind()))
                .withType(notEmptyString(paramsFromAnnotations.type()))
                .withDatasource(notEmptyString(paramsFromAnnotations.datasource()))
                .withFondType(notEmptyString(paramsFromAnnotations.fondType()))
                .withDatasourceGroup(notEmptyString(paramsFromAnnotations.datasourceGroup()));
    }

    private CreateParams createCreateParams(CreateParameters paramsFromAnnotations) {
        return CreateParams
                .createEmpty()
                .withKind(notEmptyString(paramsFromAnnotations.kind()))
                .withSubkind(notEmptyString(paramsFromAnnotations.subkind()));
    }

    private EditParams createEditParams(EditParameters paramsFromAnnotations) {
        return EditParams
                .createEmpty()
                .withKind(notEmptyString(paramsFromAnnotations.kind()))
                .withSubkind(notEmptyString(paramsFromAnnotations.subkind()));
    }
}
