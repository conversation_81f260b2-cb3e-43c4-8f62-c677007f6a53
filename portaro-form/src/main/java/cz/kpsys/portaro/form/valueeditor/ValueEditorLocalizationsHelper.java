package cz.kpsys.portaro.form.valueeditor;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.AnnotationUtil;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorLocalization;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class ValueEditorLocalizationsHelper {

    public static <EDITOR extends LocalizationsAwareValueEditor<? extends ValueEditorOptions, ? extends ValueEditorValidations, EDITOR>> EDITOR processLocalizations(EDITOR valueEditor, Object formObject, String fieldName, boolean searchInTypeAnnotation) {
        List<ValueEditorLocalization> valueEditorLocalizations = new ArrayList<>();

        Optional<ValueEditorLocalization> localizationAnnotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, ValueEditorLocalization.class, searchInTypeAnnotation);
        Optional<ValueEditorLocalization.List> localizationsAnnotation = AnnotationUtil.findAnnotationIncludingTypeAnnotations(formObject, fieldName, ValueEditorLocalization.List.class, searchInTypeAnnotation);

        localizationAnnotation
                .map(Arrays::asList)
                .ifPresent(valueEditorLocalizations::addAll);

        localizationsAnnotation
                .map(ValueEditorLocalization.List::value)
                .map(Arrays::asList)
                .ifPresent(valueEditorLocalizations::addAll);

        for (ValueEditorLocalization valueEditorLocalization : valueEditorLocalizations) {
            valueEditor = valueEditor.addLocalization(valueEditorLocalization.key(), Texts.ofCurlyBracesFormat(valueEditorLocalization.message()));
        }

        return valueEditor;
    }

}
