package cz.kpsys.portaro.sip2.server.adapter;

import com.ceridwen.circulation.SIP.messages.ItemStatusUpdateResponse;
import cz.kpsys.portaro.sip2.server.Sip2ServerResponse;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2ItemStatusUpdateServerResponse implements Sip2ServerResponse<ItemStatusUpdateResponse> {

    @Getter
    @NonNull
    ItemStatusUpdateResponse message;

    @Override
    public void setScreenMessage(@NonNull String screenMessage) {
        message.setScreenMessage(screenMessage);
    }

    public void setTitleIdentifier(String titleIdentifier) {
        message.setTitleIdentifier(titleIdentifier);
    }

    public void setPrintLine(String printLine) {
        message.setPrintLine(printLine);
    }

    public void setItemProperties(String itemProperties) {
        message.setItemProperties(itemProperties);
    }

    public void setItemIdentifier(String itemIdentifier) {
        message.setItemIdentifier(itemIdentifier);
    }
}
