package cz.kpsys.portaro.ext.sutin.businessdatatypes.companies;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinCompanyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
public record SutinCompany(

        // elementId
        @NonNull String id,

        @NonNull Department division,

        @Nullable String linkedDivisionElementId,

        @NonNull Boolean workReportEnabled,

        Optional<UUID> recordId,

        @NonNull CompanyRecordData companyRecordData

) implements IdentifiedRecord<String> {

    public static final String WORK_REPORT_ENABLED_INDICATOR = "A";

    public static SutinCompany mapNewCompany(@NonNull SutinCompanyResponse company, @Nullable UUID recordId, Department ctx) {
        log.info("Converting company: {}", company.companyData().name());

        return new SutinCompany(
                company.companyData().elementId(),
                ctx,
                company.companyData().linkedDivisionElementId(),
                mapToWorkReportEnabled(company),
                Optional.ofNullable(recordId),
                mapCompanyToRecordData(company)
        );
    }

    private static Boolean mapToWorkReportEnabled(@NonNull SutinCompanyResponse company) {
        if (company.companyData().workReportEnabled() == null) {
            return false;
        }
        return Objects.equals(company.companyData().workReportEnabled(), WORK_REPORT_ENABLED_INDICATOR);
    }

    private static CompanyRecordData mapCompanyToRecordData(@NonNull SutinCompanyResponse company) {
        return new CompanyRecordData(
                company.companyData().name().trim(),
                company.companyData().linkedDivisionElementId() != null,
                StringUtil.trimOrLetNull(company.companyData().companyIdNumber()),
                StringUtil.trimOrLetNull(company.companyData().taxIdNumber()),
                mapDirectoryAddressToCompanyAddress(company),
                mapDirectoryToCompanyContacts(company)
        );
    }

    private static CompanyContact mapDirectoryToCompanyContacts(@NonNull SutinCompanyResponse company) {
        String bankAccountNumber = StringUtil.trimOrLetNull(getContact(company.contacts(), KontaktTypKat.UCET));

        // Vyhozeni samotneho lomitka
        if (bankAccountNumber != null && bankAccountNumber.equals(SutinContants.FORBIDDEN_BANK_ACCOUNT_NUMBER)) {
            bankAccountNumber = null;
        }
        return new CompanyContact(
                StringUtil.trimOrLetNull(getContact(company.contacts(), KontaktTypKat.TELEFON)),
                StringUtil.trimOrLetNull(getContact(company.contacts(), KontaktTypKat.EMAIL)),
                bankAccountNumber,
                StringUtil.trimOrLetNull(getContact(company.contacts(), KontaktTypKat.WEB))
        );
    }

    private static CompanyAddress mapDirectoryAddressToCompanyAddress(@NonNull SutinCompanyResponse company) {
        if (!company.addresses().isEmpty()) {
            return new CompanyAddress(
                StringUtil.trimOrLetNull(getPermanentAddress(company.addresses()).address().street()),
                StringUtil.trimOrLetNull(getPermanentAddress(company.addresses()).address().city()),
                StringUtil.trimOrLetNull(getPermanentAddress(company.addresses()).address().countryId().toString()),
                StringUtil.trimOrLetNull(getPermanentAddress(company.addresses()).address().postalCode()),
                StringUtil.trimOrLetNull(getPermanentAddress(company.addresses()).address().description())
            );
        } else {
            return new CompanyAddress(null, null, null, null, null);
        }
    }

    private static String getContact(List<Kontakt> sutinContacts, KontaktTypKat type) {
        return sutinContacts.stream()
                .filter(kontakt -> kontakt.type().equals(type))
                .findFirst()
                .map(Kontakt::value)
                .orElse(null);
    }

    private static AdresaWithType getPermanentAddress(List<AdresaWithType> sutinAddresses) {
        return sutinAddresses.stream()
                .filter(address -> address.addressType().stream().map(AdresaTyp::adresaTyp).anyMatch(AdresaTypKat.SIDLO::equals))
                .findFirst()
                .orElse(null);
    }
}
