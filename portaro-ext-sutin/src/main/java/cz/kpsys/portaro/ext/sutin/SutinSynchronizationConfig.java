package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.databaseproperties.AutodetectedDatabaseConnectionSettingsContextualProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.department.DepartmentEntity;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.exemplar.imprt.DocumentWithExemplarSaver;
import cz.kpsys.portaro.ext.sutin.api.SutinApiController;
import cz.kpsys.portaro.ext.sutin.recordeditation.*;
import cz.kpsys.portaro.ext.synchronizer.*;
import cz.kpsys.portaro.ext.synchronizer.filters.NoopForeignSystemUserFilter;
import cz.kpsys.portaro.ext.synchronizer.matcher.ExternalIdSyncedUsersEqualFunction;
import cz.kpsys.portaro.ext.synchronizer.matcher.MatchSyncedUsersComparatorFunction;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.lending.LendingService;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.RecordNameResolver;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.ZoneId;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

import static cz.kpsys.portaro.event.Event.Codes.SUTIN_SYNC_UPDATE;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SutinSynchronizationConfig {

    public static final String FEATURE_NAME = "SutinV1";

    @NonNull SettingLoader settingLoader;
    @NonNull ForeignSystemUserSaver foreignSystemUserSaver;
    @NonNull ForeignSystemSynchronizerRegistry<LabeledIdentified<?>> foreignSystemSynchronizerRegistry;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> nonCachedUserSearchLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull AllValuesProvider<Department> departmentLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<@NonNull User> portaroUserProvider;
    @NonNull ExecutorService executorService;
    @NonNull Eventer eventer;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull PhoneNumberValidator phoneNumberValidator;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull LendingService directLendingService;
    @NonNull ParameterizedSearchLoader<MapBackedParams, BasicUser> basicUserSearchLoader;
    @NonNull DocumentWithExemplarSaver documentWithExemplarSaver;
    @NonNull RecordNameResolver recordNameResolver;
    @NonNull Provider<@Nullable Fond> userPersonAuthorityFondProvider;
    @NonNull CacheDeletableById recordCache;
    @NonNull Provider<@NonNull Fond> jobFileFondProvider;
    @NonNull Provider<@NonNull Fond> salaryFondProvider;
    @NonNull Provider<@NonNull Fond> workPositionFondProvider;
    @NonNull LinkedRecordSearchLoader personWorkCatalogLinkByUserIdSearchLoader;
    @NonNull LinkedRecordSearchLoader propertyWorkCatalogLinkByRecordIdSearchLoader;
    @NonNull Provider<@NonNull Fond> personWorkCatalogLinkFondProvider;
    @NonNull Provider<@NonNull Fond> propertyWorkCatalogLinkFondProvider;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull Saver<Department, DepartmentEntity> departmentSaver;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull Saver<CustomSetting<String>, ?> customSettingSaver;
    @NonNull Provider<@Nullable Fond> departmentFondProvider;

    @Bean
    public BulkSynchronizer<User> sutinSystemSynchronizer() {
        return new AlreadyRunningCheckingBulkSynchronizer<>(
                new DepartmentsBrowsingBulkSynchronizer<>(
                        settingLoader.getStaticValueMatchingContextsProvider(SutinSettingKeys.SUTIN_ENABLED, true),
                        new AuthIsolatedContextualBulkSynchronizer<>(
                                departmentedSutinDataSynchronizer(),
                                authenticationHolder,
                                portaroUserProvider,
                                executorService
                        )
                )
        );
    }

    //SUTIN
    @Bean
    public AuthenticatedContextualBulkSynchronizer<User> departmentedSutinDataSynchronizer() {
        AuthenticatedContextualBulkSynchronizer<User> bean = new LoadingAndSavingAuthenticatedContextualBulkSynchronizer(
                sutinUserLoader(),
                foreignSystemUserSaver,
                new NoopForeignSystemUserFilter());
        return new EventSavingAuthenticatedContextualBulkSynchronizer<>(
                bean,
                eventer,
                SUTIN_SYNC_UPDATE
        );
    }

    @Bean
    public ForeignSystemUserLoader sutinUserLoader() {
        return new SutinUserLoader(
                nonCachedUserSearchLoader,
                sutinDataLoader(),
                sutinRecordDataLoader(),
                contextHierarchyLoader,
                departmentLoader,
                new MatchSyncedUsersComparatorFunction<>(List.of(
                        new ExternalIdSyncedUsersEqualFunction<>(Person::getSyncId))
                ),
                eventer,
                authenticationHolder,
                personRecordSaver(),
                readerCategoryLoader,
                phoneNumberValidator,
                idsToRecordsConverter,
                jobFileFondProvider,
                sutinJobFileRecordEditationCreator(),
                salaryFondProvider,
                sutinSalaryEditationCreator(),
                personWorkCatalogLinkByUserIdSearchLoader,
                sutinWorkCatalogLinkRecordEditationCreator()
        );
    }

    @Bean
    public SutinRecordDataLoader sutinRecordDataLoader() {
        return new SutinRecordDataLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                readonlyTransactionTemplateFactory.get()
        );
    }

    @Bean
    public JobFileRecordEditationCreator sutinJobFileRecordEditationCreator() {
        return new JobFileRecordEditationCreator(
                new JobFileRecordEditationFiller(
                        sutinRecordDataLoader(),
                        recordLoader,
                        recordEditationHelper,
                        recordCache
                ),
                jobFileFondProvider.throwingWhenNull(),
                recordEditationFactory
        );
    }

    @Bean
    public SalaryRecordEditationCreator sutinSalaryEditationCreator() {
        return new SalaryRecordEditationCreator(
                new SalaryRecordEditationFiller(
                        sutinRecordDataLoader(),
                        recordLoader,
                        recordCache,
                        recordEditationHelper
                ),
                salaryFondProvider.throwingWhenNull(),
                recordEditationFactory
        );
    }

    @Bean
    public WorkCatalogLinkRecordEditationCreator sutinWorkCatalogLinkRecordEditationCreator() {
        return new WorkCatalogLinkRecordEditationCreator(
                new PersonCatalogLinkRecordEditationFiller(
                        sutinRecordDataLoader(),
                        recordLoader,
                        recordCache,
                        recordEditationHelper
                ),
                new PropertyCatalogLinkRecordEditationFiller(
                        sutinRecordDataLoader(),
                        recordLoader,
                        recordCache,
                        recordEditationHelper
                ),
                personWorkCatalogLinkFondProvider.throwingWhenNull(),
                propertyWorkCatalogLinkFondProvider.throwingWhenNull(),
                recordEditationFactory
        );
    }

    @Bean
    public StaticProvider<@NonNull ZoneId> sutinTimeZoneIdProvider() {
        return StaticProvider.of(CoreConstants.CZECH_TIME_ZONE_ID);
    }

    @Bean
    public AdhocQueryer<Department> sutinAdhodQuerier() {
        return AdhocQueryer.create(
                sutinTimeZoneIdProvider(),
                settingLoader.getDepartmentedProvider(SutinSettingKeys.SUTIN_ENABLED).toEnabledAsserter(value -> value, FEATURE_NAME, null),
                new AutodetectedDatabaseConnectionSettingsContextualProvider<>(
                        settingLoader.getDepartmentedProvider(SutinSettingKeys.SUTIN_DB_CONNECTION_STRING).throwingWhenNull(),
                        settingLoader.getDepartmentedProvider(SutinSettingKeys.SUTIN_DB_USER).throwingWhenNull(),
                        settingLoader.getDepartmentedProvider(SutinSettingKeys.SUTIN_DB_PASSWORD).throwingWhenNull()
                )
        );
    }

    @Bean
    public SutinDataLoader sutinDataLoader() {
        return new SutinDataLoader(
                sutinAdhodQuerier()
        );
    }

    @Bean
    public SutinUserRecordEditationCreator personRecordSaver() {
        return new SutinUserRecordEditationCreator(
                personDataRecordEditationFiller(),
                userPersonAuthorityFondProvider.throwingWhenNull(),
                recordEditationFactory,
                recordLoader
        );
    }

    @Bean
    public PersonDataRecordEditationFiller personDataRecordEditationFiller() {
        return new PersonDataRecordEditationFiller(recordEditationHelper);
    }

    @Bean
    public CompanyRecordEditationCreator companyEditationCreator() {
        return new CompanyRecordEditationCreator(
                companyDataRecordEditationFiller(),
                fondLoader,
                departmentFondProvider,
                recordEditationFactory,
                recordLoader
        );
    }

    @Bean
    public CompanyDataRecordEditationFiller companyDataRecordEditationFiller() {
        return new CompanyDataRecordEditationFiller(
                recordEditationHelper,
                sutinRecordDataLoader(),
                recordLoader,
                recordCache
        );
    }

    @Bean
    public SutinCompanyLoaderAndSaver sutinCompanyLoaderAndSaver() {
        return new SutinCompanyLoaderAndSaver(
                sutinDataLoader(),
                sutinRecordDataLoader(),
                companyEditationCreator(),
                departmentAccessor,
                departmentLoader,
                departmentSaver,
                customSettingLoader,
                customSettingSaver
        );
    }

    @Bean
    public PropertyRecordEditationCreator propertyEditationCreator() {
        return new PropertyRecordEditationCreator(
                propertyDataRecordEditationFiller(),
                fondLoader,
                recordEditationFactory,
                recordLoader
        );
    }

    @Bean
    public PropertyDataRecordEditationFiller propertyDataRecordEditationFiller() {
        return new PropertyDataRecordEditationFiller(
                recordLoader,
                recordEditationHelper
        );
    }

    @Bean
    public SutinPropertyMapper sutinPropertyMapper() {
        return new SutinPropertyMapper(
                sutinRecordDataLoader(),
                departmentAccessor
        );
    }

    @Bean
    public SutinPropertyLoaderAndSaver sutinPropertyLoaderAndSaver() {
        return new SutinPropertyLoaderAndSaver(
                sutinDataLoader(),
                sutinPropertyMapper(),
                propertyEditationCreator(),
                departmentAccessor,
                directLendingService,
                basicUserSearchLoader,
                documentWithExemplarSaver,
                recordNameResolver,
                propertyWorkCatalogLinkByRecordIdSearchLoader,
                sutinWorkCatalogLinkRecordEditationCreator()
        );
    }

    @Bean
    public SutinApiController sutinApiController() {
        return new SutinApiController(
                sutinCompanyLoaderAndSaver(),
                sutinPropertyLoaderAndSaver(),
                companyDepartmentLinker(),
                new SideThreadAuthenticationIsolator(authenticationHolder, portaroUserProvider, executorService)
        );
    }

    @Bean
    public CompanyDepartmentLinker companyDepartmentLinker() {
        return new CompanyDepartmentLinker(
                departmentSaver
        );
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        foreignSystemSynchronizerRegistry.register(ForeignSystemsKeys.SUTIN_SYNCHRONIZATION_MAP_KEY, sutinSystemSynchronizer());
    }

}
