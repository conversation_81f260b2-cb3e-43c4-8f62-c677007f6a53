package cz.kpsys.portaro.ext.sutin.businessdatatypes.companies;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.lang.Nullable;

public record CompanyRecordData(

        @NotNull String companyName,

        @NotNull Boolean isDivision,

        @Nullable @NullableNotBlank String companyIdNumber,

        @Nullable @NullableNotBlank String taxIdNumber,

        CompanyAddress companyAddress,

        CompanyContact companyContact

) {}
