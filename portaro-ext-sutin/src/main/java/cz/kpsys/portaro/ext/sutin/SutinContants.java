package cz.kpsys.portaro.ext.sutin;

import lombok.NonNull;

import java.time.LocalDate;
import java.util.List;

public class SutinContants {

    public static final String SUTIN_SYNC_ID_PREFIX = "sutin";

    public static final String SUTIN_API_URL_PART = "/sutin";

    public static final int DEPARTMENT_ID_FOR_COMPANY_DIRECTORY = 1;

    public static final String SUTOR_GLOBAL_ELEMENT_ID = "45481";

    public static final int PERSON_FOND_ID = 7;
    public static final int COMPANY_FOND_ID = 2;
    public static final int DIVISION_FOND_ID = 60;
    public static final int PROPERTY_FOND_ID = 9;
    public static final int PROPERTY_GROUP_FOND_ID = 71;
    public static final String ELEMENT_ID_FIELD_CODE = "d3";
    public static final String TABLE_ID_FIELD_CODE = "d4";
    public static final String ALLOWED_DIVISIONS = "(75653,42,9,21,79599,79590,79591,79592,25,79594,79593,32,88403,79595,79597,79596,22,81,123,89,79066,75664,44,43,45,46,47,48,49,27,41,31,14,29,136,79065,18,45160,79,3,15,35,10,28,53,87,88,134,12,17,87057,81636,89069,139,88318,19,39,30,36,82716,82717,82718)";
    //public static final String DEPRECATED_SUTIN_DIVISIONS = "(0,2,4,5,6,7,8,11,13,16,20,23,24,34,36,39,40,50,51,52,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,80,82,83,84,85,86,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,124,125,126,127,128,129,130,131,132,133,135,137,138,139,141,142,143,45244)";
    public static final String DEPRECATED_SUTIN_USERS = "(1)";
    public static final List<String> DEPRECATED_SUTIN_USERS_LIST = List.of("1");

    public static final String FORCED_SUTIN_USERS = "(62356,62357)";

    public static final String DEPRECATED_SUTIN_DIVISION_NUMBER_2 = "140"; // Úklid interiéru
    public static final String NEW_SUTIN_DIVISION_NUMBER_2 = "12"; // KOPISTY

    public static final String FORBIDDEN_BANK_ACCOUNT_NUMBER = "/";
    public static final String FORBIDDEN_PROPERTY_REGISTRATION_NUMBER = "?";
    public static final Integer FORBIDDEN_PROPERTY_GROUP_ID = 1;

    public static final int NEW_PROPERTY_HOLDER_DIVISION_NUMBER = 39; // FIXNÍ

    //public static final int DEPRECATED_PROPERTY_DIVISION_NUMBER_5 = 2; // TELECOM
    //public static final int DEPRECATED_PROPERTY_DIVISION_NUMBER_7 = 33; // RANIDO
    //public static final int DEPRECATED_PROPERTY_DIVISION_NUMBER_8 = 37; // KOLEJOVE vozy

    public static boolean isDateSkipped(@NonNull LocalDate date) {
        return date.getYear() > 2200;
    }

}

