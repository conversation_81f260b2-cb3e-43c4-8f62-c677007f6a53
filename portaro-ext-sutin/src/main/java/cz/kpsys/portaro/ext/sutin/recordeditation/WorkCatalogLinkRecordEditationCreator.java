package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class WorkCatalogLinkRecordEditationCreator {

    @NonNull PersonCatalogLinkRecordEditationFiller personCatalogLinkRecordEditationFiller;
    @NonNull PropertyCatalogLinkRecordEditationFiller propertyCatalogLinkRecordEditationFiller;
    @NonNull Provider<@NonNull Fond> personWorkCatalogLinkFondProvider;
    @NonNull Provider<@NonNull Fond> propertyWorkCatalogLinkFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;

    public RecordEditation ofNewRecordOfPerson(@NonNull WorkCatalogLinkDto workCatalogLink,
                                               @NonNull Record userRecord,
                                               @NonNull List<Department> holdingDepartments,
                                               @NonNull Department ctx,
                                               @NonNull UserAuthentication currentAuth) {
        Fond fond = personWorkCatalogLinkFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofNew(fond)
                .notCreatingContextualHolding()
                .build(currentAuth);

        return personCatalogLinkRecordEditationFiller.fill(workCatalogLink, recordEditation, userRecord, ctx, currentAuth);
    }

    public RecordEditation ofExistingRecordOfPerson(@NonNull Record workCatalogLinkRecord,
                                                    @NonNull WorkCatalogLinkDto workCatalogLink,
                                                    @NonNull Record userRecord,
                                                    @NonNull List<Department> holdingDepartments,
                                                    @NonNull Department ctx,
                                                    @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofExisting(workCatalogLinkRecord)
                .notCreatingContextualHolding()
                .build(currentAuth);

        return personCatalogLinkRecordEditationFiller.fill(workCatalogLink, recordEditation, userRecord, ctx, currentAuth);
    }

    public RecordEditation ofNewRecordOfProperty(@NonNull WorkCatalogLinkDto workCatalogLink,
                                               @NonNull Record propertyRecord,
                                               @NonNull List<Department> holdingDepartments,
                                               @NonNull Department ctx,
                                               @NonNull UserAuthentication currentAuth) {
        Fond fond = propertyWorkCatalogLinkFondProvider.get();

        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofNew(fond)
                .notCreatingContextualHolding()
                .build(currentAuth);

        return propertyCatalogLinkRecordEditationFiller.fill(workCatalogLink, recordEditation, propertyRecord, ctx, currentAuth);
    }

    public RecordEditation ofExistingRecordOfProperty(@NonNull Record workCatalogLinkRecord,
                                                    @NonNull WorkCatalogLinkDto workCatalogLink,
                                                    @NonNull Record propertyRecord,
                                                    @NonNull List<Department> holdingDepartments,
                                                    @NonNull Department ctx,
                                                    @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofExisting(workCatalogLinkRecord)
                .notCreatingContextualHolding()
                .build(currentAuth);

        return propertyCatalogLinkRecordEditationFiller.fill(workCatalogLink, recordEditation, propertyRecord, ctx, currentAuth);
    }


}
