package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.companies.SutinCompany;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.creation.RecordEditationCreator;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompanyRecordEditationCreator implements RecordEditationCreator<SutinCompany> {

    @NonNull CompanyDataRecordEditationFiller companyDataRecordEditationFiller;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull Provider<@Nullable Fond> departmentFondProvider;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;

    public RecordEditation ofNewRecord(@NonNull SutinCompany company, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        Fond fond = getFond(company);
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofNew(fond)
                .notCreatingContextualHolding()
                .build(currentAuth);

        return companyDataRecordEditationFiller.fill(company.id(), company.companyRecordData(), recordEditation, ctx, currentAuth);
    }

    private Fond getFond(@NonNull SutinCompany company) {
        if (company.linkedDivisionElementId() != null) {
            return departmentFondProvider.get();
        }
        return fondLoader.getById(SutinContants.COMPANY_FOND_ID);
    }

    public RecordEditation ofExistingRecord(@NonNull SutinCompany company, @NonNull List<Department> holdingDepartments, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        RecordEditation recordEditation = recordEditationFactory
                .on(ctx, holdingDepartments)
                .ofExisting(recordLoader.getById(company.recordId().get()))
                .notCreatingContextualHolding()
                .build(currentAuth);

        recordEditation.changeFond(getFond(company));

        return companyDataRecordEditationFiller.fill(company.id(), company.companyRecordData(), recordEditation, ctx, currentAuth);
    }
}
