package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.Form;
import cz.kpsys.portaro.formannotation.annotations.form.FormPropertyLabel;
import cz.kpsys.portaro.formannotation.annotations.formsubmit.FormSubmit;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAlias;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.SearchOrEditEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.searchoredit.StaticSearchParameters;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.singleacceptable.SingleAcceptableEditor;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.fond.FondType;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import jakarta.validation.constraints.NotNull;

@Form(id = "linkCompanyToDepartment")
@FormSubmit(path = CatalogWebConstants.API_URL_PREFIX + SutinContants.SUTIN_API_URL_PART + "/link-company")
public record LinkCompanyToDepartmentRequest(

        @FormPropertyLabel("{commons.Record}")
        @SearchOrEditEditor(staticSearchParameters = @StaticSearchParameters(kind = BasicMapSearchParams.KIND_RECORD, subkind = BasicMapSearchParams.SUBKIND_DOCUMENT, type = SearchViewConstants.TYPE_SEARCH_SELECTION, fondType = FondType.Constants.DEPARTMENT))
        @ValueEditorAlias(ValueEditorAliasType.RECORD_SEARCH)
        @NotNull
        Record company,

        @FormPropertyLabel("{commons.Department}")
        @NotNull
        @SingleAcceptableEditor(valuesSourceBean = "departmentLoader")
        Department department) {

    public LinkCompanyToDepartmentCommand toCommand() {
        return new LinkCompanyToDepartmentCommand(company(), department());
    }
}
