package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.companies.CompanyRecordData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompanyDataRecordEditationFiller implements RecordEditationFiller<CompanyRecordData> {

    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull CacheDeletableById recordCache;

    public RecordEditation fill(String elementId, CompanyRecordData companyRecordData, RecordEditation recordEditation, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(elementId, RecordSutinSpecificFields.FondCompany.ElementId.TYPE_ID, recordEditation);

        recordEditationHelper.setStringSubfieldValue(companyRecordData.companyName(), true, RecordSutinSpecificFields.FondCompany.CompanyName.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyName.Name.TYPE_ID, recordEditation);

        if (companyRecordData.companyIdNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyIdNumber(), true, RecordSutinSpecificFields.FondCompany.CompanyData.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyData.CompanyIdNumber.TYPE_ID, recordEditation);
        }

        if (companyRecordData.taxIdNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.taxIdNumber(), true, RecordSutinSpecificFields.FondCompany.CompanyData.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyData.TaxIdNumber.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyAddress().street() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyAddress().street(), true, RecordSutinSpecificFields.FondCompany.CompanyAddress.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyAddress.Street.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyAddress().city() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyAddress().city(), true, RecordSutinSpecificFields.FondCompany.CompanyAddress.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyAddress.City.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyAddress().zipCode() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyAddress().zipCode(), true, RecordSutinSpecificFields.FondCompany.CompanyAddress.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyAddress.ZipCode.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyAddress().state() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyAddress().state(), true, RecordSutinSpecificFields.FondCompany.CompanyAddress.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyAddress.State.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyAddress().additionalInformation() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyAddress().additionalInformation(), true, RecordSutinSpecificFields.FondCompany.CompanyAddress.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyAddress.Description.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyContact().phoneNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyContact().phoneNumber(), true, RecordSutinSpecificFields.FondCompany.CompanyContact.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyContact.PhoneNumber.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyContact().email() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyContact().email(), true, RecordSutinSpecificFields.FondCompany.CompanyContact.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyContact.Email.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyContact().bank() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyContact().bank(), true, RecordSutinSpecificFields.FondCompany.CompanyContact.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyContact.BankAccount.TYPE_ID, recordEditation);
        }

        if (companyRecordData.companyContact().website() != null) {
            recordEditationHelper.setStringSubfieldValue(companyRecordData.companyContact().website(), true, RecordSutinSpecificFields.FondCompany.CompanyContact.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.CompanyContact.Website.TYPE_ID, recordEditation);
        }

        if (companyRecordData.isDivision()) {
            Optional<UUID> sutorGlobalId = sutinRecordDataLoader.getRecordIdByExternalId(SutinContants.SUTOR_GLOBAL_ELEMENT_ID, SutinContants.ELEMENT_ID_FIELD_CODE);
            if (sutorGlobalId.isPresent()) {
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(sutorGlobalId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, RecordSutinSpecificFields.FondCompany.ParentCompany.TYPE_ID, true, RecordSutinSpecificFields.FondCompany.ParentCompany.Name.TYPE_ID, recordEditation);
                recordCache.deleteFromCacheById(sutorGlobalId.get());
            }

        }

        return recordEditation;
    }
}
