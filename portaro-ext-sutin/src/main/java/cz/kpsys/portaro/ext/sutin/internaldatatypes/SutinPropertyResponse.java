package cz.kpsys.portaro.ext.sutin.internaldatatypes;


import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.Majetek;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekZaznam;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;

import java.util.List;

public record SutinPropertyResponse(

        Majetek propertyData,

        List<MajetekZaznam> propertyRecords,

        List<WorkCatalogLinkDto> workCatalogLinks
) {
}
