package cz.kpsys.portaro.ext.sutin.businessdatatypes.property;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDate;

public record PropertyRecordData(

        @NonNull String propertyName,

        // V Sutin DB, ale nikam to ted nedavame
        @Nullable @NullableNotBlank String identification,

        @Nullable @NullableNotBlank String note,

        @Nullable @NullableNotBlank String registrationNumber,

        @NonNull Integer groupId,

        @Nullable @NullableNotBlank String serialNumber,

        @Nullable @NullableNotBlank LocalDate purchaseDate,

        @Nullable @NullableNotBlank LocalDate liquidationDate

) {}
