package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobContract;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobFileData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class JobFileRecordEditationFiller {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull CacheDeletableById recordCache;

    public RecordEditation fill(@NonNull JobFileData jobFileData,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record userRecord,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(jobFileData.rowId(), RowId.TYPE_ID, recordEditation);

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, FondJobContract.PersonLink.TYPE_ID, true, FondJobContract.PersonLink.Main.TYPE_ID, recordEditation);

        // Job data
        if (jobFileData.personalNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.personalNumber(), true, FondJobContract.JobData.TYPE_ID, true, FondJobContract.JobData.PersonNumber.TYPE_ID, recordEditation);
        }

        setDay(jobFileData.beginDate(), FondJobContract.JobData.JobBeginDate.TYPE_ID, recordEditation);
        setDay(jobFileData.testTime(), FondJobContract.JobData.TestTimeEndDate.TYPE_ID, recordEditation);
        setDay(jobFileData.endDate(), FondJobContract.JobData.JobEndDate.TYPE_ID, recordEditation);

        if (jobFileData.contractType() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.contractType().portaroVal(), true, FondJobContract.JobData.TYPE_ID, true, FondJobContract.JobData.ContractType.TYPE_ID, recordEditation);
        }

        setDay(jobFileData.contractDate(), FondJobContract.JobData.ContractDate.TYPE_ID, recordEditation);

        if (jobFileData.vat() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.vat(), true, FondJobContract.JobData.TYPE_ID, true, FondJobContract.JobData.VAT.TYPE_ID, recordEditation);
        }

        if (jobFileData.workCategory() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.workCategory().portaroVal(), true, FondJobContract.JobData.TYPE_ID, true, FondJobContract.JobData.Category.TYPE_ID, recordEditation);
        }

        // Job termination
        if (jobFileData.employmentTerminationSide() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.employmentTerminationSide().getPortaroVal(), true, FondJobContract.EmploymentTermination.TYPE_ID, true, FondJobContract.EmploymentTermination.InitiatorSide.TYPE_ID, recordEditation);
        }

        if (jobFileData.employmentTerminationType() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.employmentTerminationType().getPortaroVal(), true, FondJobContract.EmploymentTermination.TYPE_ID, true, FondJobContract.EmploymentTermination.Type.TYPE_ID, recordEditation);
        }

        if (jobFileData.employmentTerminationReason() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.employmentTerminationReason(), true, FondJobContract.EmploymentTermination.TYPE_ID, true, FondJobContract.EmploymentTermination.Reason.TYPE_ID, recordEditation);
        }

        // Employer
        if (jobFileData.employerId() != null) {
            Optional<UUID> employerId = sutinRecordDataLoader.getRecordIdByExternalId(jobFileData.employerId(), SutinContants.ELEMENT_ID_FIELD_CODE);
            if (employerId.isPresent()) {
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(employerId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondJobContract.Employer.TYPE_ID, true, FondJobContract.Employer.Name.TYPE_ID, recordEditation);
                recordCache.deleteFromCacheById(employerId.get());
            } else {
                log.error("Company from fond {} was not found by ID {} = {}!", SutinContants.COMPANY_FOND_ID, SutinContants.ELEMENT_ID_FIELD_CODE, jobFileData.employerId());
            }
        }

        // Division
        if (jobFileData.divisionId() != null) {
            Optional<UUID> divisionId = sutinRecordDataLoader.getRecordIdByExternalId(jobFileData.divisionId(), SutinContants.ELEMENT_ID_FIELD_CODE);
            if (divisionId.isPresent()) {
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(divisionId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondJobContract.Division.TYPE_ID, true, FondJobContract.Division.Main.TYPE_ID, recordEditation);
                recordCache.deleteFromCacheById(divisionId.get());
            } else {
                log.error("Division from fond {} was not found by ID {} = {}!", SutinContants.DIVISION_FOND_ID, SutinContants.ELEMENT_ID_FIELD_CODE, jobFileData.divisionId());
            }
        }

        // Job file
        // TODO: boolean or string?
        recordEditationHelper.setBooleanSubfieldValue(jobFileData.isValid(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.Validity.TYPE_ID, recordEditation);

        if (jobFileData.identificationCardNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.identificationCardNumber(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.IdentificationCardNumber.TYPE_ID, recordEditation);
        }

        if (jobFileData.jobPlace() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.jobPlace().portaroVal(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.JobPlace.TYPE_ID, recordEditation);
        }

        if (jobFileData.identityCardNumber() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.identityCardNumber(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.IdentityCardNumber.TYPE_ID, recordEditation);
        }

        if (jobFileData.identityCardValidity() != null) {
            recordEditationHelper.setDateSubfieldValue(jobFileData.identityCardValidity(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.IdentityCardValidity.TYPE_ID, recordEditation);
        }

        if (jobFileData.workPlacementEndDate() != null) {
            // TODO: what now?
        }

        if (jobFileData.healthInsuranceCode() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.healthInsuranceCode().portaroVal(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.HealthInsuranceCode.TYPE_ID, recordEditation);
        }

        if (jobFileData.handicap() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.handicap().portaroVal(), true, FondJobContract.JobFile.TYPE_ID, true, FondJobContract.JobFile.Handicap.TYPE_ID, recordEditation);
        }

        setDay(jobFileData.identificationCardValidFrom(), FondJobContract.JobFile.JobFileValidFrom.TYPE_ID, recordEditation);
        setDay(jobFileData.identificationCardValidTo(), FondJobContract.JobFile.JobFileValidTo.TYPE_ID, recordEditation);

        // Job leader
        if (jobFileData.superiorPerson() != null && !SutinContants.DEPRECATED_SUTIN_USERS_LIST.contains(jobFileData.superiorPerson())) {
            Optional<UUID> superiorPersonId = sutinRecordDataLoader.getRecordIdByExternalId(jobFileData.superiorPerson(), SutinContants.ELEMENT_ID_FIELD_CODE);
            if (superiorPersonId.isPresent()) {
                DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(superiorPersonId.get()), ctx, currentAuth);
                recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondJobContract.JobLeader.TYPE_ID, true, FondJobContract.JobLeader.Main.TYPE_ID, recordEditation);
                recordCache.deleteFromCacheById(superiorPersonId.get());
            } else {
                log.error("Missing superior person record with element ID {} when importing row ID {}. Probably second pass will be needed!", jobFileData.superiorPerson(), jobFileData.rowId());
            }
        }

        // Additional data
        if (jobFileData.maternityBeginDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobFileData.maternityBeginDate(), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.MaternityBeginDate.TYPE_ID, recordEditation);
        }

        if (jobFileData.maternityEndDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobFileData.maternityEndDate(), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.MaternityEndDate.TYPE_ID, recordEditation);
        }

        if (jobFileData.parentalBeginDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobFileData.parentalBeginDate(), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.ParentalBeginDate.TYPE_ID, recordEditation);
        }

        if (jobFileData.parentalEndDate() != null) {
            recordEditationHelper.setDateSubfieldValue(jobFileData.parentalEndDate(), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.ParentalEndDate.TYPE_ID, recordEditation);
        }

        if (jobFileData.claimedChildren() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(jobFileData.claimedChildren()), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.ClaimedChildren.TYPE_ID, recordEditation);
        }

        if (jobFileData.unclaimedChildren() != null) {
            recordEditationHelper.setNumberSubfieldValue(BigDecimal.valueOf(jobFileData.unclaimedChildren()), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.UnclaimedChildren.TYPE_ID, recordEditation);
        }

        if (jobFileData.foreclosure() != null) {
            recordEditationHelper.setStringSubfieldValue(jobFileData.foreclosure().getPortaroVal(), true, FondJobContract.AdditionalData.TYPE_ID, true, FondJobContract.AdditionalData.Foreclosure.TYPE_ID, recordEditation);
        }

        return recordEditation;
    }

    private void setDay(@Nullable LocalDate localDate, @NonNull FieldTypeId subfieldTypeId, @NonNull RecordEditation recordEditation) {
        if (localDate == null || SutinContants.isDateSkipped(localDate)) {
            return;
        }
        recordEditationHelper.setDateSubfieldValue(localDate, true, subfieldTypeId.existingParent(), true, subfieldTypeId, recordEditation);
    }

}
