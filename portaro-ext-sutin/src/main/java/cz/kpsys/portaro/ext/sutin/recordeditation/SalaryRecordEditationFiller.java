package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondJobContract;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.FondSalaryCost;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.SutinRecordDataLoader;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SalaryRecordEditationFiller {

    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull CacheDeletableById recordCache;
    @NonNull RecordEditationHelper recordEditationHelper;

    public RecordEditation fill(@NonNull SalaryData salaryData,
                                @NonNull RecordEditation recordEditation,
                                @NonNull Record jobContractRecord,
                                @NonNull Record userRecord,
                                @NonNull Department ctx,
                                @NonNull UserAuthentication currentAuth) {

        recordEditationHelper.setStringTopFieldValue(salaryData.rowId(), RowId.TYPE_ID, recordEditation);

        DetailedRecordValueCommand userRecordLinkCmd = new DetailedRecordValueCommand(userRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(userRecordLinkCmd, true, FondJobContract.PersonLink.TYPE_ID, true, FondJobContract.PersonLink.Main.TYPE_ID, recordEditation);


        DetailedRecordValueCommand jobContractRecordLinkCmd = new DetailedRecordValueCommand(jobContractRecord, ctx, currentAuth);
        recordEditationHelper.setRecordIdSubfieldValue(jobContractRecordLinkCmd, true, FondSalaryCost.JobContract.TYPE_ID, true, FondSalaryCost.JobContract.Label.TYPE_ID, recordEditation);

        if (salaryData.salaryType() != null) {
            recordEditationHelper.setStringSubfieldValue(salaryData.salaryType().getPortaroVal(), true, FondSalaryCost.SalaryType.TYPE_ID, true, FondSalaryCost.SalaryType.Type.TYPE_ID, recordEditation);
        }

        Optional<UUID> workPositionId = sutinRecordDataLoader.getRecordIdByExternalId(salaryData.workPositionId().toString(), RowId.CODE);
        if (workPositionId.isPresent()) {
            DetailedRecordValueCommand cmd = new DetailedRecordValueCommand(recordLoader.getById(workPositionId.get()), ctx, currentAuth);
            recordEditationHelper.setRecordIdSubfieldValue(cmd, true, FondSalaryCost.WorkPosition.TYPE_ID, true, FondSalaryCost.WorkPosition.Main.TYPE_ID, recordEditation);
            recordCache.deleteFromCacheById(workPositionId.get());
        } else {
            log.error("Cannot find work position for {}", salaryData);
        }

        recordEditationHelper.setNumberSubfieldValue(salaryData.basicSalary(), true, FondSalaryCost.BasicSalary.TYPE_ID, true, FondSalaryCost.BasicSalary.Value.TYPE_ID, recordEditation);
        recordEditationHelper.setNumberSubfieldValue(salaryData.floatingSalary(), true, FondSalaryCost.FloatingSalary.TYPE_ID, true, FondSalaryCost.FloatingSalary.Value.TYPE_ID, recordEditation);
        recordEditationHelper.setNumberSubfieldValue(salaryData.rewardMoney(), true, FondSalaryCost.RewardMoney.TYPE_ID, true, FondSalaryCost.RewardMoney.Value.TYPE_ID, recordEditation);

        // TODO: d1833, compute? How?
        // TODO: d1835, where to get?

        setDay(salaryData.validFrom(), FondSalaryCost.ValidFrom.Value.TYPE_ID, recordEditation);
        setDay(salaryData.validTo(), FondSalaryCost.ValidTo.Value.TYPE_ID, recordEditation);

        recordEditationHelper.setNumberSubfieldValue(salaryData.weeklyHours(), true, FondSalaryCost.WeeklyCommitment.TYPE_ID, true, FondSalaryCost.WeeklyCommitment.Hours.TYPE_ID, recordEditation);

        if (salaryData.note() != null) {
            recordEditationHelper.setStringSubfieldValue(salaryData.note(), true, FondSalaryCost.Note.TYPE_ID, true, FondSalaryCost.Note.Value.TYPE_ID, recordEditation);
        }

        return recordEditation;
    }

    private void setDay(@Nullable LocalDate localDate, @NonNull FieldTypeId subfieldTypeId, @NonNull RecordEditation recordEditation) {
        if (localDate == null || SutinContants.isDateSkipped(localDate)) {
            return;
        }
        recordEditationHelper.setDateSubfieldValue(localDate, true, subfieldTypeId.existingParent(), true, subfieldTypeId, recordEditation);
    }

}
