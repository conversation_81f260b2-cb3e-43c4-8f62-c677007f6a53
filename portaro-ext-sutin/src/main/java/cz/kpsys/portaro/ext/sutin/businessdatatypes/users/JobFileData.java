package cz.kpsys.portaro.ext.sutin.businessdatatypes.users;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record JobFileData(

        @NonNull String rowId,

        String personalNumber,

        LocalDate contractDate,

        LocalDate beginDate,

        LocalDate testTime,

        LocalDate endDate,

        @Nullable PracSmlouvaTypKat contractType,

        // PracPoziceKat
        Integer workPosition,

        @Nullable PracKategorieKat workCategory,

        PracVypovedStranaKat employmentTerminationSide,

        PracVypovedTypKat employmentTerminationType,

        String employmentTerminationReason,

        @NullableNotBlank String identityCardNumber,

        LocalDate identityCardValidity,

        @Nullable PracMistoPraceKat jobPlace,

        String superiorPerson,

        String employerId,

        String vat,

        Integer employmentOrder,

        String identificationCardNumber,

        LocalDate identificationCardValidFrom,

        LocalDate identificationCardValidTo,

        String divisionId,

        Character valid,

        PracZdravPojistovnaKat healthInsuranceCode,

        PracInvaliditaKat handicap,

        LocalDate maternityBeginDate,

        LocalDate maternityEndDate,

        LocalDate parentalBeginDate,

        LocalDate parentalEndDate,

        Integer claimedChildren,

        Integer unclaimedChildren,

        Exekuce foreclosure,

        LocalDate workPlacementEndDate

) {

    public static JobFileData fromPracovniPomer(JobFileDto jobFileDto, String superiorPerson) {
        return new JobFileData(
                jobFileDto.rowId(),
                jobFileDto.personalNumber(),
                jobFileDto.contractDate(),
                jobFileDto.beginDate(),
                jobFileDto.testTime(),
                jobFileDto.endDate(),
                jobFileDto.contractType(),
                jobFileDto.workPosition(),
                jobFileDto.workCategory(),
                jobFileDto.employmentTerminationSide(),
                jobFileDto.employmentTerminationType(),
                jobFileDto.employmentTerminationReason(),
                jobFileDto.identityCardNumber(),
                jobFileDto.identityCardValidity(),
                jobFileDto.jobPlace(),
                superiorPerson,
                jobFileDto.employerId(),
                jobFileDto.vat(),
                jobFileDto.employmentOrder(),
                jobFileDto.identificationCardNumber(),
                jobFileDto.identificationCardValidFrom(),
                jobFileDto.identificationCardValidTo(),
                jobFileDto.divisionId(),
                jobFileDto.valid(),
                jobFileDto.healthInsuranceCode(),
                jobFileDto.handicap(),
                jobFileDto.maternityBeginDate(),
                jobFileDto.maternityEndDate(),
                jobFileDto.parentalBeginDate(),
                jobFileDto.parentalEndDate(),
                jobFileDto.claimedChildren(),
                jobFileDto.unclaimedChildren(),
                jobFileDto.foreclosure(),
                jobFileDto.workPlacementEndDate()
        );
    }

    public boolean isValid() {
        return valid != null && valid.equals('A');
    }

}
