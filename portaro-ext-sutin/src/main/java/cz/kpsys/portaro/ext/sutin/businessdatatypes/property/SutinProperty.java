package cz.kpsys.portaro.ext.sutin.businessdatatypes.property;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.SutinContants;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinPropertyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
public record SutinProperty(

        // elementId
        @NonNull String id,

        @NonNull Department division,

        Optional<UUID> recordId,

        Optional<UUID> userRecordId,

        Optional<UUID> propertyGroupRecordId,

        @NonNull PropertyRecordData propertyRecordData,

        @Nullable @NullableNotBlank List<WorkCatalogLinkDto> workCatalogLinks
) {

    public static SutinProperty mapNewProperty(@NonNull SutinPropertyResponse property, @Nullable UUID recordId, @Nullable UUID userRecordId, @Nullable UUID propertyGroupRecordId, Department ctx) {
        log.info("Converting property: {}", property.propertyData().name());

        return new SutinProperty(
                property.propertyData().elementId(),
                ctx,
                Optional.ofNullable(recordId),
                Optional.ofNullable(userRecordId),
                Optional.ofNullable(propertyGroupRecordId),
                mapPropertyToRecordData(property),
                property.workCatalogLinks()
        );
    }

    private static PropertyRecordData mapPropertyToRecordData(@NonNull SutinPropertyResponse property) {
        String registrationNumber = StringUtil.trimOrLetNull(property.propertyData().registrationNumber());

        // Vyhozeni otazniku u budov
        if (registrationNumber != null && registrationNumber.equals(SutinContants.FORBIDDEN_PROPERTY_REGISTRATION_NUMBER)) {
            registrationNumber = null;
        }

        return new PropertyRecordData(
                property.propertyData().name().trim(),
                StringUtil.trimOrLetNull(property.propertyData().identification()),
                StringUtil.trimOrLetNull(property.propertyData().note()),
                registrationNumber,
                property.propertyData().groupId().getId(),
                StringUtil.trimOrLetNull(property.propertyData().serialNumber()),
                property.propertyData().purchaseDate(),
                property.propertyData().liquidationDate()
        );
    }
}
