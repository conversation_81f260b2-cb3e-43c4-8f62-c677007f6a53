package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.databasestructure.RecordDb;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.COLSEQ;
import static cz.kpsys.portaro.commons.db.QueryUtils.TC;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinRecordDataLoader {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull TransactionTemplate transactionTemplate;

    public Optional<UUID> getRecordIdByExternalId(@NonNull String externalId, @NonNull String fieldCode) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(RecordDb.RECORD_FIELD.RECORD_ID);
        sq.from(RecordDb.RECORD_FIELD.TABLE);
        sq.joins().add(RecordDb.RECORD.TABLE, COLSEQ(TC(RecordDb.RECORD_FIELD.TABLE, RecordDb.RECORD_FIELD.RECORD_ID), TC(RecordDb.RECORD.TABLE, RecordDb.RECORD.ID)));
        sq.where()
                .eq(RecordDb.RECORD_FIELD.FIELD_CODE, fieldCode)
                .and()
                .eq(RecordDb.RECORD_FIELD.TEXT_VALUE, externalId)
                .and()
                .isNull(RecordDb.RECORD.DELETION_EVENT_ID);

        List<UUID> recordId = transactionTemplate.execute(_ -> notAutoCommittingJdbcTemplate.queryForList(sq.getSql(), sq.getParamMap(), UUID.class));

        Assert.notNull(recordId, "RecordId loaded in function SutinRecordDataLoader.getRecordIdByExternalId couldn't return null");

        return DataUtils.requireMaxOne(recordId, "RecordId", "externalId " + externalId);
    }

    public List<UUID> getRecordsIdsByExternalIds(@NonNull List<String> externalIds, @NonNull String fieldCode) {
        return externalIds.stream()
                .map(externalId -> getRecordIdByExternalId(externalId, fieldCode))
                .flatMap(Optional::stream)
                .toList();
    }

}
