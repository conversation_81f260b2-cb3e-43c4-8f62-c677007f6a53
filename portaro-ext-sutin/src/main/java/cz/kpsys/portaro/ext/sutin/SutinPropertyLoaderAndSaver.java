package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.exemplar.Exemplar;
import cz.kpsys.portaro.exemplar.imprt.DocumentWithExemplarSaver;
import cz.kpsys.portaro.exemplar.imprt.ImportDocumentWithExemplar;
import cz.kpsys.portaro.exemplar.imprt.ImportExemplar;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.property.SutinProperty;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinPropertyResponse;
import cz.kpsys.portaro.ext.sutin.recordeditation.PropertyRecordEditationCreator;
import cz.kpsys.portaro.ext.sutin.recordeditation.PropertyRecordEditationWithMetadata;
import cz.kpsys.portaro.ext.sutin.recordeditation.WorkCatalogLinkRecordEditationCreator;
import cz.kpsys.portaro.loan.lending.LendingService;
import cz.kpsys.portaro.loan.lending.command.InternalLendingCommand;
import cz.kpsys.portaro.record.detail.RecordNameResolver;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static cz.kpsys.portaro.ext.sutin.SutinContants.DEPARTMENT_ID_FOR_COMPANY_DIRECTORY;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinPropertyLoaderAndSaver {

    @NonNull SutinDataLoader databaseLoader;
    @NonNull SutinPropertyMapper sutinPropertyMapper;
    @NonNull PropertyRecordEditationCreator propertyRecordEditationCreator;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull LendingService lendingService;
    @NonNull ParameterizedSearchLoader<MapBackedParams, BasicUser> basicUserSearchLoader;
    @NonNull DocumentWithExemplarSaver documentWithExemplarSaver;
    @NonNull RecordNameResolver recordNameResolver;
    @NonNull LinkedRecordSearchLoader propertyWorkCatalogLinkByRecordIdSearchLoader;
    @NonNull WorkCatalogLinkRecordEditationCreator workCatalogLinkRecordEditationCreator;

    @Transactional
    public void syncPropertyWithSutin(Department ctx, UserAuthentication currentAuth) {

        Department department = departmentAccessor.getById(DEPARTMENT_ID_FOR_COMPANY_DIRECTORY);
        if (!departmentAccessor.getTheseAndParentChains(List.of(department)).contains(ctx)) {
            throw new IllegalStateException("Cannot call synchronization function in current context (%s), worst context should be (%s)".formatted(ctx.getName(), department.getName()));
        }

        List<SutinPropertyResponse> sutorProperty = databaseLoader.loadSutinProperty(department);

        List<SutinProperty> allSutorProperty = ListUtil.convertStrict(
                sutorProperty,
                sutinPropertyMapper::mapNewProperty);

        List<PropertyRecordEditationWithMetadata> recordEditations = ListUtil.convertStrict(allSutorProperty, property -> {
            if (property.recordId().isPresent()) {
                return new PropertyRecordEditationWithMetadata(
                        propertyRecordEditationCreator.ofExistingRecord(property, List.of(property.division()), ctx, currentAuth),
                        property.division(),
                        property.recordId(),
                        property.userRecordId(),
                        property.propertyGroupRecordId(),
                        property.workCatalogLinks()
                );
            }
            return new PropertyRecordEditationWithMetadata(
                    propertyRecordEditationCreator.ofNewRecord(property, List.of(property.division()), ctx, currentAuth),
                    property.division(),
                    property.recordId(),
                    property.userRecordId(),
                    property.propertyGroupRecordId(),
                    property.workCatalogLinks()
            );
        });

        recordEditations.forEach(recordEditationWithMetadata -> {
            if (recordEditationWithMetadata.recordEditations().isDraft()) {
                recordEditationWithMetadata.recordEditations().publish(ctx, currentAuth);
            } else {
                recordEditationWithMetadata.recordEditations().saveIfModified(ctx, currentAuth);
            }
            syncCalatogLinks(recordEditationWithMetadata, ctx, currentAuth);
            log.info("Property: {} was saved.", recordEditationWithMetadata.recordEditations().getRecord());

            // TODO Dočasně vyhozeno, teď je tam nějaký problém s lokacema a pak tam byl problém u Míly s výpůjčkama, je třeba to dořešit

//            ImportDocumentWithExemplar importDocumentWithExemplar = getImportDocumentWithExemplar(recordEditationWithMetadata);
//            ExemplarImportResult exemplarSaveResult = documentWithExemplarSaver.save(importDocumentWithExemplar, recordEditationWithMetadata.recordEditations().getRecord(), recordEditationWithMetadata.division(), null, ctx, currentAuth);
//
//            if (recordEditationWithMetadata.userRecordId().isPresent() && exemplarSaveResult.exemplars().size() == 1) {
//                Exemplar exemplar = exemplarSaveResult.exemplars().getFirst();
//                Optional<BasicUser> lender = basicUserSearchLoader.getMaxOne(StaticParamsModifier.of(UserConstants.SearchParams.RECORD_ID, List.of(recordEditationWithMetadata.userRecordId().orElseThrow())));
//
//                if (lender.isPresent()) {
//                    InternalLendingCommand lending = createLending(exemplar, lender.orElseThrow(), recordEditationWithMetadata.division(), ctx, currentAuth);
//                    LendingResult lend = lendingService.lend(lending);
//                    log.info("Lending: {} was saved.", lend);
//                }
//            }
        });
    }

    private @NonNull ImportDocumentWithExemplar getImportDocumentWithExemplar(PropertyRecordEditationWithMetadata metadata) {
        return new ImportDocumentWithExemplar(
                recordNameResolver.resolve(metadata.recordEditations().getRecord()),
                List.of(),
                null,
                null,
                null,
                null,
                null,
                1,
                new ImportExemplar(
                        null,
                        null,
                        null
                )
        );
    }

    private InternalLendingCommand createLending(Exemplar exemplar, BasicUser lender, Department lendingDepartment, Department ctx, UserAuthentication currentAuth) {
        return InternalLendingCommand.ofForced(
                exemplar,
                lender,
                currentAuth.getActiveUser(),
                lendingDepartment,
                ctx,
                currentAuth,
                Exemplar.ONE_QUANTITY,
                false
        );
    }

    private void syncCalatogLinks(@NonNull PropertyRecordEditationWithMetadata recordEditationWithMetadata, Department ctx, @NonNull UserAuthentication currentAuth) {
        var newWorkCatalogLinks = recordEditationWithMetadata.workCatalogLinks();
        var currentWorkCatalogLinks = recordEditationWithMetadata.recordId()
                .map(rid -> propertyWorkCatalogLinkByRecordIdSearchLoader.loadByTargetRecordId(rid, ctx))
                .orElse(List.of());

        for (var syncedLink : newWorkCatalogLinks) {
            currentWorkCatalogLinks.stream()
                    .filter(currentLink -> currentLink.hasFirstValue(RowId.FIELD_FINDER, syncedLink.rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundLink -> workCatalogLinkRecordEditationCreator.ofExistingRecordOfProperty(foundLink, syncedLink, recordEditationWithMetadata.recordEditations().getRecord(), List.of(recordEditationWithMetadata.division()), ctx, currentAuth)
                                    .saveIfModified(ctx, currentAuth),
                            () -> workCatalogLinkRecordEditationCreator.ofNewRecordOfProperty(syncedLink, recordEditationWithMetadata.recordEditations().getRecord(), List.of(recordEditationWithMetadata.division()), ctx, currentAuth)
                                    .publish(ctx, currentAuth)
                    );
        }
    }

}
