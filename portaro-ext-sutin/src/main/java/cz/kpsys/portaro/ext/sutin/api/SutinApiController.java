package cz.kpsys.portaro.ext.sutin.api;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.SideThreadAuthenticationIsolator;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.*;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cz.kpsys.portaro.user.sec.SecurityActions.FOREIGN_SYSTEM_SYNCHRONIZATION_RUN;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + SutinContants.SUTIN_API_URL_PART)
@ResponseBody
public class SutinApiController extends GenericApiController {

    @NonNull SutinCompanyLoaderAndSaver sutinCompanyLoaderAndSaver;
    @NonNull SutinPropertyLoaderAndSaver sutinPropertyLoaderAndSaver;
    @NonNull CompanyDepartmentLinker companyDepartmentLinker;
    @NonNull SideThreadAuthenticationIsolator authIsolator;


    @PostMapping("/companies/run-synchronization")
    public ActionResponse syncCompanies(@CurrentDepartment Department ctx,
                                        UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FOREIGN_SYSTEM_SYNCHRONIZATION_RUN, currentAuth, ctx);
        authIsolator.doAuthenticated(ctx, userAuthentication -> sutinCompanyLoaderAndSaver.syncCompaniesWithSutin(ctx, userAuthentication));

        return new FinishedActionResponse(
                MultiText.ofTexts(Texts.ofMessageCoded(LocalizationCodes.Synchronized)));
    }

    @PostMapping("/property/run-synchronization")
    public ActionResponse syncProperty(@CurrentDepartment Department ctx,
                                       UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FOREIGN_SYSTEM_SYNCHRONIZATION_RUN, currentAuth, ctx);
        authIsolator.doAuthenticated(ctx, userAuthentication -> sutinPropertyLoaderAndSaver.syncPropertyWithSutin(ctx, userAuthentication));

        return new FinishedActionResponse(
                MultiText.ofTexts(Texts.ofMessageCoded(LocalizationCodes.Synchronized)));
    }

    @PostMapping("/link-company")
    public ActionResponse linkCompanyToDepartment(@RequestBody @ValidFormObject LinkCompanyToDepartmentRequest request,
                                                  @CurrentDepartment Department ctx,
                                                  UserAuthentication currentAuth) {
        securityManager.throwIfCannot(FOREIGN_SYSTEM_SYNCHRONIZATION_RUN, currentAuth, ctx);
        authIsolator.doAuthenticated(ctx, _ -> companyDepartmentLinker.link(request.toCommand()));

        return new FinishedActionResponse(
                MultiText.ofTexts(Texts.ofMessageCoded(LocalizationCodes.Synchronized)));
    }
}
