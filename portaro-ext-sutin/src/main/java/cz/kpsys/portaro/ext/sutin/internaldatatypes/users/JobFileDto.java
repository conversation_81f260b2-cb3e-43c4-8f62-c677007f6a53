package cz.kpsys.portaro.ext.sutin.internaldatatypes.users;

import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;

public record JobFileDto(

        @NonNull String rowId, // Same row ID as for Pracovnik

        @NonNull String elementId,

        String personalNumber,

        // v sutinu pouze hodnoty Null nebo 1
        Integer professionId,

        LocalDate beginDate,

        LocalDate testTime,

        LocalDate endDate,

        @Nullable PracSmlouvaTypKat contractType,

        Integer workPosition,

        String superiorPerson,

        @Nullable PracKategorieKat workCategory,

        PracVypovedStranaKat employmentTerminationSide,

        PracVypovedTypKat employmentTerminationType,

        String employmentTerminationReason,

        String identityCardNumber,

        LocalDate identityCardValidity,

        @Nullable PracMistoPraceKat jobPlace,

        String employerId,

        LocalDate contractDate,

        String vat,

        Integer employmentOrder,

        String identificationCardNumber,

        LocalDate identificationCardValidFrom,

        LocalDate identificationCardValidTo,

        String divisionId,

        Character valid,

        PracZdravPojistovnaKat healthInsuranceCode,

        PracInvaliditaKat handicap,

        LocalDate maternityBeginDate,

        LocalDate maternityEndDate,

        LocalDate parentalBeginDate,

        LocalDate parentalEndDate,

        Integer claimedChildren,

        Integer unclaimedChildren,

        Exekuce foreclosure,

        LocalDate workPlacementEndDate

) {

    public boolean isValid() {
        return valid != null && valid.equals('A');
    }

}
