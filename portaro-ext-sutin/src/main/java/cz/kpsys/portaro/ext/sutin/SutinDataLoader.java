package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.commons.convert.IdToObjectConverter;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.database.AdhocQueryer;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SelectStatementsDef;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinCompanyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinPropertyResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.companies.Spolecnost;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.contacts.*;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.Majetek;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekSkupina;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.property.MajetekZaznam;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.support.DefaultConversionService;
import org.springframework.jdbc.core.DataClassRowMapper;
import org.springframework.util.Assert;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SuppressWarnings("SqlSourceToSinkFlow")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SutinDataLoader {

    @NonNull AdhocQueryer<Department> adhocQueryer;

    public List<SutinWorkerResponse> loadSutinWorkers(Department ctx) {
        return adhocQueryer.select(ctx, (jdbc) -> {
            DefaultConversionService conversionService = getConversionService();
            List<PersonDto> persons = buildUpToDatePersons(
                    jdbc.query(SelectStatementsDef.getPracovnikSelectStatement(), DataClassRowMapper.newInstance(PersonDto.class, conversionService)));
            log.atDebug().setMessage("Loaded {} workers").addArgument(persons::size).log();

            return ListUtil.convertStrict(persons, employeeData -> {
                List<JobFileDto> professions = jdbc.query(SelectStatementsDef.getPracovniPomerStatement(employeeData.elementId()), DataClassRowMapper.newInstance(JobFileDto.class, conversionService));
                Assert.isTrue(professions.stream().filter(JobFileDto::isValid).count() == 1,
                        () -> "Employee %s should have exactly one active job contract record, but has %s".formatted(employeeData.elementId(), professions));

                List<WorkCatalogLinkDto> workTypes = jdbc.query(SelectStatementsDef.getPracKatalogCinnostiVazby(employeeData.elementId()), DataClassRowMapper.newInstance(WorkCatalogLinkDto.class, conversionService));
                List<SalaryDto> salary = jdbc.query(SelectStatementsDef.getMzdySelectStatement(employeeData.elementId()), DataClassRowMapper.newInstance(SalaryDto.class, conversionService));
                List<Kontakt> contacts = jdbc.query(SelectStatementsDef.getContactSelectStatement(employeeData.elementId()), DataClassRowMapper.newInstance(Kontakt.class, conversionService));

                List<Adresa> address = jdbc.query(SelectStatementsDef.getAddressSelectStatement(employeeData.elementId()), DataClassRowMapper.newInstance(Adresa.class, conversionService));
                List<AdresaWithType> addressWithType = address.stream()
                        .map(adresa -> {
                            List<AdresaTyp> typeList = jdbc.query(SelectStatementsDef.getAddressTypeStatement(adresa.id()), DataClassRowMapper.newInstance(AdresaTyp.class, conversionService));
                            return new AdresaWithType(adresa, typeList);
                        })
                        .toList();

                return new SutinWorkerResponse(employeeData, professions, workTypes, salary, addressWithType, contacts);
            });
        });
    }

    public List<SutinCompanyResponse> loadSutinCompanies(Department ctx) {
        return adhocQueryer.select(ctx, (jdbc) -> {
            DefaultConversionService conversionService = getConversionService();
            List<Spolecnost> sutinCompanies = jdbc.query(SelectStatementsDef.getCompanySelectStatement(), DataClassRowMapper.newInstance(Spolecnost.class, conversionService));

            return ListUtil.convertStrict(sutinCompanies, companyData -> {
                List<Kontakt> contacts = jdbc.query(SelectStatementsDef.getContactSelectStatement(companyData.elementId()), DataClassRowMapper.newInstance(Kontakt.class, conversionService));

                List<Adresa> address = jdbc.query(SelectStatementsDef.getAddressSelectStatement(companyData.elementId()), DataClassRowMapper.newInstance(Adresa.class, conversionService));
                List<AdresaWithType> addressWithType = ListUtil.convertStrict(address, adresa -> {
                    List<AdresaTyp> typeList = jdbc.query(SelectStatementsDef.getAddressTypeStatement(adresa.id()), DataClassRowMapper.newInstance(AdresaTyp.class, conversionService));
                    return new AdresaWithType(adresa, typeList);
                });

                return new SutinCompanyResponse(companyData, addressWithType, contacts);
            });
        });
    }

    public List<SutinPropertyResponse> loadSutinProperty(Department ctx) {
        return adhocQueryer.select(ctx, (jdbcTemplate) -> {
            DefaultConversionService conversionService = getConversionService();
            List<Majetek> sutinProperty = jdbcTemplate.query(SelectStatementsDef.getPropertySelectStatement(), DataClassRowMapper.newInstance(Majetek.class, conversionService));

            return ListUtil.convertStrict(sutinProperty, propertyData -> {
                List<MajetekZaznam> propertyRecords = jdbcTemplate.query(SelectStatementsDef.getPropertyStatusDataSelectStatement(propertyData.elementId()), DataClassRowMapper.newInstance(MajetekZaznam.class, conversionService));
                List<WorkCatalogLinkDto> workTypes = jdbcTemplate.query(SelectStatementsDef.getPracKatalogCinnostiVazby(propertyData.elementId()), DataClassRowMapper.newInstance(WorkCatalogLinkDto.class, conversionService));
                return new SutinPropertyResponse(propertyData, propertyRecords, workTypes);
            });
        });
    }

    private DefaultConversionService getConversionService() {
        DefaultConversionService conversionService = new DefaultConversionService();
        conversionService.addConverter(Integer.class, PracZdravPojistovnaKat.class, new IdToObjectConverter<>(PracZdravPojistovnaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVzdelaniKat.class, new IdToObjectConverter<>(PracVzdelaniKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVypovedTypKat.class, new NullFallbackLoadableConverter<>(PracVypovedTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracVypovedStranaKat.class, new NullFallbackLoadableConverter<>(PracVypovedStranaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracStatniPrislusnostKat.class, new IdToObjectConverter<>(PracStatniPrislusnostKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracSmlouvaTypKat.class, new IdToObjectConverter<>(PracSmlouvaTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracMistoPraceKat.class, new NullFallbackLoadableConverter<>(PracMistoPraceKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracKategorieKat.class, new IdToObjectConverter<>(PracKategorieKat.CODEBOOK));
        conversionService.addConverter(Integer.class, PracInvaliditaKat.class, new IdToObjectConverter<>(PracInvaliditaKat.CODEBOOK));
        conversionService.addConverter(Integer.class, KontaktTypKat.class, new IdToObjectConverter<>(KontaktTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, AdresaTypKat.class, new IdToObjectConverter<>(AdresaTypKat.CODEBOOK));
        conversionService.addConverter(Integer.class, MajetekSkupina.class, new IdToObjectConverter<>(MajetekSkupina.CODEBOOK));
        conversionService.addConverter(String.class, WorkCatalogLinkDto.Typ.class, new IdToObjectConverter<>(WorkCatalogLinkDto.Typ.CODEBOOK));
        conversionService.addConverter(String.class, SalaryType.class, new IdToObjectConverter<>(SalaryType.CODEBOOK));
        conversionService.addConverter(String.class, Gender.class, new NullFallbackLoadableConverter<>(Gender.CODEBOOK));
        conversionService.addConverter(String.class, Exekuce.class, new IdToObjectConverter<>(Exekuce.CODEBOOK));
        return conversionService;
    }

    public static List<PersonDto> buildUpToDatePersons(List<PersonDto> personDtos) {
        var comparisonState = new HashMap<String, PracovnikCoreData>(); // <elementId, PracovnikCoreData>

        var employees = new HashMap<String, PersonDto>(); // <elementId, Pracovnik>
        personDtos.stream()
                .sorted(Comparator.comparing(PersonDto::valid, Comparator.nullsFirst(Comparator.reverseOrder()))
                        .thenComparing(PersonDto::jobEndDate, Comparator.nullsLast(Comparator.naturalOrder())))
                .forEach(pracovnik -> {
                    PersonDto previous = employees.put(pracovnik.elementId(), pracovnik);
                    if (previous != null && pracovnikDetailChanged(comparisonState, pracovnik)) {
                        Assert.state(!previous.isValid(),
                                "Cannot replace valid record %s with %s".formatted(previous, pracovnik));
                        log.info("Employee personal details changed. Using new one! Old: {}, new: {}", previous, pracovnik);
                    }
                });
        return employees.values().stream().toList();
    }

    /// Mapping workers to smaller data structure with most important data and watching for their change
    private static boolean pracovnikDetailChanged(Map<String, PracovnikCoreData> comparisonState, PersonDto personDto) {
        var nextData = PracovnikCoreData.fromPracovnik(personDto);
        var previousData = comparisonState.put(nextData.elementId(), nextData);
        return previousData != null && !previousData.equals(nextData);
    }

}
