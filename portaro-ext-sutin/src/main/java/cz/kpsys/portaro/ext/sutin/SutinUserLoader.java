package cz.kpsys.portaro.ext.sutin;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.phonenumber.InvalidPhoneNumberException;
import cz.kpsys.portaro.commons.phonenumber.PhoneNumberValidator;
import cz.kpsys.portaro.commons.sync.SyncItem;
import cz.kpsys.portaro.commons.sync.SyncUtil;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.OptionalUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields.RowId;
import cz.kpsys.portaro.erp.job.LinkedRecordSearchLoader;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.EventWorkerLog;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.JobFileData;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SalaryData;
import cz.kpsys.portaro.ext.sutin.businessdatatypes.users.SutinUser;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.SutinWorkerResponse;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracMistoPraceKat;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.PracVzdelaniKat;
import cz.kpsys.portaro.ext.sutin.recordeditation.JobFileRecordEditationCreator;
import cz.kpsys.portaro.ext.sutin.recordeditation.SalaryRecordEditationCreator;
import cz.kpsys.portaro.ext.sutin.recordeditation.SutinUserRecordEditationCreator;
import cz.kpsys.portaro.ext.sutin.recordeditation.WorkCatalogLinkRecordEditationCreator;
import cz.kpsys.portaro.ext.synchronizer.ForeignSystemUserLoader;
import cz.kpsys.portaro.ext.synchronizer.matcher.SyncedUsersComparatorFunction;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.user.Person;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.edit.command.*;
import cz.kpsys.portaro.user.role.reader.ReaderCategory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Stream;

import static cz.kpsys.portaro.ext.sutin.SutinContants.SUTIN_SYNC_ID_PREFIX;
import static cz.kpsys.portaro.user.contact.SourceOfData.EXTERNAL_SUTIN;
import static java.util.stream.Collectors.toUnmodifiableSet;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class SutinUserLoader implements ForeignSystemUserLoader {

    public static final LocalDate FRESHNESS_DATE = LocalDate.of(2024, 11, 1);

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull SutinDataLoader databaseLoader;
    @NonNull SutinRecordDataLoader sutinRecordDataLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull AllValuesProvider<Department> departmentLoader;
    @NonNull SyncedUsersComparatorFunction<Department, SutinUser> syncedUsersCompareFunction;
    @NonNull Eventer eventer;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull SutinUserRecordEditationCreator sutinUserRecordEditationCreator;
    @NonNull ByIdLoadable<ReaderCategory, String> readerCategoryLoader;
    @NonNull PhoneNumberValidator phoneNumberValidator;
    @NonNull Converter<List<UUID>, List<? extends Record>> idsToRecordsConverter;
    @NonNull Provider<@NonNull Fond> jobFileFondProvider;
    @NonNull JobFileRecordEditationCreator sutinJobFileRecordEditationCreator;
    @NonNull Provider<@NonNull Fond> salaryFondProvider;
    @NonNull SalaryRecordEditationCreator salaryRecordEditationCreator;
    @NonNull LinkedRecordSearchLoader personWorkCatalogLinkByUserIdSearchLoader;
    @NonNull WorkCatalogLinkRecordEditationCreator workCatalogLinkRecordEditationCreator;

    @Override
    public List<UserRecordEditationCommand<PersonEditationCommand>> getForeignUsersFromServer(Department ctx, @NonNull UserAuthentication currentAuth) {
        log.info("Sutin user loading started");
        List<SutinWorkerResponse> workers = databaseLoader.loadSutinWorkers(ctx);

        // All existing users loaded from DB with SyncId with Sutin Prefix
        Set<Person> allExistingDbUsers = getAllExistingForeignUsers(ctx);

        // Convert all sutin ElementId to syncIds
        List<SutinUser> allSutinUsers = workers.stream()
                .map(worker -> SutinUser.mapNewUser(worker, tryToFindRecordIdOfExistingPerson(allExistingDbUsers, worker, ctx).orElse(null), this::getWorkersDivision))
                .toList();

        // people, who exists in both system or in db only
        List<SyncItem<Person, SutinUser>> bothAndDbOnlyUsers = ListUtil.convert(allExistingDbUsers, existingDbUser -> tryToConnectWithExternalUser(existingDbUser, allSutinUsers, ctx))
                .stream()
                .filter(SyncItem::isRemaining)
                .toList();

        // return list of new users which are not in DB
        List<SyncItem<Person, SutinUser>> newExternalUsers = allSutinUsers.stream()
                .map(sutinUser -> addNewSutinUser(sutinUser, allExistingDbUsers, ctx))
                .filter(Objects::nonNull)
                .toList();

        // joining both list
        List<SyncItem<Person, SutinUser>> allUsers = ListUtil.union(bothAndDbOnlyUsers, newExternalUsers);

        var res = ListUtil.convert(allUsers, userSync ->
                mapUserToPersonEditationCommand(userSync, ctx, currentAuth));

        log.info("Sutin user loading finished");
        return res;
    }

    private Department getWorkersDivision(@NonNull String divisionId) {
        if (divisionId.equals(SutinContants.DEPRECATED_SUTIN_DIVISION_NUMBER_2)) {
            divisionId = SutinContants.NEW_SUTIN_DIVISION_NUMBER_2;
        }

        String finalDivId = divisionId;
        Optional<? extends Department> division = ListUtil.findSingleMatching(
                departmentLoader.getAll(),
                department -> department.getSigla().equals(finalDivId),
                Department.class,
                "Sigla(Division elementId)=" + finalDivId
        );

        if (division.isPresent()) {
            return division.get();
        }

        throw new IllegalStateException();
    }

    private Optional<UUID> tryToFindRecordIdOfExistingPerson(Set<Person> allExistingDbUsers, SutinWorkerResponse worker, Department ctx) {
        //RECORD ID
        Optional<Person> existingUser = allExistingDbUsers.stream()
                .filter(person -> person.getSyncId().equals(SyncUtil.composeSyncId(SUTIN_SYNC_ID_PREFIX, null, worker.employeeData().elementId().toString())))
                .findFirst();

        if (existingUser.isPresent() && existingUser.get().getRecordId() != null) {
            return Optional.of(existingUser.get().getRecordId());
        }

        return sutinRecordDataLoader.getRecordIdByExternalId(worker.employeeData().elementId(), SutinContants.ELEMENT_ID_FIELD_CODE);
    }

    // Add Sutin data to existing dbUser
    private SyncItem<Person, SutinUser> tryToConnectWithExternalUser(Person existingDbUser, List<SutinUser> externalUsers, Department ctx) {
        List<SutinUser> sutinUsers = ListUtil.filter(externalUsers, externalUser -> syncedUsersCompareFunction.areEqual(externalUser, existingDbUser, ctx));
        Optional<SutinUser> sutinUserOpt = syncedUsersCompareFunction.getBestMatch(sutinUsers, existingDbUser, ctx);
        return SyncItem.ofOptionalExternal(existingDbUser, sutinUserOpt);
    }

    // Add Sutin data to non existing dbUser
    private SyncItem<Person, SutinUser> addNewSutinUser(SutinUser sutinUser, Set<Person> existingDbUsers, Department ctx) {
        boolean userIsNotFound = existingDbUsers.stream().noneMatch(dbUser -> syncedUsersCompareFunction.areEqual(sutinUser, dbUser, ctx));
        if (userIsNotFound) {
            return SyncItem.ofWithoutInternal(sutinUser);
        }
        return null;
    }

    private Set<Person> getAllExistingForeignUsers(Department ctx) {
        List<User> allExistingUsers = userSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(
                CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE),
                UserConstants.SearchParams.SYNC_ID_PREFIX, SUTIN_SYNC_ID_PREFIX
        ));
        return ListUtil.castItems(allExistingUsers, Person.class).collect(toUnmodifiableSet());
    }

    private UserRecordEditationCommand<PersonEditationCommand> mapUserToPersonEditationCommand(SyncItem<Person, SutinUser> userSync, Department ctx, @NonNull UserAuthentication currentAuth) {

        // NEW USER - map raw data
        if (userSync.isAdding()) {
            EventWorkerLog userLogSutin = new EventWorkerLog(userSync.external().get().id(), userSync.external().get().personRecordData().personData().lastName(), userSync.external().get().personRecordData().personData().firstName());
            Map<String, EventWorkerLog> eventData = Map.of("sutinUser", userLogSutin);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_ADD, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapNewUserToPersonEditationCommand(userSync.external().get(), ctx, currentAuth);
        }

        // UPDATE USER - according id just update person
        if (userSync.isRemaining()) {
            EventWorkerLog userLogSutin = new EventWorkerLog(userSync.external().get().id(), userSync.external().get().personRecordData().personData().lastName(), userSync.external().get().personRecordData().personData().firstName());
            EventWorkerLog userLogDb = new EventWorkerLog(userSync.internal().get().getId().toString(), userSync.internal().get().getLastName(), userSync.internal().get().getFirstName());
            Map<String, EventWorkerLog> eventData = Map.of("dbUser", userLogDb, "sutinUser", userLogSutin);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_UPDATE, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapEditedUserToPersonEditationCommand(userSync.external().get(), userSync.internal().get(), ctx, currentAuth);
        }

        // INACTIVE USER - just update expiration date and block
        if (userSync.isRemoving()) {
            EventWorkerLog userLogDb = new EventWorkerLog(userSync.internal().get().getId().toString(), userSync.internal().get().getLastName(), userSync.internal().get().getFirstName());
            Map<String, EventWorkerLog> eventData = Map.of("dbUser", userLogDb);
            eventer.save(Event.Codes.SUTIN_SYNC_USER_INACTIVATE, authenticationHolder.getCurrentAuth(), ctx, eventData);
            return mapInactivatedUserToPersonEditationCommand(userSync.internal().get());
        }

        throw new IllegalStateException();
    }

    // EDIT USER
    private UserRecordEditationCommand<PersonEditationCommand> mapEditedUserToPersonEditationCommand(@NonNull SutinUser externalUser, @NonNull Person dbUser, Department ctx, @NonNull UserAuthentication currentAuth) {
        UserRecordEditationCommand<PersonEditationCommand> personEditationCommand = mapNewUserToPersonEditationCommand(externalUser, ctx, currentAuth);
        personEditationCommand.userEditationCommand().setId(dbUser.getId());
        return personEditationCommand;
    }

    // INACTIVATE USER
    private UserRecordEditationCommand<PersonEditationCommand> mapInactivatedUserToPersonEditationCommand(@NonNull Person user) {
        PersonEditationCommand personEditationCommand = new PersonEditationCommand();

        personEditationCommand.setId(user.getId());
        ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();
        if (user.getReaderAccounts().size() == 1 && !user.getReaderAccounts().getFirst().isRegistrationExpired()) {
            readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(Optional.of(LocalDate.now().minusDays(1)));
        }
        personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));

        return UserRecordEditationCommand.ofUserEditationOnly(personEditationCommand);
    }

    // NEW USER
    private UserRecordEditationCommand<PersonEditationCommand> mapNewUserToPersonEditationCommand(@NonNull SutinUser user, Department ctx, @NonNull UserAuthentication currentAuth) {
        PersonEditationCommand personEditationCommand = new PersonEditationCommand();
        personEditationCommand.setSyncId(Optional.of(user.id()));

        RecordEditation userRecordEditation;
        if (user.recordId().isPresent()) {
            userRecordEditation = sutinUserRecordEditationCreator.ofExistingRecord(user, List.of(user.division()), ctx, currentAuth);
        } else {
            userRecordEditation = sutinUserRecordEditationCreator.ofNewRecord(user, List.of(user.division()), ctx, currentAuth);
        }

        fillPersonEditation(personEditationCommand, user);

        var dependentEditations = new ArrayList<RecordEditation>();
        syncJobs(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);
        syncSalaries(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);
        syncWorkCatalogLinks(user, userRecordEditation.getRecord(), ctx, currentAuth, dependentEditations);

        return new UserRecordEditationCommand<>(personEditationCommand, userRecordEditation, dependentEditations);
    }

    private void syncJobs(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var newJobFiles = user.personRecordData().jobFiles();
        var newJobFilesRowIds = newJobFiles.stream().map(JobFileData::rowId).toList();
        var currentJobs = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordsIdsByExternalIds(newJobFilesRowIds, SutinContants.TABLE_ID_FIELD_CODE)));

        var salaryPairings = user.personRecordData().salaryJobFilePairings();
        for (var syncedJob : newJobFiles) {
            currentJobs.stream()
                    .filter(currentJob -> currentJob.hasFirstValue(RowId.FIELD_FINDER, syncedJob.rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundJob -> outEditations.add(sutinJobFileRecordEditationCreator.ofExistingRecord(foundJob, syncedJob, salaryPairings, userRecord, List.of(user.division()), ctx, currentAuth)),
                            () -> outEditations.add(sutinJobFileRecordEditationCreator.ofNewRecord(syncedJob, salaryPairings, userRecord, List.of(user.division()), ctx, currentAuth)));
        }
    }

    private void syncSalaries(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var newSalaries = user.personRecordData().salaryData();
        var newSalariesRowIds = newSalaries.stream().map(SalaryData::rowId).toList();
        var currentSalaries = Objects.requireNonNull(idsToRecordsConverter.convert(
                sutinRecordDataLoader.getRecordsIdsByExternalIds(newSalariesRowIds, SutinContants.TABLE_ID_FIELD_CODE)));

        var salaryPairings = user.personRecordData().salaryJobFilePairings();
        for (var syncedSalary : user.personRecordData().salaryData()) {
            currentSalaries.stream()
                    .filter(currentSalary -> currentSalary.hasFirstValue(RowId.FIELD_FINDER, syncedSalary.rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundSalary -> outEditations.add(salaryRecordEditationCreator.ofExistingRecord(foundSalary, syncedSalary, salaryPairings, userRecord, List.of(user.division()), ctx, currentAuth)),
                            () -> outEditations.add(salaryRecordEditationCreator.ofNewRecord(syncedSalary, salaryPairings, userRecord, List.of(user.division()), ctx, currentAuth)));
        }
    }

    private void syncWorkCatalogLinks(@NonNull SutinUser user, @NonNull Record userRecord, Department ctx, @NonNull UserAuthentication currentAuth, ArrayList<RecordEditation> outEditations) {
        var newWorkCatalogLinks = user.personRecordData().workCatalogLinks();
        var currentWorkCatalogLinks = user.recordId()
                .map(rid -> personWorkCatalogLinkByUserIdSearchLoader.loadByTargetRecordId(rid, ctx))
                .orElse(List.of());

        for (var syncedLink : newWorkCatalogLinks) {
            currentWorkCatalogLinks.stream()
                    .filter(currentLink -> currentLink.hasFirstValue(RowId.FIELD_FINDER, syncedLink.rowId()))
                    .findAny()
                    .ifPresentOrElse(
                            foundLink -> outEditations.add(workCatalogLinkRecordEditationCreator.ofExistingRecordOfPerson(foundLink, syncedLink, userRecord, List.of(user.division()), ctx, currentAuth)),
                            () -> outEditations.add(workCatalogLinkRecordEditationCreator.ofNewRecordOfPerson(syncedLink, userRecord, List.of(user.division()), ctx, currentAuth)));
        }
    }

    private void fillPersonEditation(PersonEditationCommand personEditationCommand, @NonNull SutinUser user) {
        var lastProfession = user.personRecordData().findLastJobFile();

        personEditationCommand.setReadableDepartments(Optional.of(SimpleEditableList.of(user.division(), DepartmentEditationMode.OVERWRITE_ALL)));

        personEditationCommand.setFirstName(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.personRecordData().personData().firstName())));
        personEditationCommand.setLastName(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.personRecordData().personData().lastName())));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getFirstName()) || OptionalUtil.isNotNullAndPresent(personEditationCommand.getLastName())) {
            personEditationCommand.setNameSource(Optional.of(EXTERNAL_SUTIN));
        }
        personEditationCommand.setPrefixDegree(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.personRecordData().personData().prefixDegree())));
        personEditationCommand.setSuffixDegree(Optional.ofNullable(StringUtil.requireNullableNotBlank(user.personRecordData().personData().suffixDegree())));
        if (OptionalUtil.isNotNullAndPresent(personEditationCommand.getPrefixDegree()) || OptionalUtil.isNotNullAndPresent(personEditationCommand.getSuffixDegree())) {
            personEditationCommand.setDegreeSource(Optional.of(EXTERNAL_SUTIN));
        }
        personEditationCommand.setEducationLevel(Optional.ofNullable(StringUtil.requireNullableNotBlank(ObjectUtil.elvis(user.personRecordData().personData().educationLevel(), PracVzdelaniKat::getValue))));

        personEditationCommand.setJobAddress(lastProfession
                .map(JobFileData::jobPlace)
                .map(PracMistoPraceKat::getValue)
                .map(StringUtil::requireNullableNotBlank));

        if (lastProfession.isPresent() && lastProfession.get().isValid()) {
            personEditationCommand.setIdentityCardNumber(Optional.ofNullable(StringUtil.requireNullableNotBlank(lastProfession.get().identityCardNumber())));

            // TODO: na základě čeho vytvářet účet?
            // CATEGORY
            ReaderAccountEditationCommand readerAccount = ReaderAccountEditationCommand.ofEmpty();

            ReaderCategory readerCategory = readerCategoryLoader.getById(StringUtil.requireNotBlank("S"));
            readerAccount.setReaderCategory(Optional.of(readerCategory));

            readerAccount.setRegistrationDate(lastProfession.map(JobFileData::beginDate));

            readerAccount.setRegistrationExpirationDateAndRegistrationToNowIfEmpty(lastProfession
                    .map(JobFileData::endDate)
                    .or(() -> Optional.of(LocalDate.now().plusYears(1))));

//        readerAccount.setCardNumber(Optional.ofNullable(user.recordData().professionData().personalNumber()));
            personEditationCommand.setReaderAccounts(Optional.of(List.of(readerAccount)));
        } else {
            personEditationCommand.setReaderAccounts(Optional.of(List.of(ReaderAccountEditationCommand.ofEmpty())));
        }


        // CONTACTS
        personEditationCommand.setEmails(Optional.of(SourcedEditableList.of(user.personRecordData().personData().emails(), SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, EXTERNAL_SUTIN)));

        var phoneNumberCommands = user.personRecordData().personData().phoneNumbers().stream()
                .flatMap(phoneNumber -> {
                    try {
                        // Messy data where some older workers have invalid phone numbers
                        return Stream.of(phoneNumber.withValue(phoneNumberValidator.toE164(phoneNumber.value())));
                    } catch (InvalidPhoneNumberException e) {
                        if (lastProfession.isPresent()
                                && lastProfession.get().isValid()
                                && lastProfession.get().endDate() != null
                                && lastProfession.get().endDate().isAfter(FRESHNESS_DATE)) {
                            // For current active users throw error, log otherwise
                            throw e;
                        } else {
                            log.error("Invalid phone number in {}", user.personRecordData().personData().phoneNumbers(), e);
                            return Stream.empty();
                        }
                    }
                })
                .toList();
        personEditationCommand.setPhoneNumbers(Optional.of(SourcedEditableList.of(
                phoneNumberCommands,
                SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE,
                EXTERNAL_SUTIN)));

        personEditationCommand.setAddresses(Optional.of(SourcedEditableList.of(user.personRecordData().personData().addresses(), SourcedEditableListMode.OVERWRITE_DUPLICATE_VALUES_OR_APPEND_AND_EXCLUDE_BY_SOURCE, EXTERNAL_SUTIN)));

        if (user.personRecordData().personData().birthDate() != null && user.personRecordData().personData().birthDate().isBefore(LocalDate.now())) {
            personEditationCommand.setBirthDate(Optional.of(user.personRecordData().personData().birthDate()));
            personEditationCommand.setLifeDateSource(Optional.of(EXTERNAL_SUTIN));
        }
    }

}
