package cz.kpsys.portaro.ext.sutin.recordeditation;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.sutin.internaldatatypes.users.WorkCatalogLinkDto;
import cz.kpsys.portaro.record.edit.RecordEditation;
import lombok.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public record PropertyRecordEditationWithMetadata(

        RecordEditation recordEditations,

        @NonNull Department division,

        Optional<UUID> recordId,

        Optional<UUID> userRecordId,

        Optional<UUID> propertyGroupRecordId,

        List<WorkCatalogLinkDto> workCatalogLinks
) { }
