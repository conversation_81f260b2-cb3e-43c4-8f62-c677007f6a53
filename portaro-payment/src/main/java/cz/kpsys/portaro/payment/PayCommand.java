package cz.kpsys.portaro.payment;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.finance.AmountGroup;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;

public interface PayCommand {

    String PROVIDER_TYPE_CASH = "cash";
    String PROVIDER_TYPE_TERMINAL = "terminal";
    String PROVIDER_TYPE_GATEWAY = "gateway";

    @NonNull
    String provider();

    @NonNull
    BasicUser payer();

    @Nullable
    BasicUser cashier();

    // TODO: změnit na cashierDepartment
    @NonNull
    Department department();

    @NonNull
    AmountGroup<? extends TypedUserAmount> items();

    default BigDecimal sumToPay() {
        return items().getSum().abs();
    }

    @NonNull
    Department ctx();

    @NonNull
    UserAuthentication currentAuth();

    static boolean isOfType(String provider, String requiredType) {
        return provider.startsWith(requiredType);
    }
}
