package cz.kpsys.portaro.record.query;

import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

@Tag("ci")
@Tag("unit")
public class MarqueryParserTest {

    @ParameterizedTest
    @ValueSource(strings = {
            "245",
            "245#1",
            "245#[]",
            "245#[1]",
            "245#[1,2]",
            "#1",
            ".c",
            ".cd",
            ".cdefgahc",
            ".[cd]",
            ".[c,d]",
            ".c#1",
            "#[1,2]",
            "245,260,530,560",
            "245, 260, 530, 560",
            "245,260.xy, 530,560.ab",
            "245,260.#1, 530,560.ab",
            "245,260,530",
            "245, 260#1",
            "245.abcd",
            "245.abc",
            "245.[a,b,c]",
            "245.a#1,.b,.c",
            "245#1.abc#[0,1],.b#[3,5],123#3",
            "245#1.abc#[0, 1], .b#[3,5], 123#3",
            ".abc#[0, 1], #[3, 5], 123#3",
            "245, 260, 530, 560#1.a#2",
            "[245, 260], 530, 560",
            "[245,260]#2, 250#2.ba",
            "[245,260,270,280]#[2,3], 250#[0,1,2].ba",
            "[245,260]#2, [230,220]"
    })
    public void queryShouldBeValid(String query) {
        assertTrue(new MarqueryParser().isValid(query));
    }

    @Test
    public void testParse() {
        String q = "245#1.abc#[0,1], .b#[3,5], [123,124]#3, 115.[a,b]#1";

        FieldGroupFiller filler = mock(FieldGroupFiller.class);

        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings("245"), ints(1), strings("a", "b", "c"), ints(0, 1));
        verify(filler).add(strings(), ints(), strings("b"), ints(3, 5));
        verify(filler).add(strings("123", "124"), ints(3), strings(), ints());
        verify(filler).add(strings("115"), ints(), strings("a", "b"), ints(1));
    }

    @Test
    public void testParse2() {
        String q = "245#1.#[0]";
        FieldGroupFiller filler = mock(FieldGroupFiller.class);
        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings("245"), ints(1), strings(), ints(0));
    }

    @Test
    public void testParse3() {
        String q = "245#.#[]";
        FieldGroupFiller filler = mock(FieldGroupFiller.class);
        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings("245"), ints(), strings(), ints());
    }

    @Test
    public void testParse4() {
        String q = "[245,260,270,280,290,300]#[2,3], 250#[0,1,2].[b,a]";
        FieldGroupFiller filler = mock(FieldGroupFiller.class);
        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings("245", "260", "270", "280", "290", "300"), ints(2, 3), strings(), ints());
        verify(filler).add(strings("250"), ints(0, 1, 2), strings("b", "a"), ints());
    }

    @Test
    public void testParse5() {
        String q = "250#[0,1,2].[abc]";
        FieldGroupFiller filler = mock(FieldGroupFiller.class);
        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings("250"), ints(0, 1, 2), strings("abc"), ints());
    }

    @Test
    public void testParseEmptyRecordNumber() {
        String q = "#3";
        FieldGroupFiller filler = mock(FieldGroupFiller.class);
        new MarqueryParser().parse(q, filler);

        verify(filler).add(strings(), ints(3), strings(), ints());
    }

    @Test
    public void testDifferentlyDefinedEmptiesShouldReturnSameResults() {
        String[] queries = {"245", "245#", "245.", "245#.", "245.#", "245#.#", "245##"};

        for (String q : queries) {
            FieldGroupFiller filler = mock(FieldGroupFiller.class);
            new MarqueryParser().parse(q, filler);

            verify(filler).add(strings("245"), ints(), strings(), ints());
        }
    }

    private static List<Integer> ints(Integer... items) {
        return List.of(items);
    }

    private static List<String> strings() {
        return List.of();
    }

    private static List<String> strings(String... items) {
        return List.of(items);
    }

}
