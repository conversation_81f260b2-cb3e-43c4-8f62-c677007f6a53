package cz.kpsys.portaro.record.query;

import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.TestData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Tag("ci")
@Tag("unit")
public class FieldGroupFillerImplTest {

    private Record record;

    @BeforeEach
    public void setup() {
        record = TestData.createFullRecord();
    }

    @Test
    public void testGetFilledGroup() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls("245"), li(), ls("a"), li());

        ValFieldGroup filledGroup = filler.getResult();

        assertEquals(1, filledGroup.getSize());
        assertEquals("d245", filledGroup.getSingle(0).getParentCode());
    }

    @Test
    public void testGetFilledGroup2() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls("504", "650"), li(0, 2), ls("a"), li(0));

        ValFieldGroup filledGroup = filler.getResult();

        assertEquals(3, filledGroup.getSize());
        assertEquals("d504", filledGroup.getSingle(0).getParentCode());
        assertEquals("d650", filledGroup.getSingle(1).getParentCode());
        assertEquals(0, filledGroup.getSingle(1).getFieldRepetition());
        assertEquals(0, filledGroup.getSingle(1).getRepetition());
        assertEquals("d650", filledGroup.getSingle(2).getParentCode());
        assertEquals(2, filledGroup.getSingle(2).getFieldRepetition());
        assertEquals(0, filledGroup.getSingle(2).getRepetition());
    }

    @Test
    public void testFieldsAreCorrectlySortedByNumbers() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls("650", "504", "245"), li(0), ls("a"), li(0));

        ValFieldGroup filledGroup = filler.getResult();

        assertEquals(3, filledGroup.getSize());
        assertEquals("d650", filledGroup.getSingle(0).getParentCode());
        assertEquals("d504", filledGroup.getSingle(1).getParentCode());
        assertEquals("d245", filledGroup.getSingle(2).getParentCode());
    }

    @Test
    public void testSubfieldsAreCorrectlySortedByCodes() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls("100"), li(0), ls("d", "7", "a"), li(0));

        ValFieldGroup filledGroup = filler.getResult();

        assertEquals(3, filledGroup.getSize());
        assertEquals("d100", filledGroup.getSingle(0).getParentCode());
        assertEquals("d", filledGroup.getSingle(0).getCode());
        assertEquals("d100", filledGroup.getSingle(1).getParentCode());
        assertEquals("7", filledGroup.getSingle(1).getCode());
        assertEquals("d100", filledGroup.getSingle(2).getParentCode());
        assertEquals("a", filledGroup.getSingle(2).getCode());
    }

    @Test
    public void testRepetitionSorting() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls("650"), li(3, 0, 2), ls("a"), li(0));

        ValFieldGroup filledGroup = filler.getResult();

        assertEquals(3, filledGroup.getSize());
        assertEquals("d650", filledGroup.getSingle(0).getParentCode());
        assertEquals(3, filledGroup.getSingle(0).getFieldRepetition());
        assertEquals("d650", filledGroup.getSingle(1).getParentCode());
        assertEquals(0, filledGroup.getSingle(1).getFieldRepetition());
        assertEquals("d650", filledGroup.getSingle(2).getParentCode());
        assertEquals(2, filledGroup.getSingle(2).getFieldRepetition());
    }

    @Test
    public void testControlFieldsShouldNotBeInExplicitlyDefinedCodesDefinition() {
        FieldGroupFillerImpl filler = FieldGroupFillerImpl.ofFlattedDetail(record.getDetail());
        filler.add(ls(), li(), ls("a"), li());

        ValFieldGroup filledGroup = filler.getResult();

        assertFalse(filledGroup.containsOfCode("d1"));
        assertFalse(filledGroup.containsOfCode("d3"));
        assertFalse(filledGroup.containsOfCode("d5"));
        assertFalse(filledGroup.containsOfCode("d7"));
        assertFalse(filledGroup.containsOfCode("d8"));
    }


    private static List<Integer> li(Integer... items) {
        return List.of(items);
    }

    private static List<String> ls(String... items) {
        return List.of(items);
    }

}
