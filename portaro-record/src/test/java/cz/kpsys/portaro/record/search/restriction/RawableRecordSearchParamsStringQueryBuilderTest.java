package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.commons.object.StaticAllValuesProvider;
import cz.kpsys.portaro.commons.object.StaticCodebook;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.search.field.BasicSearchField;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Junction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

@Tag("ci")
@Tag("unit")
public class RawableRecordSearchParamsStringQueryBuilderTest {


    RecordSearchParamsStringQueryBuilder<MapBackedParams> builder = new RecordSearchParamsStringQueryBuilder<>(source -> "mock", new StaticCodebook())
            .withDocumentConjunctionModifier(MapBackedParams.class, new CommonDocumentParamsConjunctionModifier((_, _) -> List.of(Department.testingRoot()), StaticAllValuesProvider.of(Fond.testingMonography()), new StaticCodebook()))
            .withRestrictionModifierFor(MapBackedParams.class, new RawQueryAddingConjunctionModifier());

    private Junction<SearchField> build(MapBackedParams params) {
        Junction<SearchField> result = (Junction<SearchField>) builder.buildFinalRestriction(params, Department.testingRoot());
        System.out.println(result);
        return result;
    }

    @Test
    public void shouldGenerateQtRestriction() {
        SearchField titleSearchField = BasicSearchField.testing("TITLE");
        Term<SearchField> titleEqDavidTerm = new Term<>(titleSearchField, new Eq("david"));
        Junction<SearchField> tree = new Conjunction<SearchField>()
                .add(titleEqDavidTerm);

        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                CoreSearchParams.QT, tree,
                RecordConstants.SearchParams.FOND, List.of(Fond.testingMonography())
        ));
        Junction<SearchField> result = build(params);

        List<Restriction<? extends SearchField>> items = result.getItems();
        assertEquals(2, items.size());
        assertInstanceOf(Conjunction.class, items.getFirst());
        Junction<?> junction0 = (Junction<?>) items.getFirst();
        assertEquals(titleEqDavidTerm, junction0.getItems().getFirst());
    }
    
    
}
