package cz.kpsys.portaro.record.edit.view;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditor;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditorOptions;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.BasicMapSearchParams;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SourceRecordSupportingRecordEditorFactory implements Function<@NonNull Fond, @NonNull RecordValueEditor> {

    @NonNull Function<Fond, RecordValueEditor> standardRecordEditorFactory;
    @NonNull AllValuesProvider<Fond> enabledDocumentFondsProvider;

    @Override
    public RecordValueEditor apply(@NonNull Fond fond) {
        RecordValueEditor standardEditor = standardRecordEditorFactory.apply(fond);
        if (!fond.isForSourceDocument()) {
            return standardEditor;
        }
        List<Fond> fonds = fond.createThisAndGivenFondsList(enabledDocumentFondsProvider.getAll());
        var standardEditorSearchParams = standardEditor.getOptions().orElseGet(RecordValueEditorOptions::getEmptyOptions).searchParams();
        return standardEditor.withSearchParams(
                        standardEditorSearchParams
                        .withKind(BasicMapSearchParams.KIND_RECORD)
                        .withRootFond(fonds));
    }
}
