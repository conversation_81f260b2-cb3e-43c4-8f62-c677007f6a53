package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.math.BigDecimal;
import java.util.List;

public class Formulas {

    public static <RES> Ref<RES> ref(@NonNull LookupDefinition pointer) {
        return new Ref<>(pointer);
    }

    public static <RES> Ref<RES> ref(String linkFieldTypeId, String linkedFieldTypeId) {
        return ref(LookupDefinition.ofSpecificFieldLink(FieldTypeId.parse(linkFieldTypeId), FieldTypeId.parse(linkedFieldTypeId)));
    }

    public static <RES> Ref<RES> localRef(@NonNull String targetFieldTypeId) {
        return new Ref<>(LookupDefinition.ofSelfRecord(FieldTypeId.parse(targetFieldTypeId)));
    }

    public static <RES> BackRef<RES> backRef(@NonNull FieldTypeId linkingRecordsLinkFieldTypeId, @NonNull FieldTypeId linkingRecordsValueFieldTypeId) {
        return new BackRef<>(new LinkingRecordsLookupDefinition(linkingRecordsLinkFieldTypeId, linkingRecordsValueFieldTypeId));
    }

    public static Constant<ScalarFieldValue<BigDecimal>> constZero() {
        return Constant.ofZero();
    }

    public static Constant<ScalarFieldValue<BigDecimal>> constOne() {
        return Constant.ofOne();
    }

    public static Constant<ScalarFieldValue<BigDecimal>> constInteger(@NonNull BigDecimal value) {
        return Constant.ofInteger(value);
    }

    public static Constant<ScalarFieldValue<BigDecimal>> constDecimal2(@NonNull BigDecimal value) {
        return Constant.ofDecimal2(value);
    }

    public static Round round0(@NonNull Formula<ScalarFieldValue<BigDecimal>> operand) {
        return Round.create(operand, CoreConstants.Datatype.NUMBER);
    }

    public static Round round2(@NonNull Formula<ScalarFieldValue<BigDecimal>> operand) {
        return Round.create(operand, CoreConstants.Datatype.NUMBER_DECIMAL_2);
    }

    @SafeVarargs
    public static <RES> Coalesce<RES> coalesce(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return Coalesce.createLazy(primary, secondary, others);
    }

    @SafeVarargs
    public static <RES> Coalesce<RES> eagerCoalesce(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return Coalesce.createEager(primary, secondary, others);
    }

    public static <RES> Conditional<RES> conditional(@NonNull Formula<ScalarFieldValue<Boolean>> condition, @NonNull Formula<RES> onTrue, @NonNull Formula<RES> onFalse) {
        return Conditional.create(condition, onTrue, onFalse);
    }

    @SafeVarargs
    public static Multiplication multiply(@NonNull @NotEmpty Formula<ScalarFieldValue<BigDecimal>>... operands) {
        return new Multiplication(List.of(operands));
    }

    public static Coalesce<ScalarFieldValue<BigDecimal>> orZero(@NonNull Formula<ScalarFieldValue<BigDecimal>> primary) {
        return coalesce(primary, constZero());
    }

    @SafeVarargs
    public static Sum sum(@NonNull @NotEmpty Formula<ScalarFieldValue<BigDecimal>>... operands) {
        return new Sum(List.of(operands));
    }

}
