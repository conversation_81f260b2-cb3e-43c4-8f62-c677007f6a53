package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

public record Coalesce<RES>(

        @NonNull
        @NotEmpty
        List<Formula<RES>> operands,

        @NonNull
        FetchType fetchType

) implements Formula<RES>, NaryFunction<RES> {

    public enum FetchType {
        LAZY, EAGER
    }

    public static <RES> Coalesce<RES> createLazy(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return create(primary, secondary, others, FetchType.LAZY);
    }

    public static <RES> Coalesce<RES> createEager(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>... others) {
        return create(primary, secondary, others, FetchType.EAGER);
    }

    private static <RES> @NonNull Coalesce<RES> create(@NonNull Formula<RES> primary, @NonNull Formula<RES> secondary, @NonNull Formula<RES>[] others, FetchType fetchType) {
        List<Formula<RES>> operands = new ArrayList<>(2 + others.length);
        operands.add(primary);
        operands.add(secondary);
        operands.addAll(Arrays.asList(others));
        return new Coalesce<>(operands, fetchType);
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> operandDatatypes = OperandedFunction.resolveOperandDatatypes(operands, operandResultDatatypeResolver);
        return DatatypeUtil.getMostPreciseDatatypeIfNumberOrRequireSingleUniqueDatatype(operandDatatypes, getClass().getSimpleName());
    }

    @Override
    public String toString() {
        return "Coalesce(%s)".formatted(StringUtil.listToString(operands, ", "));
    }
}
