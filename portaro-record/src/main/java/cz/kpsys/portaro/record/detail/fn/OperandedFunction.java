package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.FieldValueDatatypeResolvingInfiniteLoopException;
import cz.kpsys.portaro.record.detail.FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException;
import jakarta.validation.constraints.NotEmpty;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public sealed interface OperandedFunction<RES> extends Formula<RES> permits Conditional, NaryFunction, UnaryFunction {

    @NonNull
    ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver);

    /// resolves operand datatypes with catching infinite-loops exception
    static <RES> @NonNull List<ScalarDatatype> resolveOperandDatatypes(@NonNull @NotEmpty List<Formula<RES>> operands, Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> resolvedDatatypes = new ArrayList<>(operands.size());
        Map<Formula<RES>, FieldValueDatatypeResolvingInfiniteLoopException> infiniteLoops = new HashMap<>(operands.size());

        for (Formula<RES> operand : operands) {
            try {
                ScalarDatatype resolvedDatatype = operandResultDatatypeResolver.apply(operand);
                resolvedDatatypes.add(resolvedDatatype);
            } catch (FieldValueDatatypeResolvingInfiniteLoopException e) {
                infiniteLoops.put(operand, e);
            }
        }

        if (resolvedDatatypes.isEmpty()) {
            Assert.notEmpty(infiniteLoops, "This should never happen - there should be at least one operand");
            throw new FieldValueDatatypeResolvingInfiniteLoopOfAllOperandsException(infiniteLoops);
        }

        return resolvedDatatypes;
    }
}
