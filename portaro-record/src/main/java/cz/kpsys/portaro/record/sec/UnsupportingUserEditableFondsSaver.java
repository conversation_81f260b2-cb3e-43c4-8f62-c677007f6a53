package cz.kpsys.portaro.record.sec;

import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.user.BasicUser;

import java.util.List;

public class UnsupportingUserEditableFondsSaver implements UserEditableFondsSaver {
    @Override
    public void save(BasicUser user, List<Fond> fonds) {
        throw new UnsupportedOperationException("User editable fonds saving is not supported yet");
    }
}
