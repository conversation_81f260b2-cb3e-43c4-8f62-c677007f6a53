package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.def.LinkDef;
import cz.kpsys.portaro.record.detail.link.def.ParentVirtualGroupToCustomEntrySubfieldLinkDef;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.With;

@With
public record PrefabFieldType(

    @NonNull
    FieldTypeId id,

    @Nullable
    FieldTypeId configPath,

    @NonNull
    String nativeName,

    boolean virtualGroup,

    boolean repeatable,

    boolean autonomous,

    @NonNull
    LinkDef linkDef,

    @NonNull
    FieldGeneration fieldGeneration,

    @NonNull
    FieldSource fieldSource,

    @NonNull
    TransferType transferType,

    @Nullable
    Fond linkRootFondConstraint,

    @Nullable
    ScalarDatatype explicitValueDatatype,

    @NonNull
    FieldExportSetting fieldExportSetting

) implements IdentifiedRecord<FieldTypeId> {

    public static final String TARGET_EXPORT_FIELD_NOT_TO_EXPORT = "0";
    public static final String TARGET_EXPORT_SUBFIELD_NOT_TO_EXPORT = "#";
    public static final Fond NO_LINK_ROOT_FOND = null;
    public static final ScalarDatatype NO_EXPLICIT_VALUE_DATATYPE = null;

    public boolean isTopfield() {
        return id.getLevel() == FieldTypeId.LEVEL_TOPFIELD;
    }

    public boolean hasParent(FieldTypeId parentId) {
        return id.hasParent() && id().existingParent().equals(parentId);
    }

    public boolean shouldHaveVirtualParent() {
        return linkDef() instanceof ParentVirtualGroupToCustomEntrySubfieldLinkDef;
    }

    public boolean shouldBeInSameVirtualGroup(PrefabFieldType other) {
        return shouldHaveVirtualParent() && other.shouldHaveVirtualParent() && id.hasSameParentAs(other.id);
    }

    public PrefabFieldType withModifiedParentId(FieldTypeId modifiedParentId) {
        String currentCode = id().getCode();
        return withId(modifiedParentId.sub(currentCode));
    }

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, PrefabFieldType.class);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        return id.toString();
    }
}
