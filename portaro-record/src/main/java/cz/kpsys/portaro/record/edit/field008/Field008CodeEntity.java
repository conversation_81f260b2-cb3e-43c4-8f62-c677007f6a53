package cz.kpsys.portaro.record.edit.field008;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.DEF_KODY008.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Field008CodeEntity implements Serializable, Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = TYPDOK)
    @NonNull
    String documentTypeCode;

    @Column(name = POZICE)
    @NonNull
    Integer position;

    @Column(name = KOD)
    @NonNull
    String code;

    @Column(name = POPIS)
    @NonNull
    String description;

    @Column(name = JE_DEFAULT)
    @NonNull
    Boolean isDefaultValue;
}
