package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class LinkConstraintsResolver {

    @NonNull ByIdLoadable<Record, UUID> nonDetailedRecordLoader;
    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchSqlLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;

    public LinkConstraints resolve(@NonNull RecordIdFieldTypeId recordIdFieldTypeId, Department ctx) {
        Record record = nonDetailedRecordLoader.getById(recordIdFieldTypeId.recordId());
        Fond fond = record.getFond();
        EditableFieldType fondedFieldType = fieldTypesByFondLoader.findByFondAndId(fond, recordIdFieldTypeId.fieldTypeId(), FieldTypesByFondLoader.WhenMissing.THROW);

        LinkConstraints constraints = LinkConstraints.empty();

        Optional<Fond> linkedFond = fondedFieldType.getLinkRootFond();
        if (linkedFond.isPresent()) {
            constraints = constraints.withFond(linkedFond.get());
        }

        for (LinkConstraintDef constraintDef : fondedFieldType.getLinkConstraints()) {
            constraints = resolveSingleConstraint(recordIdFieldTypeId, constraintDef, constraints, ctx);
        }

        return constraints;
    }

    private LinkConstraints resolveSingleConstraint(@NonNull RecordIdFieldTypeId sourceRecordIdFieldTypeId, LinkConstraintDef constraintDef, LinkConstraints constraints, Department ctx) {
        return switch (constraintDef) {
            case RecordLinkLookupDef def -> resolveLinkConstraint(sourceRecordIdFieldTypeId, def, constraints);
            case LinkingRecordsAsRelatedRecordLinkConstraintDef def -> resolveBacklinkLinkConstraint(sourceRecordIdFieldTypeId, constraints, ctx, def);
        };
    }

    private LinkConstraints resolveLinkConstraint(@NonNull RecordIdFieldTypeId sourceRecordIdFieldTypeId, RecordLinkLookupDef linkLookupDef, LinkConstraints constraints) {
        Optional<RecordIdFondPair> linkOpt = getRecordLink(sourceRecordIdFieldTypeId, linkLookupDef);
        if (linkOpt.isPresent()) {
            return constraints.withRelatedRecordHeader(linkOpt.get());
        }
        return switch (linkLookupDef.onMissing()) {
            case FORBID -> constraints.withForceReturnEmpty(true);
            case ALLOW -> constraints; // omitting
            case THROW -> throw new IllegalStateException("Related record field is missing for record " + sourceRecordIdFieldTypeId.recordId());
        };
    }

    private LinkConstraints resolveBacklinkLinkConstraint(@NonNull RecordIdFieldTypeId sourceRecordIdFieldTypeId, LinkConstraints constraints, Department ctx, LinkingRecordsAsRelatedRecordLinkConstraintDef def) {
        Fond searchedRecordsRootFond = def.searchedRecordsRootFond();
        FieldTypeId searchedRecordsLinkFieldTypeId = def.searchedRecordsLinkFieldTypeId();
        UUID wantedLinkHoldingRecordId = getWantedLinkHoldingRecordId(sourceRecordIdFieldTypeId, def.matchingRecordDef());
        List<Record> records = detailedRecordSearchSqlLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(searchedRecordsRootFond));
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<FieldTypeId>()
                    .add(new Term<>(searchedRecordsLinkFieldTypeId, new Eq(wantedLinkHoldingRecordId))));
        });
        Set<RecordIdFondPair> links = records.stream().map(source -> getRecordLinkFromField(source, def.linkingRecordsLink()).orElseThrow(() -> new IllegalStateException("Related record " + source + " does not have link in " + def.linkingRecordsLink()))).collect(Collectors.toUnmodifiableSet());
        return constraints.withRelatedRecordHeaders(links);
    }

    private @NonNull Optional<RecordIdFondPair> getRecordLink(@NonNull RecordIdFieldTypeId sourceRecordIdFieldTypeId, RecordLinkLookupDef linkLookupDef) {
        FieldTypeId linkFieldTypeId = linkLookupDef.linkFieldTypeId();
        UUID recordId = getWantedLinkHoldingRecordId(sourceRecordIdFieldTypeId, linkLookupDef.recordMatcher());
        Record detailedRecord = recordLoader.getById(recordId);
        return getRecordLinkFromField(detailedRecord, linkFieldTypeId);
    }

    private static @NonNull Optional<RecordIdFondPair> getRecordLinkFromField(Record detailedRecord, FieldTypeId linkFieldTypeId) {
        return detailedRecord.getDetail().getFirstFieldRecursive(By.typeId(linkFieldTypeId))
                .flatMap(Field::getRecordLink);
    }

    private @NonNull UUID getWantedLinkHoldingRecordId(@NonNull RecordIdFieldTypeId sourceRecordIdFieldTypeId, @NonNull MatchingRecordDef recordMatcher) {
        return switch (recordMatcher) {
            case ThisFieldRecordDef _ -> sourceRecordIdFieldTypeId.recordId();
            case ThisFieldRecordLinkDef def -> {
                RecordIdFondPair recordIdFondPair = getRecordLink(sourceRecordIdFieldTypeId, def.linkLookupDef()).orElseThrow();
                yield recordIdFondPair.id().id();
            }
        };
    }

    @With
    public record LinkConstraints(
            boolean forceReturnEmpty,
            @Nullable Fond fond,
            @Nullable Set<RecordIdFondPair> relatedRecordHeaders
    ) {

        public static LinkConstraints empty() {
            return new LinkConstraints(false, null, null);
        }

        public LinkConstraints withRelatedRecordHeader(@NonNull RecordIdFondPair recordIdFondPair) {
            return withRelatedRecordHeaders(Set.of(recordIdFondPair));
        }
    }

}
