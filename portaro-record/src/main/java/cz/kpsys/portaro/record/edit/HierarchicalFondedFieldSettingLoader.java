package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader;
import cz.kpsys.portaro.record.detail.constraints.*;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class HierarchicalFondedFieldSettingLoader implements FondedFieldSettingLoader, CacheCleaner {

    @NonNull SpringDbFondedFieldTypeEntityByFondLoader editableFieldTypeDtoByFondLoader;
    @NonNull PrefabCodebookDelegatingFieldTypeIdByConfigPathLoader fieldTypeIdByConfigPathLoader;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull Codebook<Fond, Integer> fondLoader;

    @NonFinal boolean cacheValid;
    @NonNull ReentrantLockLocker locker = new ReentrantLockLocker();
    @NonNull Map<FieldSettingId, FieldSettings> effectifeFondedCache = new ConcurrentHashMap<>();

    public void loadCaches() {
        locker.lock(() -> {
            if (cacheValid) {
                return;
            }

            Map<FieldSettingId, FieldSettings> cache = new ConcurrentHashMap<>();

            Set<FieldTypeId> requiredFieldIdsAccumulator = new HashSet<>();
            List<FondedFieldTypeEntity> entities = editableFieldTypeDtoByFondLoader.loadAll();
            for (FondedFieldTypeEntity entity : entities) {
                FieldTypeId realTypeId = getRealTypeIdFromConfigPath(entity);
                if (realTypeId == null) {
                    continue;
                }
                Fond fond = fondLoader.getById(entity.styleId());
                FieldSettings fieldSettings = new FieldSettings(
                        entity.fieldDisplayType(),
                        getRequirementType(entity, realTypeId, fond, requiredFieldIdsAccumulator),
                        entity.order(),
                        getLinkConstraintDefs(realTypeId, entity)
                );
                cache.put(new FieldSettingId(fond, realTypeId), fieldSettings);
            }

            // Set required fields to their parents (of the same fond) recursively
            for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : cache.entrySet()) {
                if (identifiedSetting.getValue().requirementType() != FieldRequirementType.REQUIRED) {
                    continue;
                }
                FieldSettingId settingId = identifiedSetting.getKey();
                FieldTypeId requiredFieldTypeId = settingId.fieldTypeId();
                while ((requiredFieldTypeId = requiredFieldTypeId.getParent()) != null) {
                    cache.computeIfPresent(new FieldSettingId(settingId.fond(), requiredFieldTypeId), (_, setting) -> setting.withRequirementType(FieldRequirementType.REQUIRED));
                }
            }

            // remap field settings by combining them with their parents
            Set<FieldTypeId> allFieldTypes = cache.keySet().stream().map(FieldSettingId::fieldTypeId).collect(Collectors.toSet());
            List<Fond> allFonds = fondLoader.getAll();
            for (Fond effectiveFond : allFonds) {
                for (FieldTypeId fieldTypeId : allFieldTypes) {
                    List<Fond> bottomToTopFonds = enabledFondInheritanceLoader.getThisAndAncestors(effectiveFond);
                    for (Fond nodeFond : bottomToTopFonds) {

                        FieldSettings existingNodeFondSetting = cache.get(new FieldSettingId(nodeFond, fieldTypeId));
                        if (existingNodeFondSetting != null) {
                            effectifeFondedCache.merge(new FieldSettingId(effectiveFond, fieldTypeId), existingNodeFondSetting, this::createBestCombination);
                        }

                    }
                }
            }

            cacheValid = true;
        });
    }

    @Override
    public void clearCache() {
        locker.lock(() -> {
            cacheValid = false;
            effectifeFondedCache.clear();
        });
    }

    @Override
    public Map<Fond, FieldSettings> getEffectiveFondedByFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        loadCaches();

        Map<Fond, FieldSettings> settingsAccumulator = new HashMap<>();

        for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : effectifeFondedCache.entrySet()) {
            if (identifiedSetting.getKey().fieldTypeId().equals(fieldTypeId)) {
                settingsAccumulator.put(identifiedSetting.getKey().fond(), identifiedSetting.getValue());
            }
        }

        return settingsAccumulator;
    }

    @Override
    public Map<FieldTypeId, FieldSettings> getFieldTypedByEffectiveFond(@NonNull Fond fond) {
        loadCaches();

        Map<FieldTypeId, FieldSettings> settingsAccumulator = new HashMap<>();

        for (Map.Entry<FieldSettingId, FieldSettings> identifiedSetting : effectifeFondedCache.entrySet()) {
            if (identifiedSetting.getKey().fond().equals(fond)) {
                settingsAccumulator.put(identifiedSetting.getKey().fieldTypeId(), identifiedSetting.getValue());
            }
        }

        return settingsAccumulator;
    }

    private @Nullable FieldTypeId getRealTypeIdFromConfigPath(@NonNull FondedFieldTypeEntity entity) {
        Optional<FieldTypeId> fieldTypeId = fieldTypeIdByConfigPathLoader.findByConfigPath(entity.configPath());
        if (fieldTypeId.isEmpty()) {
            log.warn("Field type defined in styly not found in fdef[aut] by config path {}", entity.configPath());
            return null;
        }
        return fieldTypeId.get();
    }

    private @NonNull FieldRequirementType getRequirementType(FondedFieldTypeEntity entity, FieldTypeId realTypeId, Fond nodeFond, Set<FieldTypeId> requiredFields) {
        return switch (entity.fieldRequirementType()) {
            case REQUIRED -> {
                requiredFields.add(realTypeId);
                yield FieldRequirementType.REQUIRED;
            }
            case INHERIT -> {
                Optional<FieldTypeId> entryNativeSubfieldOpt = recordEntryFieldTypeIdResolver.getEntryNativeSubfieldOpt(nodeFond);
                if (entryNativeSubfieldOpt.isPresent() && realTypeId.equals(entryNativeSubfieldOpt.get())) {
                    requiredFields.add(realTypeId);
                    yield FieldRequirementType.REQUIRED;
                }
                yield FieldRequirementType.INHERIT;
            }
        };
    }

    private Set<LinkConstraintDef> getLinkConstraintDefs(FieldTypeId realTypeId, FondedFieldTypeEntity entity) {
        Set<LinkConstraintDef> linkConstraints = FieldSettings.NO_CONSTRAINTS;

        // PRO test.kpsys.cz
        if (false && realTypeId.value().equals("d2025.main.a")) {
            linkConstraints = Set.of(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d1001.main")));
        }
        if (realTypeId.value().equals("d2054.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordLinkConstraintDef.throwWhenMissing(
                            fondLoader.getById(87), // personWorkCatalogLinkFond (Vazby činností osob)
                            FieldTypeId.parse("d8720.main"),
                            ThisFieldRecordLinkDef.create(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main"))),
                            FieldTypeId.parse("d8710.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2040.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordLinkConstraintDef.throwWhenMissing(
                            fondLoader.getById(87), // personWorkCatalogLinkFond (Vazby činností osob)
                            FieldTypeId.parse("d8720.main"),
                            ThisFieldRecordLinkDef.create(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main"))),
                            FieldTypeId.parse("d8710.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2064.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordLinkConstraintDef.throwWhenMissing(
                            fondLoader.getById(86), // propertyWorkCatalogLinkFond (Vazby činností strojů)
                            FieldTypeId.parse("d8721.main"),
                            ThisFieldRecordLinkDef.create(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2060.main"))),
                            FieldTypeId.parse("d8711.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2045.main.a")) {
            linkConstraints = Set.of(
                    LinkingRecordsAsRelatedRecordLinkConstraintDef.throwWhenMissing(
                            fondLoader.getById(86), // propertyWorkCatalogLinkFond (Vazby činností strojů)
                            FieldTypeId.parse("d8721.main"),
                            ThisFieldRecordLinkDef.create(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2060.main"))),
                            FieldTypeId.parse("d8711.main")
                    )
            );
        }
        if (realTypeId.value().equals("d2051.a")) {
            // mzdové položky
            linkConstraints = Set.of(RecordLinkLookupDef.throwWhenMissing(ThisFieldRecordDef.create(), FieldTypeId.parse("d2050.main")));
        }
        return linkConstraints;
    }

    private FieldSettings createBestCombination(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        FieldDisplayType newDisplayType = combineDisplayType(existingFieldSettings, newFieldSettings);
        FieldRequirementType newRequirementType = combineRequirementType(existingFieldSettings, newFieldSettings);
        int newOrder = combineOrder(existingFieldSettings, newFieldSettings);
        Set<LinkConstraintDef> newConstraints = combineLinkConstraints(existingFieldSettings, newFieldSettings);
        return new FieldSettings(newDisplayType, newRequirementType, newOrder, newConstraints);
    }

    private static FieldDisplayType combineDisplayType(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        List<FieldDisplayType> orderedByBestForEdit = FieldDisplayType.ORDERED_BY_BEST_FOR_EDIT;
        int indexOfExistingDisplayType = orderedByBestForEdit.indexOf(existingFieldSettings.displayType());
        int indexOfNewDisplayType = orderedByBestForEdit.indexOf(newFieldSettings.displayType());
        return orderedByBestForEdit.get(Math.min(indexOfNewDisplayType, indexOfExistingDisplayType));
    }

    private static FieldRequirementType combineRequirementType(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        return FieldRequirementType.merge(existingFieldSettings.requirementType(), newFieldSettings.requirementType());
    }

    private static int combineOrder(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        return Math.min(existingFieldSettings.order(), newFieldSettings.order());
    }

    private static Set<LinkConstraintDef> combineLinkConstraints(FieldSettings existingFieldSettings, FieldSettings newFieldSettings) {
        return ListUtil.unionSet(existingFieldSettings.linkConstraints(), newFieldSettings.linkConstraints());
    }
}
