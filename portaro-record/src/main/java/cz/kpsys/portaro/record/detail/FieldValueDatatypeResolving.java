package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.repo.SeveritedItemNotFoundException;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import static cz.kpsys.portaro.CoreConstants.Datatype.TEXT;
import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public final class FieldValueDatatypeResolving {

    @NonNull FieldValueDatatypeResolving.State state = State.INITIAL;
    @Nullable ScalarDatatype successResult;
    @Nullable RuntimeException failException;

    public static FieldValueDatatypeResolving create() {
        return new FieldValueDatatypeResolving();
    }

    public void start() {
        switch (state) {
            case SUCCESS -> throw new IllegalStateException("Cannot call start() on already resolved (successfuly) datatype");
            case FAILURE -> throw new IllegalStateException("Cannot call start() on already resolved (but failed) datatype");
            case IN_PROGRESS -> throw new FieldValueDatatypeResolvingInfiniteLoopException("Infinite loop while resolving field type value datatype");
            case INITIAL -> this.state = State.IN_PROGRESS;
        }
    }

    public void successNoDatatype() {
        Assert.state(isInProgress(), "Cannot mark success result of resolving, because it is not in progress");
        this.state = State.SUCCESS;
    }

    public void success(@NonNull ScalarDatatype successResult) {
        Assert.state(isInProgress(), "Cannot mark success result of resolving, because it is not in progress");
        this.state = State.SUCCESS;
        this.successResult = successResult;
    }

    public void failure(@NonNull RuntimeException failException) {
        Assert.state(isInProgress(), "Cannot mark failure result of resolving, because it is not in progress");
        this.state = State.FAILURE;
        this.failException = failException;
    }

    public void wrap(@NonNull Supplier<ScalarDatatype> resolver, @NonNull Object formula) {
        try {
            ScalarDatatype successResult = resolver.get();
            success(successResult);

        } catch (SeveritedItemNotFoundException e) {
            String message = "Cannot resolve datatype of formula %s: %s".formatted(formula, e.getMessage());
            if (e.isError()) {
                failure(new IllegalArgumentException(message, e));
            }
            log.warn(message);
            success(TEXT);
        }
    }

    public boolean isFinished() {
        return isSuccess() || isFailure();
    }

    public boolean isInProgress() {
        return state == State.IN_PROGRESS;
    }

    public boolean isSuccess() {
        return state == State.SUCCESS;
    }

    public boolean isFailure() {
        return state == State.FAILURE;
    }

    public @NonNull Optional<ScalarDatatype> finishedResultOrRethrowFail() {
        Assert.state(isFinished(), "Cannot get success result of not finished resolving");
        if (isFailure()) {
            throw requireNonNull(failException);
        }
        return Optional.ofNullable(successResult);
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof FieldValueDatatypeResolving resolving)) {
            return false;
        }
        return state == resolving.state;
    }

    @Override
    public int hashCode() {
        return Objects.hash(state);
    }

    private enum State {
        INITIAL,
        IN_PROGRESS,
        SUCCESS,
        FAILURE
    }
}
