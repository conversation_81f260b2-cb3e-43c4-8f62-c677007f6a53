package cz.kpsys.portaro.record.detail.fn;

import cz.kpsys.portaro.datatype.DatatypeUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import lombok.NonNull;

import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public record Conditional<RES>(

        @NonNull
        Formula<ScalarFieldValue<Boolean>> condition,

        @NonNull
        Formula<RES> onTrue,

        @NonNull
        Formula<RES> onFalse

) implements Formula<RES>, OperandedFunction<RES> {

    public static <RES> @NonNull Conditional<RES> create(@NonNull Formula<ScalarFieldValue<Boolean>> condition,
                                                         @NonNull Formula<RES> onTrue,
                                                         @NonNull Formula<RES> onFalse) {
        return new Conditional<>(condition, onTrue, onFalse);
    }

    @Override
    public @NonNull Set<LookupDefinition> dependencies() {
        return Stream.of(condition, onTrue, onFalse)
                .map(Formula::dependencies)
                .flatMap(Set::stream)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public @NonNull ScalarDatatype resolveResultDatatype(Function<Formula<RES>, @NonNull ScalarDatatype> operandResultDatatypeResolver) {
        List<ScalarDatatype> scalarDatatypes = OperandedFunction.resolveOperandDatatypes(List.of(onTrue, onFalse), operandResultDatatypeResolver);
        return DatatypeUtil.getMostPreciseDatatypeIfNumberOrRequireSingleUniqueDatatype(scalarDatatypes, getClass().getSimpleName());
    }

    @Override
    public String toString() {
        return "If(%s ? %s : %s)".formatted(condition, onTrue, onFalse);
    }
}
