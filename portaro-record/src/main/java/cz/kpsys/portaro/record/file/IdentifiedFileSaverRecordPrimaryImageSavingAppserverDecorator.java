package cz.kpsys.portaro.record.file;

import cz.kpsys.portaro.appserver.dml.DmlAppserverService;
import cz.kpsys.portaro.appserver.dml.TableWrite;
import cz.kpsys.portaro.commons.file.FileViewForm;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.AllIdsByDirectoryProvider;
import cz.kpsys.portaro.file.directory.Directory;
import cz.kpsys.portaro.record.Record;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.RecordDb.KAT1_4;
import static cz.kpsys.portaro.databasestructure.RecordDb.KATAUT_4;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class IdentifiedFileSaverRecordPrimaryImageSavingAppserverDecorator implements Saver<IdentifiedFile, IdentifiedFile> {

    @NonNull Saver<IdentifiedFile, IdentifiedFile> delegate;
    @NonNull DmlAppserverService service;
    @NonNull AllIdsByDirectoryProvider allRecordIdsByDirectoryProvider;
    @NonNull AllIdsByDirectoryProvider allAuthorityIdsByDirectoryProvider;
    @NonNull AllByIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull Saver<Record, ?> recordSaver;


    @Override
    public @NonNull IdentifiedFile save(@NonNull IdentifiedFile file) {
        IdentifiedFile saved = delegate.save(file);

        try {
            checkAndSavePrimaryImage(saved);
        } catch (Exception e) {
            log.error("Error while saving document primary image {}", saved, e);
        }

        return saved;
    }


    private void checkAndSavePrimaryImage(@NonNull IdentifiedFile file) {
        if (file.hasForm(FileViewForm.IMAGE)) {
            Directory directory = file.getDirectory();
            if (directory != null) {
                List<Record> recordsOfDirWithoutPrimaryImage = getRecordsOfDirWithoutPrimaryImage(directory);
                recordsOfDirWithoutPrimaryImage.forEach(record -> {
                    record.setCover(file);
                    recordSaver.save(record);
                });
                savePrimaryImageToRecords(recordsOfDirWithoutPrimaryImage, file);
            }
        }
    }


    private List<Record> getRecordsOfDirWithoutPrimaryImage(@NonNull Directory directory) {
        var documentIdsByDirectory = allRecordIdsByDirectoryProvider.getAllByDirectories(List.of(directory.getId()));
        var authorityIdsByDirectory = allAuthorityIdsByDirectoryProvider.getAllByDirectories(List.of(directory.getId()));
        List<UUID> recordIdsByDirectory = ListUtil.union(documentIdsByDirectory, authorityIdsByDirectory);
        if (!recordIdsByDirectory.isEmpty()) {

            List<Record> recordsWithThisDirectory = nonDetailedRichRecordLoader.getAllByIds(recordIdsByDirectory);

            return recordsWithThisDirectory.stream()
                    .filter(record -> record.getCover() == null)
                    .toList();
        }
        return List.of();
    }

    private void savePrimaryImageToRecords(List<Record> records, IdentifiedFile image) {
        if (records.isEmpty()) {
            return;
        }
        List<TableWrite> tableWrites = records.stream()
                .map(record -> {
                    if (record.getType().equals(Record.TYPE_DOCUMENT)) {
                        return TableWrite.createUpdate(KAT1_4.TABLE)
                                .addWhereCol(KAT1_4.RECORD_ID, record.getId())
                                .addCol(KAT1_4.FK_FULLTEXT_IMAGE, image);
                    }
                    return TableWrite.createUpdate(KATAUT_4.TABLE)
                            .addWhereCol(KATAUT_4.RECORD_ID,  record.getId())
                            .addCol(KATAUT_4.FK_FULLTEXT_IMAGE, image);
                })
                .toList();
        service.executeStatement(tableWrites);
    }

}
