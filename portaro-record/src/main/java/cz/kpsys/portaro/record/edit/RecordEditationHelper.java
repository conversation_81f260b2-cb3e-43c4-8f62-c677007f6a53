package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.CreatableFieldContainer;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

import static cz.kpsys.portaro.record.edit.EmptyFieldCreation.toEnd;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordEditationHelper {

    @NonNull RecordFieldEditor recordFieldEditor;

    public void setComplexAuthorityStringSubfieldValue(@NonNull String value, boolean reuseExistingTopField, FieldTypeId topfieldTypeId, FieldTypeId mainSubfieldTypeId, FieldTypeId subsubfieldTypeId, RecordEditation editation) {
        if (!value.isBlank()) {
            Field<?> field = reuseOrCreateField(reuseExistingTopField, topfieldTypeId, editation);
            Field<?> mainSubfield = reuseOrCreateField(true, mainSubfieldTypeId, field);

            FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), mainSubfield.getFieldId(), MultilabeledPredefinedMissingRecordValueCommand.ofSingle(subsubfieldTypeId, value))
                    .notCreateMissingHierarchy();
            recordFieldEditor.editField(editation, command);
        }
    }

    public void setStringSubfieldValue(@NonNull String value, boolean reuseExistingTopField, FieldTypeId topfieldTypeId, boolean reuseExistingSubfield, FieldTypeId subfieldTypeId, RecordEditation recordEditation) {
        if (!value.isBlank()) {
            setRecordIdSubfieldValue(new StringValueCommand(value), reuseExistingTopField, topfieldTypeId, reuseExistingSubfield, subfieldTypeId, recordEditation);
        }
    }

    public void setDateSubfieldValue(@NonNull LocalDate value, boolean reuseExistingTopField, FieldTypeId topfieldTypeId, boolean reuseExistingSubfield, FieldTypeId subfieldTypeId, RecordEditation recordEditation) {
        setRecordIdSubfieldValue(new LocalDateValueCommand(value), reuseExistingTopField, topfieldTypeId, reuseExistingSubfield, subfieldTypeId, recordEditation);
    }

    public void setNumberSubfieldValue(@NonNull BigDecimal value, boolean reuseExistingTopField, FieldTypeId topfieldTypeId, boolean reuseExistingSubfield, FieldTypeId subfieldTypeId, RecordEditation recordEditation) {
        setRecordIdSubfieldValue(new NumberValueCommand(value), reuseExistingTopField, topfieldTypeId, reuseExistingSubfield, subfieldTypeId, recordEditation);
    }

    public void setBooleanSubfieldValue(boolean value, boolean reuseExistingTopField, FieldTypeId topfieldTypeId, boolean reuseExistingSubfield, FieldTypeId subfieldTypeId, RecordEditation recordEditation) {
        setRecordIdSubfieldValue(new BooleanValueCommand(value), reuseExistingTopField, topfieldTypeId, reuseExistingSubfield, subfieldTypeId, recordEditation);
    }

    public void setRecordIdSubfieldValue(@NonNull FieldValueCommand valueCommand, boolean reuseExistingTopfield, FieldTypeId topfieldTypeId, boolean reuseExistingSubfield, FieldTypeId subfieldTypeId, RecordEditation editation) {
        Field<?> field = reuseOrCreateField(reuseExistingTopfield, topfieldTypeId, editation);
        Field<?> subfield = reuseOrCreateField(reuseExistingSubfield, subfieldTypeId, field);

        FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), subfield.getFieldId(), valueCommand)
                .createMissingHierarchy();

        recordFieldEditor.editField(editation, command);
    }

    public void setStringTopFieldValue(@NonNull String value, FieldTypeId topfieldTypeId, RecordEditation editation) {
        if (!value.isBlank()) {
            Field<?> field = reuseOrCreateField(true, topfieldTypeId, editation);

            FieldEditationCommand command = FieldEditationCommand.of(editation.getRecord(), field.getFieldId(), new StringValueCommand(value))
                    .notCreateMissingHierarchy();
            recordFieldEditor.editField(editation, command);
        }
    }

    private static Field<?> reuseOrCreateField(boolean reuseExisting, FieldTypeId fieldTypeId, CreatableFieldContainer fieldContainer) {
        if (reuseExisting) {
            Optional<Field<?>> existing = fieldContainer.getFirstFieldByTypeId(fieldTypeId);
            if (existing.isPresent()) {
                return existing.get();
            }
        }
        return fieldContainer.createField(toEnd(fieldTypeId));
    }

}
