package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.commons.object.AddableAllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.AllValuesProvidedCodebook;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.fn.Formulas;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.value.FieldValueConverterFactory;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.NonNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cz.kpsys.portaro.record.detail.TestingFieldTypeFactory.*;

public class TestingFieldTypeLoader implements FieldTypeLoader {

    @NonNull Map<FieldTypeId, List<String>> complexDatafieldTypeIdToSubfieldCodesMap = new HashMap<>();
    @NonNull AddableAllValuesProvider<FieldType> customFields = new AddableAllValuesProvider<>();
    @NonNull AllValuesProvidedCodebook<FieldType, String> customFieldCodebook = AllValuesProvidedCodebook.ofIdentified(customFields);

    public TestingFieldTypeLoader withComplexDatafieldType(String typeId, List<String> subfieldCodes) {
        complexDatafieldTypeIdToSubfieldCodesMap.put(FieldTypeId.parse(typeId), subfieldCodes);
        return this;
    }

    public TestingFieldTypeLoader withCustomDatafieldType(String id, String nativeName) {
        FieldType datafieldType = datafieldType(FieldTypeId.parse(id), nativeName);
        customFields.add(datafieldType);
        return this;
    }

    public TestingFieldTypeLoader withCustomStandardSubfieldType(String id, String nativeName) {
        customFields.add(subfieldType(FieldTypeId.parse(id), nativeName, FieldGeneration.PHYSICAL, FieldExportSetting.toSubfield(FieldTypeId.parse(id))));
        return this;
    }

    @Override
    public List<FieldType> getAll() {
        return customFieldCodebook.getAll();
    }

    @Override
    public FieldType getSubfieldTypeById(FieldTypeId id) {
        return customFieldCodebook.findById(id.toString())
                .orElseGet(() -> standardSubfieldType(id));
    }

    @Override
    public FieldType getTopfieldTypeById(FieldTypeId id) {
        Optional<FieldType> custom = customFieldCodebook.findById(id.toString());
        if (custom.isPresent()) {
            return custom.get();
        }

        if (CodebookDelegatingFieldTypeLoader.CONTROLFIELD_NUMBERS.contains(id.getCode())) {
            return controlfieldType(id);
        }

        FieldType datafieldType = datafieldType(id);
        if (complexDatafieldTypeIdToSubfieldCodesMap.containsKey(id)) {
            List<String> subfieldCodes = complexDatafieldTypeIdToSubfieldCodesMap.get(id);
            Fond linkedRecordFond = Fond.testingPerson();

            FieldTypeId virtualGroupTypeId = id.sub(FieldTypes.VIRTUAL_GROUP_FIELD_CODE);
            Optional<Formula<?>> groupfieldFormula = Optional.of(Formulas.ref(LookupDefinition.ofSelfLinkNativeName()));
            SimpleFieldType virtualGroupType = new SimpleFieldType(virtualGroupTypeId, "Group " + virtualGroupTypeId, true, false, true, groupfieldFormula, FieldTypes.NO_CODEBOOK, FieldGeneration.PHYSICAL, FieldSource.common(), TransferType.NO_TRANSFER, FieldTypes.TEXT_DATATYPE, Optional.of(linkedRecordFond), FieldExportSetting.disabled(), _ -> FieldValueConverterFactory.createDefaultForSimpleSubfield());

            for (String subfieldCode : subfieldCodes) {
                FieldTypeId subfieldTypeId = virtualGroupTypeId.sub(subfieldCode);
                FieldTypeId configPath = id.sub(subfieldCode);
                String customEntrySubfieldCode = subfieldCode;
                Optional<Formula<?>> subfieldFormula = Optional.of(Formulas.ref(LookupDefinition.ofVirtualGroupToCustomEntrySubfield(virtualGroupTypeId, customEntrySubfieldCode)));
                SimpleFieldType subfieldType = new SimpleFieldType(subfieldTypeId, "Testing field %s".formatted(subfieldTypeId), false, true, true, subfieldFormula, FieldTypes.NO_CODEBOOK, FieldGeneration.GENERATED_WHOLE, FieldSource.common(), TransferType.OVERWRITE, FieldTypes.TEXT_DATATYPE, Optional.of(linkedRecordFond), FieldExportSetting.toSubfield(configPath), _ -> FieldValueConverterFactory.createDefaultForSimpleSubfield());
                virtualGroupType.addSubfieldType(subfieldType);
            }

            datafieldType.addSubfieldType(virtualGroupType);
        }
        return datafieldType;
    }

}
