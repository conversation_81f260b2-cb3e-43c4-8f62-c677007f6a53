package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.data.AuthorityFieldTypeEntity;
import cz.kpsys.portaro.record.detail.data.FieldTypeEntity;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.def.*;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.EmptyResultDataAccessException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;
import static cz.kpsys.portaro.record.detail.fn.Formulas.*;
import static cz.kpsys.portaro.record.detail.link.LookupDefinition.ofSelfRecordNativeName;
import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FieldTypeEntityToPrefabFieldTypeConverter implements Converter<List<? extends FieldTypeEntity>, List<PrefabFieldType>> {

    public static final String DECIMAL_NUMBER_PIC_VALUE = "DECIMAL";

    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<TransferType, Integer> transferTypeLoader = TransferType.CODEBOOK;
    @NonNull Provider<@NonNull TransferType> defaultTransferTypeProvider = TransferType.DEFAULT_PROVIDER;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;

    @Override
    public List<PrefabFieldType> convert(@NonNull List<? extends FieldTypeEntity> entities) {

        List<PrefabFieldType> nativeFieldTypes = ListUtil.convertStrict(entities, this::convertWithExceptionNesting);

        return enhanceNativeTypesWithGeneratedVirtualGroupTypes(nativeFieldTypes);
    }

    private @NonNull List<PrefabFieldType> enhanceNativeTypesWithGeneratedVirtualGroupTypes(List<PrefabFieldType> nativeFieldTypes) {
        List<PrefabFieldType> result = new ArrayList<>(nativeFieldTypes.size() * 2);
        ListCutter<PrefabFieldType> subfieldTypesCutter = ListCutter.ofCopyOf(nativeFieldTypes);

        while (subfieldTypesCutter.hasRemainingItems()) {
            PrefabFieldType prefabFieldType = subfieldTypesCutter.cutFirst();

            if (prefabFieldType.shouldHaveVirtualParent()) {
                FieldTypeId parentVirtualFieldTypeId = prefabFieldType.id().withCode(FieldTypes.VIRTUAL_GROUP_FIELD_CODE);

                List<PrefabFieldType> otherGroupedSiblings = subfieldTypesCutter.cut(prefabFieldType::shouldBeInSameVirtualGroup);
                List<PrefabFieldType> allGroupedSiblings = ListUtil.createNewListPrepending(prefabFieldType, otherGroupedSiblings);

                List<PrefabFieldType> modifiedGroupedSiblings = ListUtil.convertStrict(allGroupedSiblings, groupedSibling -> groupedSibling.withModifiedParentId(parentVirtualFieldTypeId));
                result.add(createParentVirtualFieldType(parentVirtualFieldTypeId, modifiedGroupedSiblings));
                result.addAll(modifiedGroupedSiblings);

            } else {
                result.add(prefabFieldType);
            }
        }

        return result;
    }

    private PrefabFieldType createParentVirtualFieldType(FieldTypeId parentVirtualFieldTypeId, @NotEmpty List<PrefabFieldType> subfieldTypes) {
        String joinedNativeName = subfieldTypes.stream().map(PrefabFieldType::nativeName).collect(Collectors.joining(", "));

        TransferType bestTransferType = Stream.of(TransferType.ADD, TransferType.OVERWRITE, TransferType.REWRITE_WHEN_CREATING, TransferType.DELETE_WHEN_EMPTY, TransferType.NO_TRANSFER)
                .filter(transferType -> subfieldTypes.stream().anyMatch(subfieldType -> subfieldType.transferType() == transferType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("This should not happen - any transfer type was not found"));

        Fond firstSubfieldLinkFond = Objects.requireNonNull(subfieldTypes.getFirst().linkRootFondConstraint(), () -> "Cannot create parent virtual field type, because subfield type " + subfieldTypes.getFirst() + " does not have link root fond.");
        LinkDef linkDef = new NativeNameLinkDef(); // TODO: asi by bylo lepsi udelat link ne na entry native subfield, ale proste na nazev

        List<FieldTypeId> subfieldsTargetExportFieldTypeIds = subfieldTypes.stream()
                .map(PrefabFieldType::fieldExportSetting)
                .filter(FieldExportSetting::isEnabled)
                .map(FieldExportSetting::getTargetFieldTypeId)
                .toList();
        FieldExportSetting exportSetting = FieldExportSetting.toSubfields(subfieldsTargetExportFieldTypeIds);

        return new PrefabFieldType(
                parentVirtualFieldTypeId,
                null,
                joinedNativeName,
                true,
                false,
                true,
                linkDef,
                FieldGeneration.PHYSICAL,
                FieldSource.common(),
                bestTransferType,
                firstSubfieldLinkFond,
                PrefabFieldType.NO_EXPLICIT_VALUE_DATATYPE,
                exportSetting
        );
    }

    private PrefabFieldType convertWithExceptionNesting(@NonNull FieldTypeEntity entity) {
        try {
            return convert(entity);
        } catch (Exception e) {
            throw new DataAccessException("Cannot load %s: %s".formatted(entity.getFieldTypeId(), e.getMessage()), entity.getFieldTypeId(), e);
        }
    }

    private PrefabFieldType convert(@NonNull FieldTypeEntity entity) {
        boolean inAuthorityRecord = entity instanceof AuthorityFieldTypeEntity;

        @NonNull String exportFieldNumber = String.valueOf(requireNonNull(entity.getExportFieldNumber(), "Exported field number (CIS_TAG) is null"));
        @NullableNotBlank String exportSubfieldCode = StringUtil.notBlankTrimmedString(entity.getExportSubfieldCode());
        @NonNull FieldTypeId typeId = FieldTypeId.parse(entity.getFieldTypeId());
        @NonNull FieldRecordLinkType recordLinkType = FieldRecordLinkType.CODEBOOK.getById(entity.getAuthorityType());
        @NullableNotBlank String pic = StringUtil.notBlankTrimmedString(entity.getPic());
        @NullableNotBlank String phraseGroupId = StringUtil.notBlankTrimmedString(entity.getPhraseGroupId());
        Fond linkedRecordFond = NumberUtil.emptyIfNullOrZero(entity.getLinkedRecordFond()).map(fondLoader::getById).orElse(null);

        Formula<?> formula = null;
        FieldTypeId specificLinkFieldTypeId = null;
        FieldTypeId linkedRecordFieldTypeId = null;
        boolean parentOfGenerated = false;

        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001019") || rootSerialCodeProvider.get().equals("100007001020") || rootSerialCodeProvider.get().equals("100007001021")) { // sutor prod + sutor test + sutor import


            /// RÁMEC - KDYŽ NEMÁM NADŘAZENOU POLOŽKU TAK JSEM RÁMEC JÁ, POKUD MÁM PŘEBÍRÁM RÁMEC Z NADŘAZENÉ POLOŽKY
            if (typeId.value().equals("d1001")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1001.a")) {
                recordLinkType = FieldRecordLinkType.FORMULA;

                formula = coalesce(
                        ref("d1002.main", "d1001.a"),
                        ref(ofSelfRecordNativeName())
                );
            }


            /// VÝPOČET OBCHODNÍCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ NÁKLADOVÉ CENY
            if (typeId.value().equals("d1650")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1650.c")) { // celkova nakladova cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                var ocekavanyNakladPrace = round2(orZero(multiply(
                        ref("d1610.main", "d2221.c"),
                        localRef("d1619.m"), // pocet hodin vykazanych
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaNakladSubdodavka = round2(orZero(multiply(
                        localRef("d1614.c"), // nakladova cena subdodavky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = sum(ocekavanyNakladPrace, celkovaNakladSubdodavka);
            }

            /// VÝPOČET OBCHODNÍCH POLOŽEK A JEJICH PŘEDPOKLÁDANÉ PRODEJNÍ CENY
            if (typeId.value().equals("d1651")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1651.c")) { // celkova prodejni cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                var ocekavanyProdejPrace = round2(orZero(multiply(
                        ref("d1620.main", "d2221.c"),
                        localRef("d1619.m"), // pocet hodin vykazanych
                        localRef("d1611.m") // mnozstvi
                )));

                var celkovaProdejSubdodavka = round2(orZero(multiply(
                        localRef("d1615.c"), // nakladova cena subdodavky
                        localRef("d1611.m") // mnozstvi
                )));

                formula = round2(sum(ocekavanyProdejPrace, celkovaProdejSubdodavka));
            }

            /// NAMAPOVANI FONDU Z KATALOGU CINNOSTI DO CENIKU
            if (typeId.value().equals("d2205")) {
                parentOfGenerated = true;
            }
            // LINK FOND
            // Preneseni informace o FONDU z Katalogu cinnosti profesí a strojů
            if (typeId.value().equals("d2205.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2200.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("fond");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO MODELAČNÍHO CENÍKU OBCHODNÍCH POLOŽEK NÁKLADU
            if (typeId.value().equals("d1610")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1610.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1610.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI)  DO MODELAČNÍHO CENÍKU OBCHODNÍCH POLOŽEK PRODEJE
            if (typeId.value().equals("d1620")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1620.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d1620.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU OSOBY
            if (typeId.value().equals("d2054")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2054.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO NÁKLADOVÝ CENÍKU STROJE
            if (typeId.value().equals("d2040")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2040.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU OSOBY
            if (typeId.value().equals("d2064")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2064.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2064.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// NAMAPOVANI CENIKU (KATALOGU CINNOSTI) DO PRODEJNÍHO CENÍKU STROJE
            if (typeId.value().equals("d2045")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2045.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2045.main");
                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
            }

            /// INLINE FUNKCE
            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2071")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2071.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojNaklad = round2(orZero(multiply(
                        ref("d2064.main", "d2221.c"),
                        localRef("d2070.o") // pocet jednotek vykazanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.c"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNaklad = round2(orZero(multiply(
                        ref("d2054.main", "d2221.d"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladVikend = round2(orZero(multiply(
                        ref("d2054.main", "d2221.e"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.m") // pocet hodin vykazanych
                )));
                var prescasOsobaNakladSvatek = round2(orZero(multiply(
                        ref("d2054.main", "d2221.f"),
                        localRef("d2070.n") // pocet hodin vykazanych prescasovych
                )));

                formula = conditional(ref("d2030.d", "d2116.o"),
                        sum(mnozstviOsobaNakladSvatek, prescasOsobaNakladSvatek, mnozstviStrojNaklad),
                        conditional(ref("d2030.d", "d2115.o"),
                                sum(mnozstviOsobaNakladVikend, prescasOsobaNakladVikend, mnozstviStrojNaklad),
                                sum(mnozstviOsobaNaklad, prescasOsobaNaklad, mnozstviStrojNaklad)));
            }

            /// VYPOCET nakladu a jejich vykazane nakladove ceny
            if (typeId.value().equals("d2081")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d2081.c")) { // Vykazana cena
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;

                /// Stroj
                var mnozstviStrojProdej = round2(orZero(multiply(
                        ref("d2045.main", "d2221.c"),
                        localRef("d2080.o") // pocet jednotek uctovanych stroje
                )));

                /// Pracovní den
                var mnozstviOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.c"),
                        localRef("d2080.m") // pocet hodin uctovanych
                )));
                var prescasOsobaProdej = round2(orZero(multiply(
                        ref("d2040.main", "d2221.d"),
                        localRef("d2080.n") // pocet hodin uctovanych prescasovych
                )));

                /// Víkend
                var mnozstviOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejVikend = round2(orZero(multiply(
                        ref("d2040.main", "d2221.e"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                /// Svátek
                var mnozstviOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.m") // pocet hodin vykazanych
                )));
                var prescasOsobaProdejSvatek = round2(orZero(multiply(
                        ref("d2040.main", "d2221.f"),
                        localRef("d2080.n") // pocet hodin vykazanych prescasovych
                )));

                formula = conditional(ref("d2030.d", "d2116.o"),
                        sum(mnozstviOsobaProdejSvatek, prescasOsobaProdejSvatek, mnozstviStrojProdej),
                        conditional(ref("d2030.d", "d2115.o"),
                                sum(mnozstviOsobaProdejVikend, prescasOsobaProdejVikend, mnozstviStrojProdej),
                                sum(mnozstviOsobaProdej, prescasOsobaProdej, mnozstviStrojProdej)));
            }

            /// AGREGACNI FUNKCE
            /// Celkový náklad v montážním deníku
            if (typeId.value().equals("d1641")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1641.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;
                formula = sum(backRef(FieldTypeId.parse("d2025.main"), FieldTypeId.parse("d2071.c")));
            }

            /// Celkový prodej v montážním deníku
            if (typeId.value().equals("d1642")) {
                parentOfGenerated = true;
            }
            if (typeId.value().equals("d1642.c")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;
                formula = sum(backRef(FieldTypeId.parse("d2025.main"), FieldTypeId.parse("d2081.c")));
            }
        }

        LinkDef linkDef = getLinkDef(
                recordLinkType,
                formula,
                specificLinkFieldTypeId,
                linkedRecordFieldTypeId,
                StringUtil.notBlankTrimmedString(entity.getLinkedRecordEntryFieldSubfieldCode())
        );

        return new PrefabFieldType(
                typeId,
                typeId,
                ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(entity.getName()), typeId.toString()),
                false,
                !entity.getNotRepeatable(),
                true,
                linkDef,
                getFieldValueGeneration(parentOfGenerated, recordLinkType),
                FieldSource.common(),
                getTransferType(entity),
                linkedRecordFond,
                getExplicitDatatype(typeId, recordLinkType, pic, phraseGroupId),
                typeId.isRoot()
                        ? getFieldExportSetting(inAuthorityRecord, exportFieldNumber)
                        : getSubfieldExportSetting(inAuthorityRecord, exportFieldNumber, exportSubfieldCode, typeId)
        );
    }

    private @NonNull FieldGeneration getFieldValueGeneration(boolean parentOfGenerated, FieldRecordLinkType recordLinkType) {
        if (parentOfGenerated) {
            return FieldGeneration.GENERATED_FIELD_PARENT;
        }
        return switch (recordLinkType) {
            case NO_LINK -> FieldGeneration.PHYSICAL;
            case THIS_FIELD_ENTRYFIELD_LINK -> FieldGeneration.GENERATED_VALUE;
            case LOCAL_RECORD, PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK, SPECIFIC_FIELD_LINK, FORMULA -> FieldGeneration.GENERATED_WHOLE;
        };
    }

    private @NonNull LinkDef getLinkDef(@NonNull FieldRecordLinkType recordLinkType,
                                        @Nullable Formula<?> formula,
                                        @Nullable FieldTypeId specificLinkFieldTypeId,
                                        @Nullable FieldTypeId linkedRecordFieldTypeId,
                                        @Nullable String linkedRecordEntryFieldSubfieldCode) {
        return switch (recordLinkType) {
            case NO_LINK -> new NoLinkDef();
            case FORMULA -> new FormulaDef<>(requireNonNull(formula, () -> "Record link type defined as %s but formula is null".formatted(recordLinkType)));
            case LOCAL_RECORD -> new SelfRecordDef(requireNonNull(linkedRecordFieldTypeId));
            case SPECIFIC_FIELD_LINK -> new SpecificFieldLinkDef(requireNonNull(specificLinkFieldTypeId), requireNonNull(linkedRecordFieldTypeId));
            case PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK -> new ParentVirtualGroupToCustomEntrySubfieldLinkDef(requireNonNull(linkedRecordEntryFieldSubfieldCode, "Record link type defined as complex-relation but linked record entry field subfield code is null"));
            case THIS_FIELD_ENTRYFIELD_LINK -> new SimpleEntryCustomSubfieldLinkDef(requireNonNull(linkedRecordEntryFieldSubfieldCode, "Record link type defined as simple-relation but linked record entry field subfield code is null"));
        };
    }

    private FieldExportSetting getFieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber) {
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toField(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber));
    }

    private FieldExportSetting getSubfieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber, @Nullable String exportSubfieldCode, @NonNull FieldTypeId subfieldTypeId) {
        String resolvedCode = ObjectUtil.firstNotNull(exportSubfieldCode, subfieldTypeId.getCode());
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT) || resolvedCode.equals(PrefabFieldType.TARGET_EXPORT_SUBFIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toSubfield(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber).sub(resolvedCode));
    }

    private TransferType getTransferType(FieldTypeEntity dto) {
        try {
            return transferTypeLoader.getById(dto.getTransferTypeId());
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            return defaultTransferTypeProvider.get();
        }
    }

    private @Nullable ScalarDatatype getExplicitDatatype(@NonNull FieldTypeId typeId, @NonNull FieldRecordLinkType recordLinkType, @NullableNotBlank String pic, @NullableNotBlank String phraseGroupId) {
        if (pic != null && phraseGroupId != null) {
            log.warn("Invalid fdef[aut] configuration for field {}: both pic ('{}') and fk_frazeskup ('{}') filled", typeId, pic, phraseGroupId);
        }

        if (recordLinkType != FieldRecordLinkType.NO_LINK) {
            if (pic != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: pic filled ('{}') when auttyp is {} ({})", typeId, pic, recordLinkType.getId(), recordLinkType.name());
            }
            if (phraseGroupId != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: fk_frazeskup filled ('{}') when auttyp is {} ({})", typeId, phraseGroupId, recordLinkType.getId(), recordLinkType.name());
            }
            return null;
        }

        if (pic != null) {
            if (pic.equals(DECIMAL_NUMBER_PIC_VALUE)) {
                return NUMBER_DECIMAL_2;
            }
            return Datatype.scalar(pic);
        }
        if (phraseGroupId != null) {
            return Datatype.scalar(DATATYPE_PREFIX_PHRASE + phraseGroupId);
        }
        return TEXT;
    }

}
