package cz.kpsys.portaro.record.detail.link;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.load.*;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.record.detail.link.LinkFieldTargetSearchMode.VIRTUAL_GROUP_FIELD_LINK;

public record LinkFieldSpec(

        @Nullable
        FieldTypeId fieldTypeId,

        @NonNull
        LinkFieldTargetSearchMode linkFieldSearchMode

) {

    @NonNull
    public static LinkFieldSpec ofSelfLink() {
        return new LinkFieldSpec(null, LinkFieldTargetSearchMode.SELF_LINK);
    }

    @NonNull
    public static LinkFieldSpec ofVirtualGroupField(@NonNull FieldTypeId virtualGroupTypeId) {
        return new LinkFieldSpec(virtualGroupTypeId, VIRTUAL_GROUP_FIELD_LINK);
    }

    @NonNull
    public static LinkFieldSpec ofSpecificFieldLink(@NonNull FieldTypeId linkFieldTypeId) {
        return new LinkFieldSpec(linkFieldTypeId, LinkFieldTargetSearchMode.SPECIFIC_FIELD_LINK);
    }

    @NonNull
    public static LinkFieldSpec ofSelfRecord() {
        return new LinkFieldSpec(null, LinkFieldTargetSearchMode.SELF_RECORD);
    }

    @NonNull
    public FieldTypeId existingFieldTypeId() {
        return Objects.requireNonNull(fieldTypeId, () -> "When " + linkFieldSearchMode + " used, field type id must be specified");
    }

    @NonNull
    public RecordFieldId toLinkFieldSpecOnSpecifiedField(@NonNull RecordFieldId sourceRecordFieldId) {
        return switch (linkFieldSearchMode) {
            case SELF_LINK, SELF_RECORD -> sourceRecordFieldId;
            case VIRTUAL_GROUP_FIELD_LINK -> {
                RecordFieldId parentRecordFieldId = sourceRecordFieldId.existingParentFieldId();
                Assert.state(parentRecordFieldId.fieldId().getFieldTypeId().equals(existingFieldTypeId()), () -> "When " + linkFieldSearchMode + " used, parent of source field must be same as field type in link target (fieldTypeId=" + existingFieldTypeId() + ", sourceField=" + sourceRecordFieldId + ")");
                yield parentRecordFieldId;
            }
            case SPECIFIC_FIELD_LINK -> RecordSpec.ofRecordAndField(sourceRecordFieldId.recordIdFondPair(), existingFieldTypeId().toFieldIdWithAllFirstIndices());
        };
    }

    @NonNull
    public RecordLinksLookup lookup(@NonNull ValuableFieldNode sourceNode, @NonNull LoadedRecords loadedRecords) {
        return switch (linkFieldSearchMode) {
            case SELF_RECORD -> RecordLinksLookup.of(sourceNode.record());
            case SELF_LINK -> {
                if (!(sourceNode.id() instanceof RecordFieldId sourceFieldId)) {
                    throw new IllegalArgumentException("When " + linkFieldSearchMode + " used, source datable id must be of type RecordFieldId, but it is " + sourceNode.id().getClass().getSimpleName() + " (" + sourceNode.id() + ", this link field spec is " + this + ")");
                }
                yield getRecordLink(sourceNode.record(), sourceFieldId, loadedRecords);
            }
            case VIRTUAL_GROUP_FIELD_LINK -> {
                RecordFieldId parentFieldId = sourceNode.id().existingParentFieldId(); // if sourceFieldId is "d164sa1b:d100#0.main#0.a#0", parentFieldId will be "d164sa1b:d100#0.main#0". If sourceFieldId is "d164sa1b:d100#0.main#0.a", parentFieldId will be "d164sa1b:d100#0.main#0"
                RecordIdFondPair parentFieldRecordIdFondPair = sourceNode.record(); // zatim pouzijeme recordIdFondPair z aktualniho pole, ale lepsi by bylo to vzit z parentFieldId
                yield getRecordLink(parentFieldRecordIdFondPair, parentFieldId, loadedRecords);
            }
            case SPECIFIC_FIELD_LINK -> {
                List<Multifield> linkMultifield = loadedRecords.findMultifieldsByFieldTypeId(sourceNode.record(), existingFieldTypeId());
                if (linkMultifield.isEmpty()) {
                    yield RecordLinksLookup.ofMissingLinkField(RecordFieldId.of(sourceNode.record(), existingFieldTypeId().toFieldIdWithAllFirstIndices()));
                }
                Set<RecordFieldId> linkMissingFields = linkMultifield.stream()
                        .flatMap(multifield -> multifield.stream().filter(fieldSlot -> !fieldSlot.hasRecordLink()))
                        .map(FieldSlot::id)
                        .collect(Collectors.toSet());
                if (!linkMissingFields.isEmpty()) {
                    yield RecordLinksLookup.ofLinkMissingFields(linkMissingFields);
                }
                yield RecordLinksLookup.of(linkMultifield.stream().flatMap(multifield -> multifield.stream().map(FieldSlot::getExistingRecordLink)).toList());
            }
        };
    }

    private @NonNull RecordLinksLookup getRecordLink(RecordIdFondPair linkFieldRecordIdFondPair, RecordFieldId linkFieldId, @NonNull LoadedRecords loadedRecords) {
        Optional<FieldSlot> linkField = loadedRecords.findSingleByRecordFieldId(linkFieldRecordIdFondPair, linkFieldId);
        if (linkField.isEmpty()) {
            return RecordLinksLookup.ofMissingLinkField(linkFieldId);
        }
        if (!linkField.get().hasRecordLink()) {
            return RecordLinksLookup.ofLinkMissingField(linkFieldId);
        }
        return RecordLinksLookup.of(linkField.get().getExistingRecordLink());
    }

    @Override
    public String toString() {
        return switch (linkFieldSearchMode) {
            case SELF_RECORD -> "{self}:record";
            case SELF_LINK -> "{self}:link";
            case VIRTUAL_GROUP_FIELD_LINK, SPECIFIC_FIELD_LINK -> existingFieldTypeId() + ":link";
        };
    }
}
