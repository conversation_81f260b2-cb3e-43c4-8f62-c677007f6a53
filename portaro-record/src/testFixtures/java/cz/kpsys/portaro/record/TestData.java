package cz.kpsys.portaro.record;

import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.edit.AllPassingTestingFieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.prop.RecordPropertiesGenerators;
import cz.kpsys.portaro.record.prop.RecordPropertyKeys;

import java.util.UUID;

import static cz.kpsys.portaro.record.detail.FieldTypeId.top;

public class TestData {

    static FieldTypeLoader fieldTypeLoader = new TestingFieldTypeLoader()
            .withCustomDatafieldType("d222", "Pole222")
            .withCustomStandardSubfieldType("d222.a", "Hlavní název")
            .withCustomDatafieldType("d245", "Název")
            .withCustomStandardSubfieldType("d245.a", "Hlavní název")
            .withCustomStandardSubfieldType("d245.b", "Další údaje o názvu")
            .withCustomStandardSubfieldType("d245.c", "Údaj o odpovědnosti atd.")
            .withCustomDatafieldType("d650", "Klíčová slova")
            .withCustomStandardSubfieldType("d650.a", "Věcné téma")
            .withCustomStandardSubfieldType("d650.x", "Všeobecné zpřesnění");
    static FieldTypesByFondLoader editableFieldTypesByFondLoader = new AllPassingTestingFieldTypesByFondLoader(fieldTypeLoader);

    public static Record createFullRecord() {
        Fond fond = Fond.testingMonography();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UUID.fromString("f9185935-59a1-45a8-9f11-83f180bd03a6"), fond);

        MarcXmlToDetailConverterImpl converter = MarcXmlToDetailConverterImpl.testing(fond);
        FieldContainer detail = converter.convertSingle("""
                <collection xmlns="http://www.loc.gov/MARC21/slim">
                    <record status="3" id="304368" record_id="f9185935-59a1-45a8-9f11-83f180bd03a6" fond="1">
                      <leader>-----nam-a22-----1a-4500</leader>
                      <controlfield tag="001">kpm01304368</controlfield>
                      <controlfield tag="003">CZ-PaUK</controlfield>
                      <controlfield tag="005">20050816134808.0</controlfield>
                      <controlfield tag="007">ta</controlfield>
                      <controlfield tag="008">050714s2005    xr ||||||||||#u0|0||cze||</controlfield>
                      <datafield tag="015" ind1="#" ind2="#">
                        <subfield code="a">cnb000085030</subfield>
                      </datafield>
                      <datafield tag="020" ind1="#" ind2="#">
                        <subfield code="a">80-85955-30-X (váz.)</subfield>
                      </datafield>
                      <datafield tag="020" ind1="#" ind2="#">
                        <subfield code="a">80-85955-25-3 (váz.)</subfield>
                      </datafield>
                      <datafield tag="020" ind1="#" ind2="#">
                        <subfield code="a">978020137962</subfield>
                      </datafield>
                      <datafield tag="022" ind1="0" ind2="#">
                        <subfield code="a">0046-225X</subfield>
                        <subfield code="y">0046-2254</subfield>
                      </datafield>
                      <datafield tag="024" ind1="#" ind2="#">
                        <subfield code="a">9780201379624</subfield>
                      </datafield>
                      <datafield tag="024" ind1="7" ind2="#">
                        <subfield code="a">urn:nbn:cz:aba012-00009y</subfield>
                        <subfield code="2">urn</subfield>
                      </datafield>
                      <datafield tag="035" ind1="#" ind2="#">
                        <subfield code="a">(OCoLC)51184532</subfield>
                      </datafield>
                      <datafield tag="040" ind1="#" ind2="#">
                        <subfield code="a">PAD001</subfield>
                        <subfield code="b">cze</subfield>
                        <subfield code="e">AACR2</subfield>
                        <subfield code="e">AACR2</subfield>
                      </datafield>
                      <datafield tag="041" ind1="0" ind2="#">
                        <subfield code="a">cze</subfield>
                      </datafield>
                      <datafield tag="044" ind1="#" ind2="#">
                        <subfield code="a">xr</subfield>
                      </datafield>
                      <datafield tag="072" ind1="#" ind2="7">
                        <subfield code="a" fond_id="59" record_id="f8a6c358-0aad-4971-8f8d-c7e378569792">929.5/.9</subfield>
                        <subfield code="x" fond_id="59" record_id="f8a6c358-0aad-4971-8f8d-c7e378569792">Genealogie. Heraldika. Šlechta. Vlajky</subfield>
                        <subfield code="2" fond_id="59" record_id="f8a6c358-0aad-4971-8f8d-c7e378569792">Konspekt</subfield>
                      </datafield>
                      <datafield tag="080" ind1="#" ind2="#">
                        <subfield code="a" fond_id="49" record_id="8dd9263f-d127-4038-b7df-b0cfbf5fafa5">929.52</subfield>
                        <subfield code="2">MRF</subfield>
                      </datafield>
                      <datafield tag="080" ind1="#" ind2="#">
                        <subfield code="a" fond_id="49" record_id="16542edb-75cc-4b30-89a0-c57039fa7990">929.7</subfield>
                        <subfield code="2">MRF</subfield>
                      </datafield>
                      <datafield tag="100" ind1="1" ind2="#">
                        <subfield code="a" fond_id="31" record_id="8bbaa13a-bd1b-462b-af53-5c67ab84ff28">Buben, Milan</subfield>
                        <subfield code="d" fond_id="31" record_id="8bbaa13a-bd1b-462b-af53-5c67ab84ff28">1946-</subfield>
                        <subfield code="7" fond_id="31" record_id="8bbaa13a-bd1b-462b-af53-5c67ab84ff28">jn19981000346</subfield>
                      </datafield>
                      <datafield tag="245" ind1="1" ind2="0">
                        <subfield code="a">Almanach českých šlechtických a rytířských rodů [2005] /</subfield>
                        <subfield code="c">[Milan M. Buben, Karel Vavřínek ; ilustrovali Milan M. Buben a Julie Bubnová-Mühlová]</subfield>
                      </datafield>
                      <datafield tag="260" ind1="#" ind2="#">
                        <subfield code="a">Praha :</subfield>
                        <subfield code="b">Martin,</subfield>
                        <subfield code="c">2005</subfield>
                      </datafield>
                      <datafield tag="300" ind1="#" ind2="#">
                        <subfield code="a">213 s. :</subfield>
                        <subfield code="b">erby ;</subfield>
                        <subfield code="c">17 cm</subfield>
                      </datafield>
                      <datafield tag="500" ind1="#" ind2="#">
                        <subfield code="a">V tiráži uveden rok vydání 2004</subfield>
                      </datafield>
                      <datafield tag="504" ind1="#" ind2="#">
                        <subfield code="a">Obsahuje bibliografické odkazy</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="d4b025d1-229e-4743-bcf1-68a112550df9">biografie</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="33adb60c-2d31-4dac-911c-5a1f6a507551">genealogie</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="f2152d25-de10-4760-9ca0-dd56718da393">šlechtické rody</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="75e7eca7-60d2-4f88-ad0c-9b4356cb5454">rytířství</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="59498907-67f8-4a6e-878e-043d5e32f900">české země</subfield>
                      </datafield>
                      <datafield tag="650" ind1="1" ind2="4">
                        <subfield code="a" fond_id="51" record_id="c5520813-4c9b-400e-8cad-8cb72510110c">přehledy</subfield>
                      </datafield>
                      <datafield tag="700" ind1="1" ind2="#">
                        <subfield code="a" fond_id="31" record_id="d62d9f80-d2c1-425d-a6dc-fa96d3eec6d5">Vavřínek, Karel</subfield>
                        <subfield code="7" fond_id="31" record_id="d62d9f80-d2c1-425d-a6dc-fa96d3eec6d5">xx0031215</subfield>
                      </datafield>
                      <datafield tag="900" ind1=" " ind2=" ">
                        <subfield code="a">PAD001</subfield>
                      </datafield>
                      <datafield tag="910" ind1="#" ind2="#">
                        <subfield code="a">PAD001</subfield>
                        <subfield code="b">64240-05</subfield>
                      </datafield>
                      <datafield tag="1100" ind1=" " ind2=" ">
                        <subfield code="a">20050714d2005####m##y0czey0103####ba</subfield>
                      </datafield>
                      <datafield tag="980" ind1=" " ind2=" ">
                        <subfield code="s">available</subfield>
                      </datafield>
                    </record>
                </collection>""");

        return new Record(recordIdFondPair, 1000, null, false, null, detail, ObjectProperties.empty());
    }

    public static Record createEmptyRecord() {
        Fond fond = Fond.testingMonography();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UUID.fromString("f9185935-59a1-45a8-9f11-83f180bd03a7"), fond);
        return new Record(recordIdFondPair, 1000, null, false, null, new SimpleFieldContainer(), ObjectProperties.empty());
    }

    public static Record createFloricultureDocument() {
        UUID id = UUID.fromString("f9185935-59a1-45a8-9f11-83f180bd03a6");
        Fond fond = Fond.testingMonography();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(id, fond);
        FieldTypesByFondLoader.StaticFondLoader monographyFieldTypeLoader = editableFieldTypesByFondLoader.toStaticLoader(fond);

        EditableFieldType fieldType222 = monographyFieldTypeLoader.getTopfieldTypeById(top("d222"));
        EditableFieldType fieldType222a = monographyFieldTypeLoader.getSubfieldTypeById(top("d222").sub("a"));
        EditableFieldType fieldType245 = monographyFieldTypeLoader.getTopfieldTypeById(top("d245"));
        EditableFieldType fieldType245a = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("a"));
        EditableFieldType fieldType245b = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("b"));
        EditableFieldType fieldType245c = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("c"));
        EditableFieldType fieldType650 = monographyFieldTypeLoader.getTopfieldTypeById(top("d650"));
        EditableFieldType fieldType650a = monographyFieldTypeLoader.getSubfieldTypeById(top("d650").sub("a"));
        EditableFieldType fieldType650x = monographyFieldTypeLoader.getSubfieldTypeById(top("d650").sub("x"));

        FieldContainer detail = new SimpleFieldContainer();

        Field<?> df = fieldType245.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType245a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("Floriculture :"))));
        df.add(fieldType245b.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" designing & merchandising /"))));
        df.add(fieldType245c.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" Charles Griner."))));
        detail.add(df);

        df = fieldType650.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType650a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("Flower arrangement"))));
        detail.add(df);

        df = fieldType650.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType650a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("<<Florists"), RecordIdFondPair.of("59669182-f20c-4a3f-ab5c-742a30e0cf24", Fond.testingTopic()))));
        df.add(fieldType650x.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" Vocational guidance."), RecordIdFondPair.of("59669182-f20c-4a3f-ab5c-742a30e0cf24", Fond.testingTopic()))));
        detail.add(df);

        df = fieldType222.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType222a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("http://nevim.cz/abc"))));
        detail.add(df);

        ObjectProperties props = ObjectProperties.of(RecordPropertiesGenerators.createForDocumentByDetail(detail));
        return new Record(recordIdFondPair, 1000, null, false, props.get(RecordPropertyKeys.NAME), detail, props);
    }

    public static Record createSimpleRecord() {
        UUID id = UUID.fromString("b51522bb-37a6-42b0-8b6c-2464e2747f71");
        Fond fond = Fond.testingMonography();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(id, fond);
        FieldTypesByFondLoader.StaticFondLoader monographyFieldTypeLoader = editableFieldTypesByFondLoader.toStaticLoader(fond);

        EditableFieldType fieldType84 = monographyFieldTypeLoader.getTopfieldTypeById(top("d84"));
        EditableFieldType fieldType84a = monographyFieldTypeLoader.getSubfieldTypeById(top("d84").sub("a"));
        EditableFieldType fieldType84c = monographyFieldTypeLoader.getSubfieldTypeById(top("d84").sub("c"));
        EditableFieldType fieldType222 = monographyFieldTypeLoader.getTopfieldTypeById(top("d222"));
        EditableFieldType fieldType222a = monographyFieldTypeLoader.getSubfieldTypeById(top("d222").sub("a"));
        EditableFieldType fieldType245 = monographyFieldTypeLoader.getTopfieldTypeById(top("d245"));
        EditableFieldType fieldType245a = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("a"));
        EditableFieldType fieldType245b = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("b"));
        EditableFieldType fieldType245c = monographyFieldTypeLoader.getSubfieldTypeById(top("d245").sub("c"));
        EditableFieldType fieldType650 = monographyFieldTypeLoader.getTopfieldTypeById(top("d650"));
        EditableFieldType fieldType650a = monographyFieldTypeLoader.getSubfieldTypeById(top("d650").sub("a"));
        EditableFieldType fieldType650x = monographyFieldTypeLoader.getSubfieldTypeById(top("d650").sub("x"));

        Record record = RecordFactory.testingMonography(id, 1000);
        FieldContainer detail = new SimpleFieldContainer();
        record.setDetail(detail);

        Field<?> df = fieldType245.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType245a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("Floriculture :"))));
        df.add(fieldType245b.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" designing & merchandising /"))));
        df.add(fieldType245c.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" Charles Griner."))));
        detail.add(df);

        df = fieldType650.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType650a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("Flower arrangement"))));
        detail.add(df);
        df = fieldType650.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType650a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("<<Florists"), RecordIdFondPair.of("59669182-f20c-4a3f-ab5c-742a30e0cf24", Fond.testingTopic()))));
        df.add(fieldType650x.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of(" Vocational guidance."), RecordIdFondPair.of("59669182-f20c-4a3f-ab5c-742a30e0cf24", Fond.testingTopic()))));
        detail.add(df);

        //222[0].a
        df = fieldType222.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType222a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("http://nevim.cz/abc"))));
        detail.add(df);

        //84[0].a
        df = fieldType84.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType84a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("ab"))));
        detail.add(df);
        //84[1].a
        df = fieldType84.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType84a.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("cd"))));
        detail.add(df);
        //84[2].c
        df = fieldType84.createEmptyFieldByParentId(recordIdFondPair, null, UuidGenerator.forIdentifier());
        df.add(fieldType84c.createFieldByParentId(recordIdFondPair, df.getFieldId(), UuidGenerator.forIdentifier(), FieldPayload.of(StringFieldValue.of("ef"))));
        detail.add(df);

        return record;
    }

}
