plugins {
    `java-test-fixtures`
}

dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")
    testImplementation("org.mockito:mockito-core:+")

    testImplementation(project(":portaro-test-environment"))
    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testImplementation("org.junit.jupiter:junit-jupiter-params:+")
    testImplementation("org.hamcrest:hamcrest-library:+")
    testImplementation("org.assertj:assertj-core:+")
    testImplementation("org.xmlunit:xmlunit-core:+")
    testImplementation("org.xmlunit:xmlunit-matchers:+")
    testImplementation("org.apache.commons:commons-lang3:3.+")
    testImplementation("org.springframework:spring-web:6.+")

    testFixturesCompileOnly("org.projectlombok:lombok:+")
    testFixturesAnnotationProcessor("org.projectlombok:lombok:+")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-appserver"))
    implementation(project(":portaro-auth"))
    api(project(":portaro-commons"))
    implementation(project(":portaro-commons-image"))
    api(project(":portaro-core"))
    implementation(project(":portaro-database-structure"))
    implementation(project(":portaro-export"))
    implementation(project(":portaro-file"))
    implementation(project(":portaro-form"))
    implementation(project(":portaro-form-annotation"))
    implementation(project(":portaro-form-config"))
    implementation(project(":portaro-marcxml"))
    implementation(project(":portaro-search"))
    implementation(project(":portaro-security"))
    implementation(project(":portaro-sql-generator"))
    implementation(project(":portaro-user"))
    implementation(project(":portaro-web"))
    implementation(project(":portaro-marcxml"))

    api("org.springframework:spring-core:6.+")
    implementation("org.springframework:spring-web:6.+")
    implementation("org.springframework:spring-jdbc:6.+")
    implementation("org.springframework.security:spring-security-core:6.+")
    implementation("org.springframework.data:spring-data-jpa:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.+")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.+")
    implementation("org.jdom:jdom2:2.+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("org.hibernate.orm:hibernate-core:6.5.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
    implementation("org.apache.commons:commons-lang3:3.+")
    implementation("commons-io:commons-io:2.15.+") //no problem to update to higher version, this is only not to use version 2.2
    implementation("com.google.guava:guava:+")
    implementation("io.swagger.core.v3:swagger-annotations-jakarta:+")
}
