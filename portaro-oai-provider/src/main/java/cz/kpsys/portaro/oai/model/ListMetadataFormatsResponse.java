package cz.kpsys.portaro.oai.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.NonNull;

import java.util.List;

public record ListMetadataFormatsResponse(

        @JacksonXmlProperty(localName = "metadataFormat", namespace = "http://www.openarchives.org/OAI/2.0/")
        @JacksonXmlElementWrapper(useWrapping = false)
        @NonNull
        List<MetadataFormatResponse> metadataFormats

) implements OaiVerbResponse {}
