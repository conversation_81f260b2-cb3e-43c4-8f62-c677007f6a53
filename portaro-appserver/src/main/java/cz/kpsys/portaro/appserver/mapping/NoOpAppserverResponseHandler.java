package cz.kpsys.portaro.appserver.mapping;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import org.jdom2.Document;


@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class NoOpAppserverResponseHandler<UNUSED> implements AppserverResponseHandler<UNUSED> {

    public static <UNUSED> NoOpAppserverResponseHandler<UNUSED> create() {
        return new NoOpAppserverResponseHandler<>();
    }

    @Override
    public UNUSED mapResponse(Document d) {
        return null;
    }
    
}
