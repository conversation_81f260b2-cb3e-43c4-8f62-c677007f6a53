package cz.kpsys.portaro.view.web.rest.record;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.SimilarRecordsLoader;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Locale;

@Hidden // TODO: zdokumentovat Swaggerem a odstranit anotaci
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.RECORDS_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SimilarDocumentsApiController extends GenericApiController {
    
    @NonNull SimilarRecordsLoader similarRecordsLoader;
    @NonNull ViewableItemsTypedConverter<Record, ViewableRecord> recordsToViewableRecordsConverter;
    @NonNull Provider<Integer> similarMaxCountProvider;
    
    @GetMapping("/{id}/similar")
    public List<ViewableRecord> getAllByDocument(@PathVariable("id") Record record,
                                                 @CurrentDepartment Department ctx,
                                                 UserAuthentication currentAuth,
                                                 Locale locale) {
        List<Record> records = similarRecordsLoader.getAllByRecord(record, ctx, similarMaxCountProvider.get());
        return recordsToViewableRecordsConverter.convert(records, currentAuth, ctx, locale);
    }

}
