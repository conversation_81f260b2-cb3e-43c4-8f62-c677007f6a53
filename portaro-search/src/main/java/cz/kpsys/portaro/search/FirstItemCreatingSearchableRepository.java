package cz.kpsys.portaro.search;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.function.Consumer;


@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FirstItemCreatingSearchableRepository<P extends SearchParams, ITEM extends Identified<ID>, ID> implements SearchableRepository<P, ITEM, ID> {

    @NonNull SearchableRepository<P, ITEM, ID> delegate;
    @NonNull Provider<ITEM> firstItemProvider;
    @NonNull SearchParamsPredicate<P> parameterCondition;

    @Override
    public ITEM getById(@NonNull ID id) throws ItemNotFoundException {
        return delegate.getById(id);
    }

    @Override
    public void delete(ITEM item) {
        delegate.delete(item);
    }

    @Override
    public @NonNull ITEM save(@NonNull ITEM item) {
        return delegate.save(item);
    }

    @Override
    public Chunk<ITEM, RangePaging> getPage(RangePaging paging, SortingItem customSorting, P params) {
        if (parameterCondition.test(params, paging.range()) && delegate.getTotalElements(params) == 0) {
            return Chunk.ofDefinitelyFinal(List.of(firstItemProvider.get()));
        } else {
            return delegate.getPage(paging, customSorting, params);
        }
    }

    @Override
    public int getTotalElements(P params) {
        var totalElements = delegate.getTotalElements(params);
        if (parameterCondition.test(params) && totalElements == 0) {
            return 1; // first item (to be created)
        }
        return totalElements;
    }

    @Override
    public List<ITEM> getContent(RangePaging paging, SortingItem customSorting, Consumer<P> paramsModifier) {
        if (parameterCondition.test(paramsModifier, paging.range()) && delegate.getTotalElements(paramsModifier) == 0) {
            return List.of(firstItemProvider.get());
        }
        return delegate.getContent(paging, customSorting, paramsModifier);
    }

    @Override
    public int getTotalElements(Consumer<P> paramsModifier) {
        var totalElements = delegate.getTotalElements(paramsModifier);
        if (parameterCondition.test(paramsModifier) && totalElements == 0) {
            return 1; // first item (to be created)
        }
        return totalElements;
    }

    @Override
    public List<ITEM> getAllByIds(@NonNull List<ID> ids) {
        return delegate.getAllByIds(ids);
    }
}
