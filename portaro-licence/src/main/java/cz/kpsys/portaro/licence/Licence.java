package cz.kpsys.portaro.licence;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.Nullable;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cz.kpsys.portaro.licence.EnumeratedBusinessVersion.V2_2;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Licence {

    @Getter
    @NullableNotBlank
    String rootUrl;

    @Getter
    boolean testing;

    @NonNull
    Map<Module, Boolean> moduleStates;

    @Getter
    @Nullable
    Integer editorLimit;

    @Getter
    @Nullable
    Integer documentLimit;

    @Getter
    @NonNull
    BusinessVersion licenceVersion;

    @Getter
    @Nullable
    LocalDate maintenanceExpirationDate;


    public static Licence createTestingDisabled() {
        return new Licence(null, false, Map.of(), 0, 0, V2_2, null);
    }

    public boolean isEnabled(Module module) {
        return moduleStates.getOrDefault(module, false);
    }

    public List<Module> getEnabledModules() {
        return Arrays.stream(Module.values())
                .filter(this::isEnabled)
                .toList();
    }


    @Override
    public String toString() {
        String enabledModules = getEnabledModules().stream()
                .map(Module::getName)
                .collect(Collectors.joining(","));
        return "Version %s, rootUrl %s, testing %s, maintenance to %s, editorLimit %s, documentLimit %s, enabled modules: %s"
                .formatted(getLicenceVersion(), getRootUrl(), isTesting(), getMaintenanceExpirationDate(), getEditorLimit(), getDocumentLimit(), enabledModules);
    }

}
