package cz.kpsys.portaro.ext.cpk.impl;

import cz.kpsys.portaro.FieldsNotFilledValidationException;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.cpk.ContextualCpkClient;
import cz.kpsys.portaro.ext.cpk.CpkSearchFieldValuePair;
import cz.kpsys.portaro.ext.cpk.model.CpkRecord;
import cz.kpsys.portaro.ext.cpk.model.CpkSearchResult;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.isbn.Isbn;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.sorting.SortingItem;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CpkSearch extends AbstractStandardSearch<MapBackedParams, CpkRecord, RangePaging> {

    @NonNull ContextualCpkClient<Department> contextualCpkClient;
    boolean forZiskejSupportingRecordsOnly;

    @Override
    public InternalSearchResult<CpkRecord, MapBackedParams, RangePaging> loadResult(RangePaging paging, SortingItem sorting, MapBackedParams completeParams, Department ctx, CacheMode cacheMode) {
        List<CpkSearchFieldValuePair<?>> fieldValuePairs = getSearchFieldAndValue(completeParams);
        if (fieldValuePairs == null) {
            return InternalSearchResult.empty(paging);
        }

        CpkSearchResult result = contextualCpkClient.search(ctx, fieldValuePairs);

        return new InternalSearchResult<>(Chunk.of(result.records(), paging.incrementPage()), Math.toIntExact(result.resultCount()), paging, List.of(), null);
    }

    @Nullable
    private List<CpkSearchFieldValuePair<?>> getSearchFieldAndValue(MapBackedParams p) throws FieldsNotFilledValidationException {
        List<CpkSearchFieldValuePair<?>> res = new ArrayList<>();

        if (p.has(CoreSearchParams.Q)) {
            String value = p.get(CoreSearchParams.Q);
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.WHATEVER, value));
        }

        if (p.has(RecordConstants.SearchParams.ISBN)) {
            String value = p.get(RecordConstants.SearchParams.ISBN);
            if (!IsbnChecker.isValidIsbn(value)) {
                return null;
            }
            String normalizedIsbn = new Isbn(value).getNormalizedValue();
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.ISBN, normalizedIsbn));
        }

        if (p.has(RecordConstants.SearchParams.ISSN)) {
            String value = p.get(RecordConstants.SearchParams.ISSN);
            if (!IsbnChecker.isValidIssn(value)) {
                return null;
            }
            String normalizedIssn = new Isbn(value).getNormalizedValue();
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.ISSN, normalizedIssn));
        }

        if (p.has(RecordConstants.SearchParams.ISBN_OR_ISSN)) {
            String value = p.get(RecordConstants.SearchParams.ISBN_OR_ISSN);
            if (IsbnChecker.isValidIsbn(value)) {
                String normalizedIsbn = new Isbn(value).getNormalizedValue();
                res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.ISBN, normalizedIsbn));
            } else if (IsbnChecker.isValidIssn(value)) {
                String normalizedIssn = new Isbn(value).getNormalizedValue();
                res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.ISSN, normalizedIssn));
            } else {
                return null;
            }
        }

        if (p.has(CoreSearchParams.NAME)) {
            String value = p.get(CoreSearchParams.NAME);
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.TITLE, value));
        }

        if (p.has(RecordConstants.SearchParams.AUTHOR)) {
            String value = p.get(RecordConstants.SearchParams.AUTHOR);
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.AUTHOR, value));
        }

        if (p.has(RecordConstants.SearchParams.PUBLICATION_YEAR)) {
            Integer value = p.get(RecordConstants.SearchParams.PUBLICATION_YEAR);
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.PUBLICATION_YEAR, String.valueOf(value)));
        }

        if (forZiskejSupportingRecordsOnly) {
            res.add(new CpkSearchFieldValuePair<>(ContextualCpkClient.SearchField.ZISKEJ_SUPPORTED_TYPE, "MVS"));
        }

        return res;
    }

}
