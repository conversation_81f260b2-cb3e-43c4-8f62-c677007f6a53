package cz.kpsys.portaro.ext.ziskej.impl;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.ziskej.ContextualZiskejClient;
import cz.kpsys.portaro.ext.ziskej.model.ZiskejDk;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.loan.ill.Seeking;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.user.Library;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.user.edit.LibrarySyncHelper;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SeekingZiskejAcceptableProvidersResolver implements ContextualFunction<Seeking, Department, List<Library>> {

    @NonNull ContextualZiskejClient<Department> contextualZiskejClient;
    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull LibrarySyncHelper librarySyncHelper;

    @Override
    public List<Library> getOn(Seeking seeking, Department ctx) {
        String ziskejTicketId = Objects.requireNonNull(seeking.getZiskejTicketId(), "Cannot load Ziskej acceptable providers when seeking is not synced with Ziskej (does not have ticketId)");
        List<ZiskejDk> ziskejDks = contextualZiskejClient.getTicketManualDks(ctx, ziskejTicketId);

        List<Library> dbLibraries = loadDatabasedLibraries(ctx, ListUtil.convert(ziskejDks, ZiskejDk::sigla));

        return ziskejDks.stream()
                .map(ziskejDk -> upsertLibrary(ziskejDk, dbLibraries, ctx))
                .toList();
    }

    private List<Library> loadDatabasedLibraries(Department ctx, List<String> siglas) {
        List<User> users = userSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(
                CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.ALL), // we search in whole database to mitigate duplicity creation
                UserConstants.SearchParams.SIGLA, siglas
        ));
        return ListUtil.castItems(users, Library.class).toList();
    }

    private static Optional<Library> findExistingLibrary(ZiskejDk ziskejDk, List<Library> libraries) {
        return libraries.stream()
                .filter(user -> user.getSigla() != null && user.getSigla().equals(ziskejDk.sigla()))
                .findFirst();
    }

    private Library upsertLibrary(ZiskejDk ziskejDk, List<Library> dbLibraries, Department ctx) {
        Optional<Library> existingLibrary = findExistingLibrary(ziskejDk, dbLibraries);
        if (existingLibrary.isPresent()) {
            return librarySyncHelper.update(existingLibrary.get(), ziskejDk.name(), ctx);
        }
        return librarySyncHelper.create(ziskejDk.sigla(), ziskejDk.name(), ctx);
    }

}
