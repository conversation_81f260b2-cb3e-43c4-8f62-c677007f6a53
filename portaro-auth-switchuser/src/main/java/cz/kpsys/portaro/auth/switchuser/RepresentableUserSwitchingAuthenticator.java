package cz.kpsys.portaro.auth.switchuser;

import cz.kpsys.portaro.auth.Authenticator;
import cz.kpsys.portaro.auth.composite.CompositeSuccessAuthentication;
import cz.kpsys.portaro.auth.process.AuthenticationRequest;
import cz.kpsys.portaro.auth.process.AuthoritiedSuccessAuthentication;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.relation.RepresentableUserLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.web.authentication.switchuser.SwitchUserGrantedAuthority;

import java.util.List;

import static cz.kpsys.portaro.user.BasicUser.*;

/**
 * Authentication provider decorator, which switches user to its company, when user has not reader privileges and represents only one company.
 * Created by pacho on 22.03.2016.
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RepresentableUserSwitchingAuthenticator<AUTH_REQUEST extends AuthenticationRequest<?>> implements Authenticator<AUTH_REQUEST, AuthoritiedSuccessAuthentication> {

    @NonNull Authenticator<AUTH_REQUEST, ? extends AuthoritiedSuccessAuthentication> delegate;
    @NonNull RepresentableUserLoader representableUserLoader;
    @NonNull UserByBasicUserLoader userLoader;


    @Override
    public AuthoritiedSuccessAuthentication authenticate(AUTH_REQUEST authRequest) throws org.springframework.security.core.AuthenticationException {
        AuthoritiedSuccessAuthentication originalAuth = delegate.authenticate(authRequest);

        if (!ListUtil.hasAny(originalAuth.getRole(), ROLE_ACTUATOR, ROLE_ADMIN, ROLE_LIBRARIAN, ROLE_LIBRARY, ROLE_READER, ROLE_SERVICEMAN, ROLE_SUPPLIER)) {
            log.info("User in auth {} has not any meaningful roles (only {}), trying to find, if represents any company or related user", originalAuth.getActiveUser(), originalAuth.getRole());

            BasicUser user = originalAuth.getActiveUser();

            List<BasicUser> allRepresentableUsers = representableUserLoader.getAllRepresentableUsers(user);
            if (allRepresentableUsers.size() == 1) {
                User relatedUser = userLoader.getUser(allRepresentableUsers.getFirst());
                log.info("User {} has one related user {}, switching to it", user, relatedUser);
                return createSwitchUserAuthentication(CompositeSuccessAuthentication.ofSingle(originalAuth), relatedUser, authRequest.getDepartment());
            } else {
                log.info("User {} has zero or more than one related users {}, cannot switch user", user, allRepresentableUsers);
            }
        }

        return originalAuth;
    }


    private AuthoritiedSuccessAuthentication createSwitchUserAuthentication(CompositeSuccessAuthentication originalAuth, User targetUser, @NonNull Department ctx) {
        // get the original authorities and add the new switch user authority
        List<GrantedAuthority> targetUserAuths = AuthorityUtils.createAuthorityList(targetUser.getRole().toArray(new String[]{}));
        SwitchUserGrantedAuthority switchUserAuthority = new SwitchUserGrantedAuthority(SwitchedSuccessAuthentication.SWITCH_AUTHORITY_ROLE, originalAuth);
        List<GrantedAuthority> extendedTargetUserAuths = ListUtil.createNewListAppending(targetUserAuths, switchUserAuthority);

        // create the new authentication token
        return new SwitchedSuccessAuthentication(targetUser, ctx, extendedTargetUserAuths, originalAuth);
    }

}
