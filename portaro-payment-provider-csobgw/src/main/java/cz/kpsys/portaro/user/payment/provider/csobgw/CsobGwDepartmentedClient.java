package cz.kpsys.portaro.user.payment.provider.csobgw;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.payment.provider.csobgw.datatypes.*;

public interface CsobGwDepartmentedClient {

    CsobGwEchoResponse echo(Department department, CsobGwEchoRequest request, Boolean postMethod);

    CsobGwEchoCustomerResponse echoCustomer(Department department, CsobGwEchoCustomerRequest request);

    CsobGwPaymentResponse paymentInit(Department department, CsobGwPaymentInitRequest request);

    CsobGwPaymentResponse paymentProcess(Department department, CsobGwPaymentRequest statusRequest);

    CsobGwPaymentResponse paymentStatus(Department department, CsobGwPaymentRequest statusRequest);

    CsobGwPaymentResponse paymentClose(Department department, CsobGwPaymentCloseRequest request);

    CsobGwPaymentResponse paymentReverse(Department department, CsobGwPaymentRequest request);

    CsobGwPaymentResponse paymentRefund(Department department, CsobGwPaymentRefundRequest request);
}
