package cz.kpsys.portaro.erp.reportoptimisation;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.finance.Currency;
import cz.kpsys.portaro.finance.Price;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldFinder;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.NumberFieldValue;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Junction;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PROTECTED, makeFinal = true)
@RequiredArgsConstructor
public abstract class ReportOptimisationLoader {

    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull Provider<@NonNull List<Fond>> reportOptimisationFondsProvider;
    @NonNull Provider<@NonNull Fond> optimisationReportFondProvider;

    protected List<Record> loadReportRecords(Department ctx, Junction<FieldTypeId> conjunction) {
        return detailedRecordSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, reportOptimisationFondsProvider.get());
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, conjunction);
        });
    }

    protected List<Record> filterOptimisedRecords(List<Record> reportRecords) {
        Fond optimisationReportFond = optimisationReportFondProvider.get();

        return reportRecords.stream()
                .filter(record -> record.getFond().getId().equals(optimisationReportFond.getId()))
                .toList();
    }

    protected List<Record> filterOutOptimisedRecords(List<Record> reportRecords) {
        Fond optimisationReportFond = optimisationReportFondProvider.get();

        return reportRecords.stream()
                .filter(record -> !record.getFond().getId().equals(optimisationReportFond.getId()))
                .toList();
    }

    protected int sumNumberFieldValuesHolders(List<Record> reportRecords, FieldFinder<FieldContainer, Field<NumberFieldValue>> fieldFinder) {
        return reportRecords.stream()
                .map(record -> fieldFinder.findFirstIn(record.getDetail(), NumberFieldValue::extract).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .intValue();
    }

    protected Map<String, List<Record>> groupedReportsByProfession(List<Record> reportRecords) {
        return reportRecords.stream()
                .filter(record -> RecordSutinSpecificFields.FondWorkReport.Profession.Main.ProfessionName.FIELD_FINDER.findFirstIn(record.getDetail()).isPresent())
                .collect(Collectors.groupingBy(record ->
                        RecordSutinSpecificFields.FondWorkReport.Profession.Main.ProfessionName.FIELD_FINDER
                                .getFirstIn(record.getDetail(), StringFieldValue::extract)
                ));
    }

    protected @NonNull Price createCzkPrice(long value) {
        return new Price(BigDecimal.valueOf(value), Currency.createCzk());
    }
}
