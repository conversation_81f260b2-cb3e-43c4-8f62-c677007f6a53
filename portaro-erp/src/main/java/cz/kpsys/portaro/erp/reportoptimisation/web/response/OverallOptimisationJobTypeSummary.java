package cz.kpsys.portaro.erp.reportoptimisation.web.response;

import cz.kpsys.portaro.finance.Price;
import lombok.NonNull;

public record OverallOptimisationJobTypeSummary(

        @NonNull String name,

        int reportedStandardHours,
        int reportedOvertimeHours,
        int chargedStandardHours,
        int chargedOvertimeHours,

        @NonNull Price reportedPrice,
        @NonNull Price chargedPrice
) {}