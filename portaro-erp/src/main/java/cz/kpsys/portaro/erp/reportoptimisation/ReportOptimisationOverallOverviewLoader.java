package cz.kpsys.portaro.erp.reportoptimisation;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.reportoptimisation.web.response.OverallOptimisationJobTypeSummary;
import cz.kpsys.portaro.erp.reportoptimisation.web.response.OverallOptimisationOverviewResponse;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportOptimisationOverallOverviewLoader extends ReportOptimisationLoader {

    public ReportOptimisationOverallOverviewLoader(@NonNull HierarchyLoader<Department> contextHierarchyLoader,
                                                   @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader,
                                                   @NonNull Provider<@NonNull List<Fond>> reportOptimisationFondsProvider,
                                                   @NonNull Provider<@NonNull Fond> optimisationReportFondProvider) {

        super(contextHierarchyLoader, detailedRecordSearchLoader, reportOptimisationFondsProvider, optimisationReportFondProvider);
    }

    public OverallOptimisationOverviewResponse load(UUID projectRecordId, Department ctx) {
        List<Record> reportRecords = loadReportRecords(ctx, new Conjunction<FieldTypeId>()
                .add(new Term<>(RecordSutinSpecificFields.ReportedProject.Main.TYPE_ID, new Eq(projectRecordId))));

        var groupedByProfession = groupedReportsByProfession(reportRecords);

        var jobTypeSummaries = groupedByProfession.entrySet().stream()
                .map(entry -> mapRecordsToJobTypeSummary(entry.getKey(), entry.getValue()))
                .toList();

        return new OverallOptimisationOverviewResponse(jobTypeSummaries);
    }

    private @NonNull OverallOptimisationJobTypeSummary mapRecordsToJobTypeSummary(String professionName, List<Record> allReports) {
        List<Record> realReports = filterOutOptimisedRecords(allReports);

        int reportedPrice = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.ReportedPrice.Value.FIELD_FINDER);
        int reportedStandardHours = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.Reported.StandardHoursValue.FIELD_FINDER);
        int reportedOvertimeHours = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.Reported.OvertimeHoursValue.FIELD_FINDER);

        int chargedPrice = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.ChargedPrice.Value.FIELD_FINDER);
        int chargedStandardHours = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.Charged.StandardHoursValue.FIELD_FINDER);
        int chargedOvertimeHours = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.Charged.OvertimeHoursValue.FIELD_FINDER);

        return new OverallOptimisationJobTypeSummary(
                professionName,

                reportedStandardHours,
                reportedOvertimeHours,
                chargedStandardHours,
                chargedOvertimeHours,

                createCzkPrice(reportedPrice),
                createCzkPrice(chargedPrice)
        );
    }
}
