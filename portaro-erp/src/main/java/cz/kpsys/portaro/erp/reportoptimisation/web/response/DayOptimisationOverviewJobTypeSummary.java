package cz.kpsys.portaro.erp.reportoptimisation.web.response;

import lombok.NonNull;
import cz.kpsys.portaro.finance.Price;

public record DayOptimisationOverviewJobTypeSummary(

        @NonNull String name,

        int reportedWorkersCount,
        int chargedWorkersCount,

        int reportedStandardHours,
        int reportedOvertimeHours,
        int chargedStandardHours,
        int chargedOvertimeHours,

        @NonNull Price reportedPrice,
        @NonNull Price chargedPrice
) {}