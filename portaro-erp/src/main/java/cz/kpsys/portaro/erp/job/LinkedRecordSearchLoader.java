package cz.kpsys.portaro.erp.job;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

/// Vyhledání záznamů, které v definovaném poli odkazují na požadovaný záznam.
/// <p>
/// Například, když chci najít všechny monografie, které mají v poli 100 odkaz na Boženu Němcovou, vytvořím tuto třídu
/// s parametry pole, ve kterém má být daný odkaz (d100.main) a fond, do kterého vyhledávané záznamy spadají (1).
/// <em>Vyhledají se i všechny záznamy, které spadají do zděděných fondů!</em>
/// <pre> {@code
/// var fieldTypeId = FieldTypeId.parse("d100.main");
/// var fondProvider = () -> 1;
/// var searchLoader = new LinkedRecordSearchLoader(fieldTypeId, _, _, fondProvider);
///
/// var recordId = nemcovaRecord.getId();
/// List<Record> monografieOdNemcove = searchLoader.loadByTargetRecordId(recordId, ctx);
/// } </pre>
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class LinkedRecordSearchLoader {

    @NonNull Provider<FieldTypeId> linkFieldTypeIdProvider;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader;
    @NonNull Provider<@NonNull Fond> targetRecordfondProvider;

    public LinkedRecordSearchLoader(@NonNull FieldTypeId linkFieldTypeId,
                                    @NonNull HierarchyLoader<Department> contextHierarchyLoader,
                                    @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader,
                                    @NonNull Provider<@NonNull Fond> targetRecordfondProvider,
                                    @NonNull FieldTypeLoader fieldTypeLoader) {
        this.linkFieldTypeIdProvider = Provider.of(() -> {
            // Lazy check of field type link, if it points to some kind of authority
            var fieldType = fieldTypeLoader.getById(linkFieldTypeId);
            var maybeFond = fieldType.getLinkRootFond();
            Assert.isTrue(maybeFond.isPresent(), "linkFieldTypeId must represent field containing link to another record!");

            return linkFieldTypeId;
        }).cached();
        this.contextHierarchyLoader = contextHierarchyLoader;
        this.detailedRecordSearchLoader = detailedRecordSearchLoader;
        this.targetRecordfondProvider = targetRecordfondProvider;
    }

    @Transactional(readOnly = true)
    public List<Record> loadByTargetRecordId(@NonNull UUID recordId, @NonNull Collection<Restriction<? extends FieldTypeId>> restrictions, @NonNull Department ctx) {
        return detailedRecordSearchLoader.getContent(RangePaging.forAll(), p -> {
            p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
            p.set(CoreSearchParams.FACETS_ENABLED, false);
            p.set(CoreSearchParams.INCLUDE_DRAFT, false);
            p.set(CoreSearchParams.INCLUDE_DELETED, false);
            p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
            p.set(RecordConstants.SearchParams.ROOT_FOND, List.of(targetRecordfondProvider.get()));
            p.set(CoreSearchParams.DEPARTMENT, contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.SUBTREE));
            p.set(RecordConstants.SearchParams.RECORD_FIELD_VALUE_RESTRICTION, new Conjunction<FieldTypeId>()
                    .add(new Term<>(linkFieldTypeIdProvider.get(), new Eq(recordId)))
                    .addAll(restrictions));
        });
    }

    @Transactional(readOnly = true)
    public List<Record> loadByTargetRecordId(@NonNull UUID recordId, @NonNull Department ctx) {
        return loadByTargetRecordId(recordId, List.of(), ctx);
    }

}
