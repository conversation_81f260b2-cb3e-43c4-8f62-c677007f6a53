package cz.kpsys.portaro.erp.reportoptimisation;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.erp.RecordSutinSpecificFields;
import cz.kpsys.portaro.erp.reportoptimisation.web.response.DayOptimisationOverviewJobTypeSummary;
import cz.kpsys.portaro.erp.reportoptimisation.web.response.DayOptimisationOverviewResponse;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ReportOptimisationItemOverviewLoader extends ReportOptimisationLoader {

    public ReportOptimisationItemOverviewLoader(@NonNull HierarchyLoader<Department> contextHierarchyLoader,
                                                @NonNull ParameterizedSearchLoader<MapBackedParams, Record> detailedRecordSearchLoader,
                                                @NonNull Provider<@NonNull List<Fond>> reportOptimisationFondsProvider,
                                                @NonNull Provider<@NonNull Fond> optimisationReportFondProvider) {

        super(contextHierarchyLoader, detailedRecordSearchLoader, reportOptimisationFondsProvider, optimisationReportFondProvider);
    }

    public DayOptimisationOverviewResponse load(UUID projectRecordId, UUID projectItemRecordId, Department ctx) {
        List<Record> reportRecords = loadReportRecords(ctx, new Conjunction<FieldTypeId>()
                .add(new Term<>(RecordSutinSpecificFields.ReportedProject.Main.TYPE_ID, new Eq(projectRecordId)))
                .add(new Term<>(RecordSutinSpecificFields.FondWorkReport.ProjectItem.Main.TYPE_ID, new Eq(projectItemRecordId))));

        var groupedByProfession = groupedReportsByProfession(reportRecords);

        var jobTypeSummaries = groupedByProfession.entrySet().stream()
                .map(entry -> mapRecordsToJobTypeSummary(entry.getKey(), entry.getValue()))
                .toList();

        return new DayOptimisationOverviewResponse(jobTypeSummaries);
    }

    private @NonNull DayOptimisationOverviewJobTypeSummary mapRecordsToJobTypeSummary(String professionName, List<Record> allReports) {
        List<Record> optimisedReports = filterOptimisedRecords(allReports);
        List<Record> realReports = filterOutOptimisedRecords(allReports);

        var totalReportedPrice = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.ReportedPrice.Value.FIELD_FINDER);
        var totalReportedStandardHours = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.Reported.StandardHoursValue.FIELD_FINDER);
        var totalReportedOvertimeHours = sumNumberFieldValuesHolders(realReports, RecordSutinSpecificFields.FondWorkReport.Reported.OvertimeHoursValue.FIELD_FINDER);

        var totalChargedPrice = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.ChargedPrice.Value.FIELD_FINDER);
        var totalChargedStandardHours = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.Charged.StandardHoursValue.FIELD_FINDER);
        var totalChargedOvertimeHours = sumNumberFieldValuesHolders(allReports, RecordSutinSpecificFields.FondWorkReport.Charged.OvertimeHoursValue.FIELD_FINDER);

        return new DayOptimisationOverviewJobTypeSummary(
                professionName,

                realReports.size(),
                realReports.size() + optimisedReports.size(),

                totalReportedStandardHours,
                totalReportedOvertimeHours,
                totalChargedStandardHours,
                totalChargedOvertimeHours,

                createCzkPrice(totalReportedPrice),
                createCzkPrice(totalChargedPrice)
        );
    }
}
