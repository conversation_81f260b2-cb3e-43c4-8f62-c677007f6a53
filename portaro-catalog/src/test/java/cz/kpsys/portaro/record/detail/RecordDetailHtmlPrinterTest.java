package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.localization.TestingTranslator;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.TestData;
import cz.kpsys.portaro.record.ViewableRecord;
import cz.kpsys.portaro.record.print.RecordDetailPrinter;
import cz.kpsys.portaro.record.print.RecordDetailRichHtmlPrinter;
import cz.kpsys.portaro.search.view.RecordToViewableRecordsConverter;
import cz.kpsys.portaro.user.NoConcreteUser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("ci")
@Tag("unit")
public class RecordDetailHtmlPrinterTest {

    public static final CurrentAuth CURRENT_AUTH = CurrentAuth.createForTestingWithAbsoluteAuthenticity(NoConcreteUser.anonymousUser());

    private RecordDetailPrinter printer;
    private TestingTranslator<Department> translator;
    private ViewableRecord viewableRecord;

    @BeforeEach
    public void setup() {
        Record record = TestData.createSimpleRecord();
        viewableRecord = RecordToViewableRecordsConverter.testing().convertSingle(record, CURRENT_AUTH, Department.testingRoot(), TestingTranslator.USED_LOCALE);
        translator = new TestingTranslator<>();
        printer = new RecordDetailRichHtmlPrinter(translator, Department.testingRoot(), TestingTranslator.USED_LOCALE, recordId -> "http://test.cz/#!/records/" + recordId);
    }
    
    @Test
    public void shouldPrintWholeField() {
        String result = printer.field(viewableRecord, "245", "abc", "-pref-", "-suf-", ";", " .. ");
        String expected = "-pref-<span class=\"field_a fieldtype-d245-a\" title=\"Hlavní název\">Floriculture :</span> .. <span class=\"field_b fieldtype-d245-b\" title=\"Další údaje o názvu\"> designing & merchandising /</span> .. <span class=\"field_c fieldtype-d245-c\" title=\"Údaj o odpovědnosti atd.\"> Charles Griner.</span>-suf-";
        Assertions.assertEquals(expected, result);
    }
    
    @Test
    public void shouldPrintFieldWithUrl() {
        String result = printer.field(viewableRecord, "222", null, "-pref-", "-suf-", ";", " ");
        String expected = "-pref-<span class=\"field_a fieldtype-d222-a\" title=\"Hlavní název\"><a href=\"http://nevim.cz/abc\" target=\"_blank\">http://nevim.cz/abc</a></span>-suf-";
        Assertions.assertEquals(expected, result);
    }
    
    @Test
    public void shouldEscapePrintedField() {
        String result = printer.field(viewableRecord, "650", "a", "-pref-", "-suf-", "; ", " ");
        String expected = "-pref-<span class=\"field_a fieldtype-d650-a\" title=\"Věcné téma\">Flower arrangement</span>; <span class=\"field_a fieldtype-d650-a\" title=\"Věcné téma\"><a href=\"http://test.cz/#!/records/59669182-f20c-4a3f-ab5c-742a30e0cf24\">&lt;&lt;Florists</a></span>-suf-";
        Assertions.assertEquals(expected, result);
    }
    
    @Test
    public void shouldLocalizePrefixAndSuffix() {
        translator.with("messageCodeOfPrefix", "Lokalizovaný prefix");
        translator.with("messageCodeOfSuffix");
        String result = printer.field(viewableRecord, "245", "a", "-{messageCodeOfPrefix}:-", "-{messageCodeOfSuffix}-", ";", " ");
        String expected = "-Lokalizovaný prefix:-<span class=\"field_a fieldtype-d245-a\" title=\"Hlavní název\">Floriculture :</span>-messageCodeOfSuffix-";
        Assertions.assertEquals(expected, result);
    }
    
    @Test
    public void shouldPrintEmptyWhenFieldDoesNotExist() {
        String result = printer.field(viewableRecord, "1500", "a", "-pref:-", "-suf-", ";", " ");
        String expected = "";
        Assertions.assertEquals(expected, result);
    }

    /**
     * Otestuje vypsani prefixu, kdyz existuji tri opakovani pole 84, ale 84[0].a 84[1].a nejsou vypsatelne, ma se vypsat pouze 84[2].c
     */
    @Test
    public void shouldPrintPrefixWhenWrittableSubfieldFromThirdFieldOnly() {
        String result = printer.field(viewableRecord, "84", "c", "-pref:-", "-suf-", ";", " ");
        String expected = "-pref:-<span class=\"field_c fieldtype-d84-c\" title=\"Testing field d84.c\">ef</span>-suf-";
        Assertions.assertEquals(expected, result);
    }
    
}
