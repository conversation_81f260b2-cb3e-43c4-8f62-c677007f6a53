package cz.kpsys.portaro.view.domain.paragraph;

import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.auth.current.CurrentAuth;
import cz.kpsys.portaro.commons.contextual.ContextIgnoringContextualProvider;
import cz.kpsys.portaro.commons.localization.TestingTranslator;
import cz.kpsys.portaro.commons.object.StaticProvider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.SystemInstitution;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.TestData;
import cz.kpsys.portaro.search.VelocitySearchResult;
import cz.kpsys.portaro.template.velocity.VelocityTemplateEngine;
import cz.kpsys.portaro.user.NoConcreteUser;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.StringWriter;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Tag("ci")
@Tag("unit")
public class StringTemplateParagraphTest {

    private static final String SERVER_URL = "http://localhost";
    public static final CurrentAuth CURRENT_AUTH = CurrentAuth.createForTestingWithAbsoluteAuthenticity(NoConcreteUser.anonymousUser());
    
    private Record record;
    private TestingTranslator<Department> translator;


    /**
     * Pro testovaci ucely. Vytvari velocityEngine sam
     */
    private static VelocityTemplateEngine testingEngine() {
        return new VelocityTemplateEngine(
                new VelocityEngine(),
                new TestingTranslator<>(),
                ContextIgnoringContextualProvider.of(""),
                StaticProvider.of(""),
                ContextIgnoringContextualProvider.of(CatalogConstants.DEFAULT_LOCALE),
                ctx -> SystemInstitution.testing(),
                (_, _, _) -> nontypedParams -> Mockito.mock(VelocitySearchResult.class)
        );
    }

    @BeforeEach
    public void setup() {
        translator = new TestingTranslator<>();
        record = TestData.createFloricultureDocument();
    }

    @Test
    public void testSimpleTagWithoutRecordInformations() {
        String paragraph = "<span>cus</span>";
        VelocityTemplateEngine templateEngine = testingEngine();
        StringTemplateParagraph stringTemplateParagraph = StringTemplateParagraph.testing(templateEngine, translator, paragraph);
        String expResult = "<span>cus</span>";
        String result = stringTemplateParagraph.build(record, CURRENT_AUTH, Department.testingRoot(), TestingTranslator.USED_LOCALE, SERVER_URL, null);
        assertEquals(expResult, result);
    }

    @Test
    public void testRecordName() {
        String paragraph = "<span>$record.name</span>";
        VelocityTemplateEngine templateEngine = testingEngine();
        StringTemplateParagraph stringTemplateParagraph = StringTemplateParagraph.testing(templateEngine, translator, paragraph);
        String expResult = "<span>Floriculture</span>";
        String result = stringTemplateParagraph.build(record, CURRENT_AUTH, Department.testingRoot(), TestingTranslator.USED_LOCALE, SERVER_URL, null);
        assertEquals(expResult, result);
    }

    @Test
    public void testRecordDetailInformations() {
        String paragraph = "<span>$recordPrinter.field($record, 245, 'a', null, null, null, null)</span>";
        VelocityTemplateEngine templateEngine = testingEngine();
        StringTemplateParagraph stringTemplateParagraph = StringTemplateParagraph.testing(templateEngine, translator, paragraph);
        String expResult = "<span><span class=\"field_a fieldtype-d245-a\" title=\"Hlavní název\">Floriculture :</span></span>";
        String result = stringTemplateParagraph.build(record, CURRENT_AUTH, Department.testingRoot(), TestingTranslator.USED_LOCALE, SERVER_URL, null);
        assertEquals(expResult, result);
    }

    @Test
    public void testStartsWithExpression() {
        String paragraph = "<span>$record.getSubfield(245, 'a').raw.startsWith(\"Flo\")</span>";
        VelocityTemplateEngine templateEngine = testingEngine();
        StringTemplateParagraph stringTemplateParagraph = StringTemplateParagraph.testing(templateEngine, translator, paragraph);
        String expResult = "<span>true</span>";
        String result = stringTemplateParagraph.build(record, CURRENT_AUTH, Department.testingRoot(), TestingTranslator.USED_LOCALE, SERVER_URL, null);
        assertEquals(expResult, result);
    }

    @Test
    public void testUsingNestedMacroInSet() {
        String template = """
                #macro( sf $a1 $a2 $a3 $a4 $a5 $a6 $a7 $a8) #end
                #set($a = "#sf(245 'a' 'a' '' '' '' 'a' 'a')")""";
        VelocityEngine velocityEngine = new VelocityEngine();
        StringWriter writer = new StringWriter();
        velocityEngine.evaluate(new VelocityContext(), writer, "custom-string-template", template);
        //pass if not exception
    }

}