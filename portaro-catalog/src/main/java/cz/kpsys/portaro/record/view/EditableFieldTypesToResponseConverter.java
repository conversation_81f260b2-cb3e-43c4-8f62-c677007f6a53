package cz.kpsys.portaro.record.view;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;
import cz.kpsys.portaro.record.detail.fn.Ref;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldDisplayType;
import cz.kpsys.portaro.record.edit.RecordEntryFieldTypeIdResolver;
import cz.kpsys.portaro.record.edit.view.ValueEditorByFieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditableFieldTypesToResponseConverter {

    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull ValueEditorByFieldTypeLoader valueEditorByFieldTypeLoader;

    public @NonNull List<ViewableEditableFieldType> convert(@NonNull Record record, @NonNull List<? extends EditableFieldType> subfieldTypes) {
        // preload entry field types for better performance
        Map<Fond, FieldTypeId> recordableFondEntryFieldTypes = recordEntryFieldTypeIdResolver.getRecordableFondEntryNativeSubfieldTypes(ListUtil.convert(subfieldTypes, EditableFieldType::getEffectiveFond));

        return subfieldTypes.stream()
                .filter(editableFieldType -> FieldDisplayType.ALL_EDITABLE.contains(editableFieldType.getDisplayType()))
                .sorted(EditableFieldType.EDITATION_ORDER_COMPARATOR)
                .map(editableFieldType -> convertSingle(record, editableFieldType, recordableFondEntryFieldTypes))
                .toList();
    }

    private @NonNull ViewableEditableFieldType convertSingle(@NonNull Record record, EditableFieldType editableFieldType, Map<Fond, FieldTypeId> recordableFondEntryFieldTypes) {
        return new ViewableEditableFieldType(
                editableFieldType.getId(),
                editableFieldType.getText(),
                valueEditorByFieldTypeLoader.getEditorIfDatatyped(editableFieldType).orElse(null),
                editableFieldType.isRepeatable(),
                isReorderable(editableFieldType),
                getSingleLinkedRecordSubfieldTypeId(record, editableFieldType),
                convert(record, editableFieldType.getSubfieldTypes()),
                editableFieldType.getCode(),
                editableFieldType.getDisplayType(),
                getEditNote(editableFieldType),
                editableFieldType.getFieldTypeId().equals(recordableFondEntryFieldTypes.get(editableFieldType.getEffectiveFond()))
        );
    }

    private boolean isReorderable(EditableFieldType editableFieldType) {
        return !FieldTypes.INDICATORS_FIELD_CODES.contains(editableFieldType.getCode());
    }

    @Nullable
    private String getSingleLinkedRecordSubfieldTypeId(@NonNull Record record, EditableFieldType fieldType) {
        if (fieldType.getFormula().isEmpty()) {
            return null;
        }
        if (!(fieldType.getFormula().get() instanceof Ref(LookupDefinition pointer))) {
            return null;
        }
        if (pointer.isSelfRecord()) {
            return null;
        }
        // return pointer.existingLinkedFieldTypeId().value();
        return null;
    }

    private Text getEditNote(EditableFieldType editableFieldType) {
        Text columnMessageCode = Texts.ofMessageCoded("field.editNote.%s".formatted(editableFieldType.getFieldTypeId().value()));
        return Texts.ofDefaulted(columnMessageCode, Texts.ofEmpty());
    }
}
