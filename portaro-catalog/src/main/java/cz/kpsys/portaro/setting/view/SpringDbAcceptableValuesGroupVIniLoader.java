package cz.kpsys.portaro.setting.view;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledId;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.object.AbstractSpringDbAcceptableValuesGroupLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;

import static cz.kpsys.portaro.databasestructure.SettingDb.INI_VALHOD.*;

public class SpringDbAcceptableValuesGroupVIniLoader extends AbstractSpringDbAcceptableValuesGroupLoader<LabeledIdentified<String>, String> {
    
    public SpringDbAcceptableValuesGroupVIniLoader(NamedParameterJdbcOperations jdbcTemplate, QueryFactory queryFactory) {
        super(jdbcTemplate, queryFactory, TABLE, ID_VALHOD, PORADI);
    }
    
    @Override
    public LabeledIdentified<String> mapRow(ResultSet rs, int rowNum) throws SQLException {
        String value = rs.getString(OBSAH);
        Text text = Texts.ofNative(StringUtil.firstNotBlank(rs.getString(POPIS), value));
        return new LabeledId<>(value, text);
    }
}
