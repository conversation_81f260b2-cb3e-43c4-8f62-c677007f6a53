package cz.kpsys.portaro.oai.provider.impl;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.util.RegExpUtils;
import cz.kpsys.portaro.commons.web.PlaceholderTemplate;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.oai.model.GetRecordResponse;
import cz.kpsys.portaro.oai.model.RecordResponse;
import cz.kpsys.portaro.oai.provider.OaiException;
import cz.kpsys.portaro.oai.provider.handler.OaiCommand;
import cz.kpsys.portaro.oai.provider.handler.VerbHandler;
import cz.kpsys.portaro.oai.provider.impl.record.FormattedRecordResponseConverter;
import cz.kpsys.portaro.oai.provider.impl.record.UpdatedRecordId;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordRegExpPatterns;
import cz.kpsys.portaro.record.export.RecordExport;
import cz.kpsys.portaro.record.export.listener.RecordExportListener;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cz.kpsys.portaro.oai.model.ErrorCode.BAD_ARGUMENT;
import static cz.kpsys.portaro.oai.model.ErrorCode.ID_DOES_NOT_EXIST;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class GetRecordHandler implements VerbHandler<GetRecordResponse> {

    @NonNull String recordIdPatternPlaceholder;
    @NonNull String kindedIdPatternPlaceholder;
    @NonNull ContextualProvider<Department, @NonNull String> documentKindedIdPattern;
    @NonNull ContextualProvider<Department, @NonNull String> recordIdPattern;
    @NonNull ByIdLoadable<RecordExport, String> recordExportByOaiSetIdLoader;
    @NonNull ByIdLoadable<Record, Integer> nonDetailedDocumentByKindedIdLoader;
    @NonNull FormattedRecordResponseConverter formattedRecordResponseConverter;
    @NonNull RecordExportListener recordExportListener;

    @Override
    public @NonNull GetRecordResponse handle(@NonNull OaiCommand command) {
        OaiException.throwIf(command.metadataPrefix() == null, BAD_ARGUMENT, "Required metadataPrefix is missing in the request");
        OaiException.throwIf(command.identifier() == null, BAD_ARGUMENT, "Required identifier is missing in the request");

        RecordExport recordExport = recordExportByOaiSetIdLoader.getById(command.set());
        UpdatedRecordId updatedRecordId = getUpdatedRecordId(command.identifier(), command.department());
        RecordResponse recordResponse = formattedRecordResponseConverter.toRecordResponse(command.metadataPrefix(), recordExport, updatedRecordId, command.currentAuth(), command.department());

        recordExportListener.recordsExported(List.of(updatedRecordId.getId()), recordExport, command.requesterIp());

        return new GetRecordResponse(recordResponse);
    }


    private UpdatedRecordId getUpdatedRecordId(@NonNull String oaiRecordId, @NonNull Department currentDepartment) {
        Optional<UUID> recordId = Optional.empty();

        Optional<Integer> documentKindedId = parseDocumentKindedId(oaiRecordId, currentDepartment);
        if (documentKindedId.isPresent()) {
            recordId = Optional.of(nonDetailedDocumentByKindedIdLoader.getById(documentKindedId.get()).getId());
        }

        if (recordId.isEmpty()) {
            recordId = parseRecordId(oaiRecordId, currentDepartment);
        }

        if (recordId.isEmpty()) {
            throw new OaiException(ID_DOES_NOT_EXIST, "Wrong on inexisting record identifier");
        }

        return UpdatedRecordId.ofUnknownUpdateDate(recordId.get());
    }


    private Optional<Integer> parseDocumentKindedId(@NonNull String oaiRecordId, @NonNull Department currentDepartment) {
        return parse(oaiRecordId, documentKindedIdPattern.getOn(currentDepartment), kindedIdPatternPlaceholder, RecordRegExpPatterns.RECORD_KINDED_ID, Integer::parseInt);
    }

    private Optional<UUID> parseRecordId(@NonNull String oaiRecordId, @NonNull Department currentDepartment) {
        return parse(oaiRecordId, recordIdPattern.getOn(currentDepartment), recordIdPatternPlaceholder, UuidGenerator.UUID_PATTERN_INFIX, UUID::fromString);
    }

    @NonNull
    private <T> Optional<T> parse(@NonNull String oaiRecordId, @NonNull String placeholderedPattern, @NonNull String placeholder, @NonNull String idFormatRegexp, Function<String, T> stringIdToTargetTypeFunction) {
        String wholePlaceholder = PlaceholderTemplate.SINGLE_CURLY_BRACKET_START + placeholder + PlaceholderTemplate.SINGLE_CURLY_BRACKET_END;
        if (!placeholderedPattern.contains(wholePlaceholder)) {
            return Optional.empty();
        }

        String regex = placeholderedPattern.replace(wholePlaceholder, RegExpUtils.group(idFormatRegexp));
        Pattern pattern = Pattern.compile(regex);
        Matcher m = pattern.matcher(oaiRecordId);
        boolean find = m.find();
        if (!find) {
            throw new IllegalStateException("Invalid OAI record id %s in pattern %s".formatted(oaiRecordId, placeholderedPattern));
        }
        String group = m.group(1);
        T targetTypeId = stringIdToTargetTypeFunction.apply(group);
        return Optional.of(targetTypeId);
    }

}
