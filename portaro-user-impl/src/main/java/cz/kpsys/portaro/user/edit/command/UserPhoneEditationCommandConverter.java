package cz.kpsys.portaro.user.edit.command;

import cz.kpsys.portaro.user.PhoneNumberEditationCommand;
import cz.kpsys.portaro.user.contact.Contact;
import cz.kpsys.portaro.user.contact.ContactType;

public class UserPhoneEditationCommandConverter {

    public static PhoneNumberEditationCommand toCommand(Contact contact) {
        return new PhoneNumberEditationCommand(contact.getValue(), contact.type() == ContactType.SMS_PHONE, contact.source());
    }
}
