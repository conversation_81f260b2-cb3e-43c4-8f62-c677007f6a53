package cz.kpsys.portaro.record.export.marc;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.marcxml.model.StrictRecordMarcDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.export.RecordExport;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EnhancingRecordsToMarcRecordsMapper implements RecordsToMarcRecordsMapper {

    @NonNull RecordsToMarcRecordsMapper delegate;
    @NonNull MarcRecordsEnhancer enhancer;

    @Override
    public List<IdentifiedValue<UUID, StrictRecordMarcDto>> map(@NonNull List<Record> records,
                                                                @NonNull RecordExport recordExport,
                                                                @NonNull UserAuthentication currentAuth,
                                                                @NonNull Department currentDepartment) {
        Collection<IdentifiedValue<UUID, StrictRecordMarcDto>> original = Objects.requireNonNull(delegate.map(records, recordExport, currentAuth, currentDepartment));
        List<MappedRecord> originalMappedRecords = mapValuesToMappedRecords(records, original);
        Collection<MappedRecord> enhancedMappedRecords = enhancer.enhance(originalMappedRecords, recordExport, currentAuth, currentDepartment);
        return mapMappedRecordsToValues(enhancedMappedRecords);
    }

    private static List<MappedRecord> mapValuesToMappedRecords(@NonNull List<Record> records, Collection<IdentifiedValue<UUID, StrictRecordMarcDto>> original) {
        return ListUtil.convertStrict(original, identifiedMarcRecord -> {
            Record record = Record.getByRecordId(records, identifiedMarcRecord.id(), false, () -> "Given list of records does not contain desired one (by id %s) for enhancing marc record %s".formatted(identifiedMarcRecord.id(), identifiedMarcRecord));
            return new MappedRecord(record, identifiedMarcRecord.value());
        });
    }

    private static List<IdentifiedValue<UUID, StrictRecordMarcDto>> mapMappedRecordsToValues(Collection<MappedRecord> enhancedPairs) {
        return ListUtil.convert(enhancedPairs, enhancedPair -> IdentifiedValue.of(enhancedPair.record().getId(), enhancedPair.marcRecord()));
    }
}
