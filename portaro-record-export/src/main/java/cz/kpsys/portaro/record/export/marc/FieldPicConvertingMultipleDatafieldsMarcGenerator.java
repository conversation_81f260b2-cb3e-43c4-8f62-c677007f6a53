package cz.kpsys.portaro.record.export.marc;

import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.marcxml.model.StrictDatafieldMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictSubfieldMarcDto;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldExportSetting;
import cz.kpsys.portaro.record.detail.FieldHelper;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.IndicatorType;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldDisplayType;
import cz.kpsys.portaro.record.edit.RecordFieldTypesLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FieldPicConvertingMultipleDatafieldsMarcGenerator<ITEM> implements DatafieldsMarcGenerator<Provider<List<ITEM>>> {

    @NonNull ContextualProvider<Department, @NonNull FieldTypeId> fieldTypeIdProvider;
    @NonNull RecordFieldTypesLoader recordEditableFieldTypesLoader;
    @NonNull Map<String, ContextualFunction<ITEM, Department, String>> picToSubfieldExporterMap;

    @Override
    public @NonNull List<StrictDatafieldMarcDto> generate(@NonNull Record record, @NonNull Provider<List<ITEM>> items, @NonNull Department ctx) {
        return items.get().stream()
                .flatMap(exemplar -> generateSingle(record, exemplar, ctx))
                .toList();
    }

    @NonNull
    private Stream<StrictDatafieldMarcDto> generateSingle(@NonNull Record record, @NonNull ITEM item, @NonNull Department ctx) {
        Optional<EditableFieldType> itemDatafieldType = recordEditableFieldTypesLoader.findById(record, fieldTypeIdProvider.getOn(ctx));
        if (itemDatafieldType.isEmpty()) {
            return Stream.empty();
        }
        List<StrictSubfieldMarcDto> marcSubfields = itemDatafieldType.get().getSubfieldTypes().stream()
                .flatMap(fieldType -> getItemSubfield(fieldType, item, ctx))
                .toList();
        if (marcSubfields.isEmpty()) {
            return Stream.empty();
        }
        return Stream.of(new StrictDatafieldMarcDto(FieldHelper.fieldCodeToMarc21Tag(fieldTypeIdProvider.getOn(ctx).getCode()), IndicatorType.WIDE_SUPPORTED_EMPTY_IND, IndicatorType.WIDE_SUPPORTED_EMPTY_IND, marcSubfields));
    }

    private Stream<StrictSubfieldMarcDto> getItemSubfield(@NonNull EditableFieldType fieldType, @NonNull ITEM item, @NonNull Department ctx) {
        if (fieldType.getDisplayType() == FieldDisplayType.DISABLED) {
            return Stream.empty();
        }

        FieldExportSetting exportSetting = fieldType.getExportSetting();
        if (!exportSetting.isEnabled()) {
            return Stream.empty();
        }

        String targetCode = exportSetting.getTargetFieldTypeId().getCode();
        ContextualFunction<ITEM, Department, String> exporter = picToSubfieldExporterMap.get(fieldType.getDatatypeOrThrow().getName());
        if (exporter == null) {
            log.error("Invalid PIC value '{}' in exemplar-related field type {} -> skip generating exported subfield {}", fieldType.getDatatypeOrThrow(), fieldType, targetCode);
            return Stream.empty();
        }

        String value = exporter.getOn(item, ctx);
        if (value == null) {
            return Stream.empty();
        }

        return Stream.of(StrictSubfieldMarcDto.ofWithoutRecord(targetCode, value));
    }

}
