package cz.kpsys.portaro.template;

import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.localization.UserFriendlyException;
import cz.kpsys.portaro.commons.object.SeveritedException;
import org.springframework.core.NestedRuntimeException;

public class TemplateParseException extends NestedRuntimeException implements SeveritedException, UserFriendlyException {

    public TemplateParseException(String templateDescriptor, Throwable cause) {
        super("Template parsing error (" + templateDescriptor + ")", cause);
    }

    @Override
    public Text getText() {
        return Texts.ofNative("Template parsing error");
    }

    @Override
    public int getSeverity() {
        return SEVERITY_ERROR;
    }
}
