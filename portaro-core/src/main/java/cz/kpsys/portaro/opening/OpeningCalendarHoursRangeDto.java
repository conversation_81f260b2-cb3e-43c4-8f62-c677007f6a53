package cz.kpsys.portaro.opening;

import lombok.NonNull;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public record OpeningCalendarHoursRangeDto(@NonNull String fromTime, @NonNull String toTime) {

    public static OpeningCalendarHoursRangeDto byTimes(@NonNull LocalTime fromTime, @NonNull LocalTime toTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return new OpeningCalendarHoursRangeDto(fromTime.format(formatter), toTime.format(formatter));
    }

}
