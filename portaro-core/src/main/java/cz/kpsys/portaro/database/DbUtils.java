package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sql.generator.Query;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;
import java.sql.*;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

public class DbUtils {

    public static String getStringNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(rs.getString(column), () -> "String column " + column + " cannot be null, but it is");
    }

    public static String getStringNotNullNotBlank(ResultSet rs, String column) throws SQLException {
        return StringUtil.requireNotBlank(getStringNotNull(rs, column));
    }

    @NonNull
    public static Optional<String> getOptionalNotBlankTrimmedString(ResultSet rs, String column) throws SQLException {
        String string = rs.getString(column);
        if (string == null) {
            return Optional.empty();
        }
        return Optional.of(StringUtil.requireNotBlank(string.trim()));
    }

    public static Integer getIntegerNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(getInteger(rs, column), () -> "Integer column " + column + " cannot be null, but it is");
    }

    public static Integer getInteger(ResultSet rs, String column) throws SQLException {
        int number = rs.getInt(column);
        if (rs.wasNull()) {
            return null;
        }
        return number;
    }

    public static Long getLong(ResultSet rs, String column) throws SQLException {
        long number = rs.getLong(column);
        if (rs.wasNull()) {
            return null;
        }
        return number;
    }

    public static Long getLongNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(getLong(rs, column), () -> "Long column " + column + " cannot be null, but it is");
    }

    public static Short getShort(ResultSet rs, String column) throws SQLException {
        short number = rs.getShort(column);
        if (rs.wasNull()) {
            return null;
        }
        return number;
    }

    public static Short getShortNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(getShort(rs, column), () -> "Short column " + column + " cannot be null, but it is");
    }

    public static Boolean getBoolean(ResultSet rs, String column) throws SQLException {
        boolean val = rs.getBoolean(column);
        if (rs.wasNull()) {
            return null;
        }
        return val;
    }

    public static Boolean getBooleanNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(getBoolean(rs, column), () -> "Boolean column " + column + " cannot be null, but it is");
    }

    public static LocalDate getLocalDateOrNull(ResultSet rs, String column) throws SQLException {
        Date date = rs.getDate(column);
        if (date == null) {
            return null;
        }
        return date.toLocalDate();
    }

    public static boolean containsColumn(ResultSet rs, String column) {
        return getColumnIndex(rs, column) >= 0;
    }

    public static Integer integerOrNullIfColumnNotExists(ResultSet rs, String column) throws SQLException {
        if (containsColumn(rs, column)) {
            return getInteger(rs, column);
        }
        return null;
    }

    public static int getColumnIndex(ResultSet rs, String column) {
        try {
            ResultSetMetaData rsMetaData = rs.getMetaData();
            int numberOfColumns = rsMetaData.getColumnCount();

            // get the column names; column indexes start from 1
            for (int i = 1; i < numberOfColumns + 1; i++) {
                String columnInRS = rsMetaData.getColumnLabel(i);
                // Get the name of the column's table name
                if (column.equalsIgnoreCase(columnInRS)) {
                    return i;
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return -1;
    }

    public static int toInt(Number n) {
        if (n == null) {
            return 0;
        }
        return n.intValue();
    }

    public static int intResult(NamedParameterJdbcOperations jdbcOperations, Query sq) {
        return toInt(jdbcOperations.queryForObject(sq.getSql(), sq.getParamMap(), Integer.class));
    }

    /**
     * Zavola rs.getObject(column) s tim, ze prevede nestandardni typy (napr BigDecimal) na standardni (Integer).
     */
    public static Object getStandardObject(ResultSet rs, String column) throws SQLException {
        Object obj = rs.getObject(column);
        if (obj instanceof Long longObj && longObj <= Integer.MAX_VALUE) {
            obj = longObj.intValue();
        }
        if (obj instanceof BigDecimal bigDecimal) {
            obj = bigDecimal.intValue();
        }
        if (obj instanceof Timestamp timestamp) {
            obj = timestamp.toLocalDateTime();
        }
        if (obj instanceof String string) {
            Optional<UUID> uuid = StringToUuidConverter.tryFromString(string);
            if (uuid.isPresent()) {
                return uuid.get();
            }
        }
        return obj;
    }

    public static Instant instantNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(instantOrNull(rs, column), () -> "Instant column " + column + " cannot be null, but it is");
    }

    @Nullable
    public static Instant instantOrNull(ResultSet rs, String column) throws SQLException {
        Timestamp date = rs.getTimestamp(column);
        if (date == null) {
            return null;
        }
        return date.toInstant();
    }

    @NonNull
    public static UUID uuidNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(uuidOrNull(rs, column), () -> "UUID column " + column + " cannot be null, but it is");
    }

    @Nullable
    public static UUID uuidOrNull(ResultSet rs, String column) throws SQLException {
        String value = rs.getString(column);
        if (value == null) {
            return null;
        }
        return UUID.fromString(value);
    }

    @Nullable
    public static Double doubleOrNull(ResultSet rs, String column) throws SQLException {
        double value = rs.getDouble(column);
        if (rs.wasNull()) {
            return null;
        }
        return value;
    }

    @NonNull
    public static Double doubleNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(doubleOrNull(rs, column), () -> "Double column " + column + " cannot be null, but it is");
    }

    @Nullable
    public static BigDecimal bigDecimalOrNull(ResultSet rs, String column) throws SQLException {
        BigDecimal value = rs.getBigDecimal(column);
        if (rs.wasNull()) {
            return null;
        }
        return value;
    }

    @NonNull
    public static BigDecimal bigDecimalNotNull(ResultSet rs, String column) throws SQLException {
        return Objects.requireNonNull(bigDecimalOrNull(rs, column), () -> "BigDecimal column " + column + " cannot be null, but it is");
    }
}
