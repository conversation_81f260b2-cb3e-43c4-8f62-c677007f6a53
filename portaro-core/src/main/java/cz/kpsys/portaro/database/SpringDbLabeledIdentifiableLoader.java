package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.io.Serializable;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * Pomocna trida pro nacitani objektu ciselnikoveho typu.
 * <AUTHOR> <PERSON>
 * @param <E> Nacitany objekt, ktery musi byt LabeledIdentified
 * @param <ID> Typ idecka v LabeledIdentified
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public abstract class SpringDbLabeledIdentifiableLoader<E, ID extends Serializable> implements AllValuesProvider<E>, RowMapper<E> {

    @NonNull protected NamedParameterJdbcOperations jdbcTemplate;
    @NonNull protected QueryFactory queryFactory;
    @NonNull String table;
    @NonNull String idColumn;
    @NonNull String labelColumn;
    @NonNull List<String> sortColumns;
    @NonFinal boolean trimName = false;

    public SpringDbLabeledIdentifiableLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                             @NonNull QueryFactory queryFactory,
                                             @NonNull String table,
                                             @NonNull String idColumn,
                                             @NonNull String textColumn,
                                             @NonNull String... sortColumns) {
        this.jdbcTemplate = jdbcTemplate;
        this.queryFactory = queryFactory;
        this.table = table;
        this.idColumn = idColumn;
        this.labelColumn = textColumn;
        this.sortColumns = Arrays.asList(sortColumns);
    }

    @Override
    public List<E> getAll() {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(table);
        addQueryConditions(sq);
        sortColumns.forEach(sortColumn -> sq.orderBy().addAsc(sortColumn));
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    /**
     * Method for adding where contitions to loadForCache sql
     * @param sq
     */
    protected void addQueryConditions(SelectQuery sq) {
        //for subclasses
    }

    @Override
    public E mapRow(ResultSet rs, int rowNum) throws SQLException {
        Object idObject = DbUtils.getStandardObject(rs, idColumn);
        ID id = formatId((ID) idObject);
        String name = rs.getString(labelColumn);
        if (trimName) {
            name = StringUtil.trimOrLetNull(name);
        } else {
            name = StringUtil.rtrimOrLetNull(name);
        }
        return createObject(id, name, rs);
    }

    /**
     * automaticky zprava otrimujeme id.
     */
    protected ID formatId(ID id) {
        if (id == null) {
            return null;
        }
        if (id instanceof String s) {
            if (!s.isEmpty()) {
                s = StringUtil.rtrimOrLetNull(s);
            }
            return (ID) s;
        }
        return id;
    }
    
    /**
     * Vrati naplneny objekt.
     * @param id id objektu
     * @param name nacteny string ze sloupecku textColumn
     * @param rs resultset
     */
    protected abstract E createObject(ID id, String name, ResultSet rs) throws SQLException;

    public void setTrimName(boolean trimName) {
        this.trimName = trimName;
    }

}
