package cz.kpsys.portaro.shutdown;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ConfigurableApplicationContext;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ApplicationShutdownerByContextInterruptAndSystemExit implements ApplicationShutdowner {

    private static final long FORCE_SHUTDOWN_TIMEOUT = 5000L;
    private static final long SHUTDOWN_DELAY = 500L;

    @NonNull ConfigurableApplicationContext context;


    public void shutdown() {
        log.info("Performing shutdown");

        Thread shutdownThread = new Thread(this::performShutdown);
        shutdownThread.setContextClassLoader(getClass().getClassLoader());
        shutdownThread.start();

        Thread forceShutdownThread = new Thread(this::performForceShutdown);
        forceShutdownThread.setContextClassLoader(getClass().getClassLoader());
        forceShutdownThread.start();
    }


    private void performShutdown() {
        try {
            Thread.sleep(SHUTDOWN_DELAY);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        }
        try {
            this.context.close();
        } catch (Exception ex) {
            try {
                log.warn("Cannot close context", ex);
            } catch (Exception innerEx) {
                System.err.println("Cannot close context and cannot log it. Exception is " + ex);
                ex.printStackTrace();
            }
        }
    }


    private void performForceShutdown() {
        try {
            Thread.sleep(FORCE_SHUTDOWN_TIMEOUT);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
        }
        System.out.println("Application did not shutdown in " + FORCE_SHUTDOWN_TIMEOUT + "s, performing force shutdown");
        System.exit(0);
    }

}
