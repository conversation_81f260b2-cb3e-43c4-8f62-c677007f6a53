package cz.kpsys.portaro.object;

import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class CompositeDepartmentedObjectModifier<V> implements TypedContextualObjectModifier<V> {

    @NonNull List<TypedContextualObjectModifier<V>> modifiers;

    @Override
    public V modify(V formObject, Department department) {
        for (TypedContextualObjectModifier<V> modifier : modifiers) {
            formObject = modifier.modify(formObject, department);
        }
        return formObject;
    }
}
