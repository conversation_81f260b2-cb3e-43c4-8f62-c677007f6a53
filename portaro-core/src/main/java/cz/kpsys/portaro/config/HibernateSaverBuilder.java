package cz.kpsys.portaro.config;

import cz.kpsys.portaro.appserver.GenericTableWriteSaver;
import cz.kpsys.portaro.appserver.dml.*;
import cz.kpsys.portaro.appserver.mapping.AppserverResponseHandler;
import cz.kpsys.portaro.appserver.mapping.JpaAppserverSaveResponseHandlerFactory;
import cz.kpsys.portaro.appserver.mapping.NoOpAppserverResponseHandler;
import cz.kpsys.portaro.commons.cache.AllCacheObjectIgnoringCleanerAdapter;
import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.convert.EToEConverter;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.springframework.core.convert.converter.Converter;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class HibernateSaverBuilder<IN_MODEL extends Identified<IN_MODEL_ID>, IN_MODEL_ID, SAVED_MODEL> {

    @NonNull CacheService cacheService;
    @NonNull DmlAppserverService dmlAppserverService;
    @NonNull Converter<IN_MODEL, SAVED_MODEL> toSavedModelConverter;
    @NonNull AppserverResponseHandler<IN_MODEL_ID> idAppserverResponseExtractor;
    @NonNull BiConsumer<IN_MODEL, IN_MODEL_ID> returnedObjectConsumer;
    @NonNull List<CacheCleaner> cacheCleaners = new ArrayList<>();
    @NonFinal boolean generateAsInsertOnly = false;

    public static <IN_MODEL extends Identified<IN_MODEL_ID>, IN_MODEL_ID> HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, IN_MODEL> of(
            @NonNull CacheService cacheService,
            @NonNull DmlAppserverService dmlAppserverService) {
        return new HibernateSaverBuilder<>(cacheService, dmlAppserverService, new EToEConverter<>(), NoOpAppserverResponseHandler.create(), (model, id) -> {});
    }

    public HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, SAVED_MODEL> withClearedCacheName(String clearedCacheName) {
        return withClearedCache(cacheService.createCleanerFor(clearedCacheName));
    }

    public HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, SAVED_MODEL> withClearedCache(CacheCleaner cacheCleaner) {
        this.cacheCleaners.add(cacheCleaner);
        return this;
    }

    public HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, SAVED_MODEL> generateWritesAsInsertOnly() {
        this.generateAsInsertOnly = true;
        return this;
    }

    public <NEW_SAVED_MODEL> HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, NEW_SAVED_MODEL> intermediateConverting(
            @NonNull Converter<SAVED_MODEL, NEW_SAVED_MODEL> intermediateConverter) {
        Converter<IN_MODEL, NEW_SAVED_MODEL> toNewModelConverter = toSavedModelConverter.andThen(intermediateConverter);
        return new HibernateSaverBuilder<>(
                cacheService,
                dmlAppserverService,
                toNewModelConverter,
                idAppserverResponseExtractor,
                returnedObjectConsumer
        );
    }

    public HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, SAVED_MODEL> idSaving(
            @NonNull AppserverResponseHandler<IN_MODEL_ID> idAppserverResponseExtractor,
            @NonNull BiConsumer<IN_MODEL, IN_MODEL_ID> returnedObjectConsumer) {
        return new HibernateSaverBuilder<>(
                cacheService,
                dmlAppserverService,
                toSavedModelConverter,
                idAppserverResponseExtractor,
                returnedObjectConsumer
        );
    }

    public HibernateSaverBuilder<IN_MODEL, IN_MODEL_ID, SAVED_MODEL> idSetting(
            @NonNull Class<? extends Identified<IN_MODEL_ID>> savingEntityClass,
            @NonNull Converter<String, IN_MODEL_ID> stringToModelIdConverter,
            @NonNull BiConsumer<IN_MODEL, IN_MODEL_ID> returnedObjectConsumer) {
        HibernateMetadataExtractor metadata = new HibernateMetadataExtractor(MetadataExtractorIntegrator.INSTANCE.getMetadata());
        AppserverResponseHandler<IN_MODEL_ID> appserverSaveResponseHandler = JpaAppserverSaveResponseHandlerFactory.forAnyIdentifier(metadata, savingEntityClass, stringToModelIdConverter);
        return idSaving(appserverSaveResponseHandler, returnedObjectConsumer);
    }

    public Saver<IN_MODEL, IN_MODEL> build() {
        HibernateMetadataExtractor metadata = new HibernateMetadataExtractor(MetadataExtractorIntegrator.INSTANCE.getMetadata());
        PreConvertingTableWriteGenerator<IN_MODEL, SAVED_MODEL> preConvertingTableWriteGenerator = new PreConvertingTableWriteGenerator<>(toSavedModelConverter, new SaveTableWriteGeneratorJpa<>(metadata, generateAsInsertOnly));
        return GenericTableWriteSaver.of(
                preConvertingTableWriteGenerator,
                dmlAppserverService,
                idAppserverResponseExtractor,
                returnedObjectConsumer,
                new AllCacheObjectIgnoringCleanerAdapter<>(cacheCleaners)
        );
    }

}
