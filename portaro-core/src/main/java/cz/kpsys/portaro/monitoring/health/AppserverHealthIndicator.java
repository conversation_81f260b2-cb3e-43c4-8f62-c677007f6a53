package cz.kpsys.portaro.monitoring.health;

import cz.kpsys.portaro.commons.health.Liveness;
import cz.kpsys.portaro.commons.object.Provider;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AppserverHealthIndicator extends AbstractHealthIndicator {

    @NonNull String appserverUrl;
    @NonNull AppserverHealthMonitor appserverHealthMonitor;
    @NonNull Provider<Boolean> appserverEnabledProvider;

    @Override
    protected void doHealthCheck(Health.Builder builder) {
        if (!appserverEnabledProvider.get()) {
            return; // status will default to unknown
        }

        Liveness liveness = appserverHealthMonitor.getLiveness();
        if (liveness.up()) {
            builder.up();
        } else {
            builder.down(liveness.downException());
        }
        builder.withDetail("location", appserverUrl);
        appserverHealthMonitor.getLastKnownVersion().ifPresent(version -> builder.withDetail("version", version));
    }

}
