package cz.kpsys.portaro.record.grid;

import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.form.valueeditor.date.DateValueEditor;
import cz.kpsys.portaro.form.valueeditor.inlinerecordsearch.InlineRecordSearchValueEditor;
import cz.kpsys.portaro.form.valueeditor.multipleacceptable.MultipleAcceptableValueEditor;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditor;
import cz.kpsys.portaro.form.valueeditor.record.RecordValueEditorOptions;
import cz.kpsys.portaro.form.valueeditor.singleacceptable.SingleAcceptableValueEditor;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.BasicValueEditorType;
import cz.kpsys.portaro.formannotation.annotations.valueeditor.ValueEditorAliasType;
import cz.kpsys.portaro.grid.GridFieldAttributeFactory;
import cz.kpsys.portaro.grid.GridFieldType;
import cz.kpsys.portaro.hierarchy.InheritanceLoader;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.fn.Ref;
import cz.kpsys.portaro.record.detail.link.LinkFieldTargetSearchMode;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldDisplayType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.view.ValueEditorByFieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EditableFieldTypesToGridResponsesConverter {

    @NonNull ValueEditorByFieldTypeLoader valueEditorByFieldTypeLoader;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull InheritanceLoader<Fond> enabledFondInheritanceLoader;
    @NonNull Provider<@NonNull ZoneId> timeZoneProvider;
    @NonNull FieldTypesByFondLoader editableFieldTypesByFondLoader;

    public @NonNull List<GridFieldType> convert(@NonNull List<? extends EditableFieldType> fieldTypes, @NonNull Fond fond) {
        List<Fond> expandedFonds = enabledLoadableFondsExpander.apply(fond);
        return map(fieldTypes, expandedFonds);
    }

    private @NonNull List<GridFieldType> map(List<? extends EditableFieldType> fieldTypes, List<Fond> expandedFonds) {
        List<Fond> recordableExpandedFonds = Fond.filterRecordable(expandedFonds);
        return fieldTypes.stream()
                .filter(fieldType -> FieldDisplayType.USABLE_IN_GRID.contains(fieldType.getDisplayType()))
                .sorted(EditableFieldType.EDITATION_ORDER_COMPARATOR)
                .map(ft -> mapFieldTypeToGridFieldType(ft, expandedFonds, recordableExpandedFonds))
                .toList();
    }

    private @NonNull GridFieldType mapFieldTypeToGridFieldType(EditableFieldType fieldType, List<Fond> expandedFonds, List<Fond> recordableExpandedFonds) {
        var subfondedEditableFieldTypes = new HashMap<Fond, Optional<EditableFieldType>>();
        for (var subfond : expandedFonds) {
            var subfondEditableType = editableFieldTypesByFondLoader.findByFondAndId(subfond, fieldType.getFieldTypeId(), FieldTypesByFondLoader.WhenMissing.RETURN_EMPTY);
            subfondedEditableFieldTypes.put(subfond, Optional.ofNullable(subfondEditableType));
        }

        var attributeFactory = new GridFieldAttributeFactory(fieldType, subfondedEditableFieldTypes);

        /*
         * Warning - all EditableFieldType (eft) value mapping functions should access only that type.
         * They are called also for EditableFieldTypes in subfonds!
         */
        return new GridFieldType(
                fieldType.getId(),
                fieldType.getText(),
                fieldType.getEditationOrder(),
                fieldType.getDatatype().orElse(null),
                attributeFactory.createForAllFields(EditableFieldTypesToGridResponsesConverter::isEnabled, () -> false),
                attributeFactory.createForFieldsDefinedBySubfonds(EditableFieldTypesToGridResponsesConverter::isFieldEditableInGrid),
                attributeFactory.createForFieldsDefinedBySubfonds(eft -> isHierarchicalRecordLink(eft, recordableExpandedFonds)),
                attributeFactory.createForFieldsDefinedBySubfonds(EditableFieldType::isRequired),
                attributeFactory.createForFieldsDefinedBySubfonds(this::getEditor),
                map(fieldType.getSubfieldTypes(), recordableExpandedFonds),
                getDependentFieldTypeIds(fieldType)
        );
    }

    private static boolean isEnabled(EditableFieldType fieldType) {
        return FieldDisplayType.USABLE_IN_GRID.contains(fieldType.getDisplayType());
    }

    private static boolean isFieldEditableInGrid(@NonNull EditableFieldType fieldType) {
        if (!FieldDisplayType.ALL_EDITABLE.contains(fieldType.getDisplayType())) {
            return false;
        }
        if (!fieldType.isWholeFieldGenerated()) {
            return true;
        }
        return fieldType.getFormula().isPresent() && fieldType.getFormula().orElseThrow() instanceof Ref<?> ref && ref.pointer().linkFieldSpec().linkFieldSearchMode() == LinkFieldTargetSearchMode.VIRTUAL_GROUP_FIELD_LINK;
    }

    private boolean isHierarchicalRecordLink(@NonNull EditableFieldType fieldType, @NonNull Collection<Fond> recordableExpandedFonds) {
        Optional<Fond> linkedRecordFond = fieldType.getLinkRootFond();
        if (linkedRecordFond.isEmpty()) {
            return false;
        }
        List<Fond> possibleLinkedRecordFonds = Fond.filterRecordable(enabledFondInheritanceLoader.getThisAndChildren(linkedRecordFond.get()));
        return ListUtil.hasIntersection(recordableExpandedFonds, possibleLinkedRecordFonds);
    }

    @Deprecated
    private @NonNull List<String> getDependentFieldTypeIds(@NonNull EditableFieldType fieldType) {
        return fieldType.getLookups().stream()
                .flatMap(lookupDef -> {
                    if (lookupDef.isSelfLinkOfSelfRecord()) {
                        // return lookupDef.existingLinkedFieldTypeId(); // deprecated - nyni muze byt linkovany na dynamicky field (vstupni prvek)
                        return Stream.empty();
                    }
                    return Stream.of(lookupDef.linkFieldSpec().existingFieldTypeId());
                })
                .map(FieldTypeId::value)
                .toList();
    }

    private ValueEditor<?, ?, ?> getEditor(@NonNull EditableFieldType fieldType) {
        Optional<ValueEditor<?, ?, ?>> resolvedEditor = valueEditorByFieldTypeLoader.getEditorIfDatatyped(fieldType);
        if (resolvedEditor.isEmpty()) {
            return null;
        }
        var editor = resolvedEditor.get();

        if (editor.getType().equals(BasicValueEditorType.DATE)) {
            var smallestFourDigitYear = LocalDate.of(1000, 1, 1)
                    .atStartOfDay()
                    .atZone(timeZoneProvider.get())
                    .toInstant();

            editor = ((DateValueEditor) editor).withMinDateValidation(() -> smallestFourDigitYear);
        } else if (editor.getType().equals(BasicValueEditorType.SINGLE_ACCEPTABLE)) {
            editor = ((SingleAcceptableValueEditor<?>) editor).withForcedDropdownMenu();
        } else if (editor.getType().equals(BasicValueEditorType.MULTIPLE_ACCEPTABLE)) {
            editor = ((MultipleAcceptableValueEditor<?>) editor).withForcedDropdownMenu();
        } else if (editor.getType().equals(ValueEditorAliasType.RECORD_SEARCH_OR_EDIT)) {
            var originalEditor = (RecordValueEditor) editor;

            editor = InlineRecordSearchValueEditor
                    .getEmptyEditor()
                    .withEditorId(originalEditor.getEditorId().orElse(null))
                    .withEditorName(originalEditor.getEditorName().orElse(null))
                    .withPlaceholder(originalEditor.getPlaceholder().orElse(null))
                    .withDisabled(originalEditor.getDisabled().orElse(false))
                    .withVisible(originalEditor.getVisible().orElse(null))
                    .withValidations(originalEditor.getValidations().orElse(null))
                    .withStaticSearchParams(originalEditor.getOptions().map(RecordValueEditorOptions::searchParams).orElse(null));
        }

        return editor;
    }

}
