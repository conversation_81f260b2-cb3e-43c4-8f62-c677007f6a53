package cz.kpsys.portaro.auth.sidechannel;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.department.Department;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CompositeVerificationDataSender implements VerificationDataSender {

    @NonNull List<ConditionalVerificationDataSender> senders = new ArrayList<>();

    public CompositeVerificationDataSender add(@NonNull ContextualProvider<Department, @NonNull Boolean> enabledProvider, @NonNull VerificationDataSender delegate) {
        senders.add(new ConditionalVerificationDataSender(enabledProvider, delegate));
        return this;
    }

    @Override
    public List<VerificationDataSendChannel> send(@NonNull VerificationDataSendCommand command) {
        return senders.stream()
                .flatMap(conditionalSender -> conditionalSender.send(command).stream())
                .toList();
    }

}
