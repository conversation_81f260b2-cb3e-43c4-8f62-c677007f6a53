package cz.kpsys.portaro.auth.sidechannel;

import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.mail.*;
import cz.kpsys.portaro.template.TemplateEngine;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MailVerificationDataSender implements VerificationDataSender {

    @NonNull MailService mailService;
    @NonNull TemplateEngine templateEngine;
    @NonNull UserLocaleResolver userLocaleResolver;

    @Override
    public List<VerificationDataSendChannel> send(@NonNull VerificationDataSendCommand command) {
        Map<String, Object> model = new HashMap<>();
        model.put("verificationCode", command.sideChannelCode());
        model.put("verificationLink", command.sideChannelLink());

        String body = templateEngine.build(SideChannelAuthConstants.TEMPLATE_MAIL, command.currentAuth(), command.ctx(), model, userLocaleResolver.resolveLocale(command.user(), command.ctx()));

        mailService.sendRawBodyMail(new NonspecificMailSendCommand(
                RawBodyMailTopics.USER_MAIL_VERIFICATION,
                command.ctx(),
                From.system(),
                To.user(command.user()),
                Texts.ofMessageCoded("login.sidechannelauthlinkmail.mail.subject"),
                body,
                List.of(),
                command.currentAuth()
        ));

        return List.of(VerificationDataSendChannel.EMAIL);
    }

}
