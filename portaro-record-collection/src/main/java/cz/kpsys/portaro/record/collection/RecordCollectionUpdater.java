package cz.kpsys.portaro.record.collection;

import cz.kpsys.portaro.auth.BasicUserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.record.collection.recordcollection.RecordCollection;
import cz.kpsys.portaro.record.collection.recordcollectioncategory.RecordCollectionCategory;
import cz.kpsys.portaro.record.collection.utils.RecordCollectionsTreeMaxDepthGuard;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.SearchableRepository;
import cz.kpsys.portaro.search.StaticParamsModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.Optional;
import java.util.UUID;

import static cz.kpsys.portaro.record.collection.RecordCollectionConstants.SearchParams.RECORD_COLLECTION_ANCESTOR;


@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class RecordCollectionUpdater {
    @NonNull SearchableRepository<MapBackedParams, RecordCollection, UUID> recordCollectionSearchableRepository;
    @NonNull RecordCollectionOrderProvider recordCollectionOrderProvider;
    @NonNull RecordCollectionsTreeMaxDepthGuard recordCollectionsTreeMaxDepthGuard;
    @NonNull Eventer eventer;

    public RecordCollection update(@NonNull RecordCollection originalRecordCollection, @NonNull RecordCollectionEditationCommand command, Department currentDepartment, BasicUserAuthentication currentAuth) {

        var optionalParentCollection = Optional.ofNullable(command.getParentCollection());

        int updatedOrder;
        if (!originalRecordCollection.getParentId().equals(optionalParentCollection.map(RecordCollection::getId))) {
            // if moving subtree, add to the end (set order same as during creating new subcollection)
            updatedOrder = optionalParentCollection.map(recordCollectionOrderProvider::getOrderForSubCollection).orElse(recordCollectionOrderProvider.getOrderForRootCollection(originalRecordCollection.getOwner()));
        } else {
            updatedOrder = command.getOrder();
        }

        Event updateEvent = eventer.save(Event.Codes.RECORD_COLLECTION_UPDATE, currentAuth, currentDepartment, null, originalRecordCollection.getId());

        RecordCollection recordCollection = new RecordCollection(
                originalRecordCollection.getId(),
                optionalParentCollection.map(RecordCollection::getId),
                originalRecordCollection.getDepartment(),
                command.getName(),
                updatedOrder,
                Optional.ofNullable(command.getNote()),
                command.getRecordCollectionCategory(),
                originalRecordCollection.getOwner(),
                originalRecordCollection.getCreationEventId(),
                updateEvent.getId());

        if (!recordCollection.getParentId().equals(originalRecordCollection.getParentId())) {
            throwIfHierarchyContainsCycle(recordCollection);
            recordCollectionsTreeMaxDepthGuard.throwIfTreeDepthOverThreshold(recordCollection);
        }

        if (!recordCollection.getRecordCollectionCategory().equals(originalRecordCollection.getRecordCollectionCategory())) {
            updateSubcollectionsCategories(recordCollection, recordCollection.getRecordCollectionCategory(), currentDepartment, currentAuth);
        }

        recordCollectionSearchableRepository.save(recordCollection);
        return recordCollection;
    }

    private void updateSubcollectionsCategories(RecordCollection recordCollection, RecordCollectionCategory category, Department currentDepartment, BasicUserAuthentication currentAuth) {
        var subcollections = recordCollectionSearchableRepository.getContent(RangePaging.forAll(), StaticParamsModifier.of(RECORD_COLLECTION_ANCESTOR, recordCollection.getId()));

        subcollections.stream().map(subcollection -> updateCategory(subcollection, category, currentDepartment, currentAuth)).forEach(recordCollectionSearchableRepository::save);
    }

    private RecordCollection updateCategory(RecordCollection originalRecordCollection, RecordCollectionCategory category, Department currentDepartment, BasicUserAuthentication currentAuth) {
        Event updateEvent = eventer.save(Event.Codes.RECORD_COLLECTION_UPDATE, currentAuth, currentDepartment, null, originalRecordCollection.getId());
        return originalRecordCollection.withRecordCollectionCategory(category).withLastUpdateEventId(updateEvent.getId());
    }

    private void throwIfHierarchyContainsCycle(@NonNull RecordCollection collection) {
        if (isRecursivelyDetectedCycle(collection.getId(), collection.getParentId())) {
            throw new RuntimeException("Record collection hierarchy can not contain cycles");
        }
    }


    private boolean isRecursivelyDetectedCycle(UUID startingId, Optional<UUID> parentId) {
        if (parentId.isEmpty()) {
            return false;
        }

        if (parentId.get().equals(startingId)) {
            return true;
        }

        return isRecursivelyDetectedCycle(startingId, recordCollectionSearchableRepository.getById(parentId.get()).getParentId());
    }
}
