package cz.kpsys.portaro.record.collection.userrecordcollection;

import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class EntitiesToUserRecordCollectionsConverter implements Converter<List<? extends UserRecordCollectionEntity>, List<UserRecordCollection>> {

    @NonNull IdAndIdsLoadable<BasicUser, Integer> basicUserLoader;

    @Override
    public List<UserRecordCollection> convert(@NonNull List<? extends UserRecordCollectionEntity> entities) {
        var entityToUserMap = BatchFiller.of(basicUserLoader).load(entities, UserRecordCollectionEntity::getUserId);
        return ListUtil.convert(entities, entity -> convertSingle(entity, entityToUserMap.get(entity)));
    }

    private UserRecordCollection convertSingle(UserRecordCollectionEntity source, BasicUser basicUser) {
        return new UserRecordCollection(
                source.getId(),
                basicUser,
                source.getRecordCollectionId());
    }
}
