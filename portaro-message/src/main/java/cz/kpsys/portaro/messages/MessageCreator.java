package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreateCommand;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreator;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.dto.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MessageCreator {

    @NonNull Saver<Message, ?> messageSaver;
    @NonNull Saver<MessageSending, ?> messageSendingSaver;
    @NonNull Eventer eventer;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull MessagePublisher messagePublisher;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;
    @NonNull ByIdLoadable<FileAccessType, Integer> fileAccessTypeLoader;

    public Message createInternal(@NonNull NewThreadMessageCommand command) {
        return transactionTemplate.execute(_ -> {
            Event creationEvent = eventer.save(Event.Codes.MESSAGE_CREATION, command.currentAuth(), command.ctx(), null);

            UUID messageId = UuidGenerator.forIdentifier();
            Integer directoryId = getDirectoryId(messageId, command);

            UserToThreadMessage message = Message.ofUserToThread(
                    messageId,
                    command.content(),
                    MessageTopic.COMMON,
                    MessageSeverity.INFO,
                    command.sender(),
                    command.thread(),
                    command.ctx(),
                    creationEvent,
                    false,
                    ContentType.MARKDOWN,
                    directoryId,
                    Instant.now(),
                    null
            );

            Event sendingEvent = eventer.save(Event.Codes.MESSAGE_SENDING, command.currentAuth(), command.ctx(), null);

            MessageSendingInternal messageSending = new MessageSendingInternal(
                    message.id(),
                    message.id(),
                    sendingEvent,
                    null,
                    null,
                    command.content()
            );

            messageSaver.save(message);
            messageSendingSaver.save(messageSending);

            if (!command.withFiles()) {
                messagePublisher.publish(message);
            }

            return message;
        });
    }

    private @Nullable Integer getDirectoryId(@NonNull UUID messageId, @NonNull NewThreadMessageCommand command) {
        if (command.withFiles()) {
            return parentableDirectoryCreator.create(ParentableDirectoryCreateCommand.ofNewDirectory(command.thread().getDirectoryId(), messageId.toString(), fileAccessTypeLoader.getById(FileAccessType.THREAD_PARTICIPANT_ONLY))).getId();
        }
        return null;
    }
}
