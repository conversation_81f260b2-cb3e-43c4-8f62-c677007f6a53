package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;


@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public final class MessageSendingSms extends MessageSending {

    @NonNull String recipientPhoneNumber;
    @NonNull String content;
    @Nullable String errorMessage;

    public MessageSendingSms(@NonNull UUID id, @NonNull UUID messageId, @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent, @NonNull String recipientPhoneNumber, @NonNull String content, @Nullable String errorMessage) {
        super(id, messageId, MessageMedium.SMS, sendingEvent, receiptionEvent, confirmationEvent);
        this.recipientPhoneNumber = recipientPhoneNumber;
        this.content = content;
        this.errorMessage = errorMessage;
    }

    public MessageSendingSms(@NonNull UUID messageId,  @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent, @NonNull String recipientPhoneNumber, @NonNull String content, @Nullable String errorMessage) {
        this(messageId, messageId, sendingEvent, receiptionEvent, confirmationEvent, recipientPhoneNumber, content, errorMessage);
    }
}
