package cz.kpsys.portaro.messages.sms;

import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.object.repo.SeveritedItemNotFoundException;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.MessageSending;
import cz.kpsys.portaro.messages.dto.MessageSendingSms;
import cz.kpsys.portaro.user.contact.ContactManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class SmsMessageSender implements SmsSender {

    @NonNull Saver<Message, ?> messageSaver;
    @NonNull Saver<MessageSending, ?> messageSendingSaver;
    @NonNull Eventer eventer;
    @NonNull ContactManager contactManager;

    @Transactional
    @Override
    public void send(SmsSenderCommand command) {

        String smsNumber = contactManager.getSmsPhone(command.recipient())
                .orElseThrow(() -> new SeveritedItemNotFoundException("SMS phone number", "User %s".formatted(command.recipient().getId()), SeveritedException.SEVERITY_ERROR));

        Event creationEvent = eventer.save(Event.Codes.MESSAGE_CREATION, command.currentAuth(), command.ctx(), null);

        Message message = Message.ofUserToUser(
                UuidGenerator.forIdentifier(),
                command.content(),
                command.messageTopic(),
                command.severity(),
                command.sender(),
                command.recipient(),
                command.ctx(),
                creationEvent,
                false,
                null,
                null,
                Instant.now(),
                Instant.now()
        );

        MessageSendingSms messageSending = new MessageSendingSms(
                message.id(),
                null,
                null,
                null,
                smsNumber,
                command.content(),
                null
        );

        messageSaver.save(message);
        messageSendingSaver.save(messageSending);
    }
}
