package cz.kpsys.portaro.messages.db.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_INTERNAL.BODY;
import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_INTERNAL.TABLE;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class MessageSendingInternalEntity extends MessageSendingEntity {

    @Column(name = BODY)
    @NotNull
    String body;

    public MessageSendingInternalEntity(@NotNull UUID id,
                                        @NotNull UUID messageId,
                                        @NotNull String messageMediumId,
                                        @NotNull UUID sendingEventId,
                                        @NotNull UUID receiptionEventId,
                                        @NotNull UUID confirmationEventId,
                                        @NotNull String body) {
        super(id, messageId, messageMediumId, sendingEventId, receiptionEventId, confirmationEventId);
        this.body = body;
    }
}
