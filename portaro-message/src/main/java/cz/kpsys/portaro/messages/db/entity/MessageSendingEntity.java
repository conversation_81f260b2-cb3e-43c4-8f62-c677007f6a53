package cz.kpsys.portaro.messages.db.entity;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING.*;

@Entity
@Table(name = TABLE)
@Inheritance(strategy = InheritanceType.JOINED)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public abstract class MessageSendingEntity implements Identified<UUID> {

    @Id
    @Column(name = ID)
    @EqualsAndHashCode.Include
    @NonNull
    UUID id;

    @Column(name = MESSAGE_ID)
    @NonNull
    UUID messageId;

    @Column(name = MESSAGE_MEDIUM_ID)
    @NonNull
    String messageMediumId;

    @Column(name = SENDING_EVENT_ID)
    @Nullable
    UUID sendingEventId;

    @Column(name = RECEIPTION_EVENT_ID)
    @Nullable
    UUID receiptionEventId;

    @Column(name = CONFIRMATION_EVENT_ID)
    @Nullable
    UUID confirmationEventId;

}
