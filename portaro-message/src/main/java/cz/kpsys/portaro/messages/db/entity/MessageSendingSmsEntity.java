package cz.kpsys.portaro.messages.db.entity;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_SMS.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class MessageSendingSmsEntity extends MessageSendingEntity {

    @Column(name = RECIPIENT_PHONE_NUMBER)
    @Size(min = 1, max = 40)
    @NotBlank
    String recipientPhoneNumber;

    @Column(name = CONTENT)
    @NotNull
    String content;

    @Column(name = ERROR_MESSAGE)
    @Size(min = 1, max = 1024)
    @NullableNotBlank
    String errorMessage;

    public MessageSendingSmsEntity(@NotNull UUID id,
                                   @NotNull UUID messageId,
                                   @NotNull String messageMediumId,
                                   @NotNull UUID sendingEventId,
                                   @NotNull UUID receiptionEventId,
                                   @NotNull UUID confirmationEventId,
                                   @NotNull String recipientPhoneNumber,
                                   @NotNull String content,
                                   @NullableNotBlank String errorMessage) {
        super(id, messageId, messageMediumId, sendingEventId, receiptionEventId, confirmationEventId);
        this.recipientPhoneNumber = recipientPhoneNumber;
        this.content = content;
        this.errorMessage = errorMessage;
    }
}
