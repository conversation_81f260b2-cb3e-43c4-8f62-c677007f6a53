package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public abstract sealed class MessageSending implements Identified<UUID> permits MessageSendingEmail, MessageSendingInternal, MessageSendingPost, MessageSendingSms {

    @NonNull
    UUID id;

    @NonNull
    UUID messageId;

    @NonNull
    MessageMedium messageMedium;

    @Nullable
    Event sendingEvent;

    @Nullable
    Event receiptionEvent;

    @Nullable
    Event confirmationEvent;

    public MessageSending(@NonNull UUID id, @NonNull UUID messageId, @NonNull MessageMedium messageMedium, @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent) {
        this.id = id;
        this.messageId = messageId;
        this.messageMedium = messageMedium;
        this.sendingEvent = sendingEvent;
        this.receiptionEvent = receiptionEvent;
        this.confirmationEvent = confirmationEvent;
    }
}
