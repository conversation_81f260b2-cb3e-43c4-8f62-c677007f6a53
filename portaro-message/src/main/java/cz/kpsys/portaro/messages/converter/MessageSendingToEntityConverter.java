package cz.kpsys.portaro.messages.converter;

import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.db.entity.*;
import cz.kpsys.portaro.messages.dto.*;
import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

import java.util.Optional;

public class MessageSendingToEntityConverter implements Converter<MessageSending, MessageSendingEntity> {

    @Override
    public MessageSendingEntity convert(@NonNull MessageSending dto) {
        return switch (dto) {
            case MessageSendingSms messageSendingSms -> new MessageSendingSmsEntity(
                    dto.getId(),
                    dto.getMessageId(),
                    dto.getMessageMedium().getId(),
                    Optional.ofNullable(dto.getSendingEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getReceiptionEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getConfirmationEvent()).map(Event::getId).orElse(null),
                    messageSendingSms.getRecipientPhoneNumber(),
                    messageSendingSms.getContent(),
                    messageSendingSms.getErrorMessage()
            );
            case MessageSendingEmail messageSendingEmail -> new MessageSendingEmailEntity(
                    dto.getId(),
                    dto.getMessageId(),
                    dto.getMessageMedium().getId(),
                    Optional.ofNullable(dto.getSendingEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getReceiptionEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getConfirmationEvent()).map(Event::getId).orElse(null),
                    messageSendingEmail.getSubject(),
                    messageSendingEmail.getBody(),
                    messageSendingEmail.getAttachmentDirectoryId(),
                    messageSendingEmail.getErrorMessage()
            );
            case MessageSendingPost messageSendingPost -> new MessageSendingPostEntity(
                    dto.getId(),
                    dto.getMessageId(),
                    dto.getMessageMedium().getId(),
                    Optional.ofNullable(dto.getSendingEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getReceiptionEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getConfirmationEvent()).map(Event::getId).orElse(null),
                    messageSendingPost.getAddress(),
                    messageSendingPost.getFulltextId(),
                    Optional.ofNullable(messageSendingPost.getPrintingEvent()).map(Event::getId).orElse(null)

            );
            case MessageSendingInternal messageSendingInternal -> new MessageSendingInternalEntity(
                    dto.getId(),
                    dto.getMessageId(),
                    dto.getMessageMedium().getId(),
                    Optional.ofNullable(dto.getSendingEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getReceiptionEvent()).map(Event::getId).orElse(null),
                    Optional.ofNullable(dto.getConfirmationEvent()).map(Event::getId).orElse(null),
                    messageSendingInternal.getBody()
            );
        };

    }
}
