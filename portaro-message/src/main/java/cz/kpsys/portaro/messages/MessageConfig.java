package cz.kpsys.portaro.messages;

import cz.kpsys.portaro.commons.convert.EToEConverter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.provider.SingleMatchingProvider;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.config.ConverterRegisterer;
import cz.kpsys.portaro.config.SaverBuilderFactory;
import cz.kpsys.portaro.database.IdAndIdsLoadableJpa;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.event.Eventer;
import cz.kpsys.portaro.eventbus.EventBus;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.ParentableDirectoryCreator;
import cz.kpsys.portaro.file.text.pdf.PdfByIdentifiedFilesMerger;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import cz.kpsys.portaro.messages.constants.MessageSendingsSendStatus;
import cz.kpsys.portaro.messages.constants.MessageSeverity;
import cz.kpsys.portaro.messages.constants.MessageTopic;
import cz.kpsys.portaro.messages.converter.*;
import cz.kpsys.portaro.messages.db.SpringDbMessageIdSearchLoader;
import cz.kpsys.portaro.messages.db.SpringDbMessageSendingIdSearchLoader;
import cz.kpsys.portaro.messages.db.SpringDbMessageSendingLoader;
import cz.kpsys.portaro.messages.db.entity.MessageEntity;
import cz.kpsys.portaro.messages.db.entity.MessageSendingEmailAddressEntity;
import cz.kpsys.portaro.messages.db.entity.MessageSendingEmailAddressToEntityConverter;
import cz.kpsys.portaro.messages.db.entity.MessageSendingEntity;
import cz.kpsys.portaro.messages.dto.Message;
import cz.kpsys.portaro.messages.dto.MessageSending;
import cz.kpsys.portaro.messages.dto.MessageSendingEmailAddress;
import cz.kpsys.portaro.messages.participants.*;
import cz.kpsys.portaro.messages.sms.SmsApiController;
import cz.kpsys.portaro.messages.sms.SmsMessageSender;
import cz.kpsys.portaro.messages.sms.SmsSender;
import cz.kpsys.portaro.messages.snailmail.MessageSendingSavingPostSender;
import cz.kpsys.portaro.messages.snailmail.PostSender;
import cz.kpsys.portaro.messages.thread.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordEditationHelper;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondType;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.security.PermissionFactory;
import cz.kpsys.portaro.security.PermissionRegistry;
import cz.kpsys.portaro.security.PermissionResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.contact.ContactManager;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.core.support.RepositoryFactorySupport;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.UUID;

import static cz.kpsys.portaro.security.PermissionResolver.adaptingSubject;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class MessageConfig {

    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull AllByIdsLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull AllByIdsLoadable<Event, UUID> eventLoader;
    @NonNull Eventer eventer;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull PdfByIdentifiedFilesMerger pdfByIdentifiedFilesMerger;
    @NonNull Codebook<Department, Integer> departmentLoader;
    @NonNull ContactManager contactManager;
    @NonNull EntityManager entityManager;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull RecordEditationHelper recordEditationHelper;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull RepositoryFactorySupport jpaRepositoryFactory;
    @NonNull IdAndIdsLoadable<BasicUser, UUID> basicUserByUuidLoader;
    @NonNull EventBus eventBus;
    @NonNull SecurityManager securityManager;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;
    @NonNull ByIdLoadable<FileAccessType, Integer> fileAccessTypeLoader;

    @Bean
    public IdAndIdsLoadable<Message, UUID> messageLoader() {
        MessageFromEntityConverter messageFromEntityConverter = new MessageFromEntityConverter(basicUserLoader, recordLoader, eventLoader, new AllByIdsLoadableByAllValuesProviderAdapter<>(departmentLoader), messageSendingBatchLoader());
        IdAndIdsLoadableJpa<MessageEntity, UUID> jpaLoader = new IdAndIdsLoadableJpa<>(new SimpleJpaRepository<>(MessageEntity.class, entityManager));
        ChunkingAllByIdsLoader<Message, UUID, UUID> allByIdsLoader = ChunkingAllByIdsLoader.ofIdentified(new IdAndIdsLoadableConverting<>(jpaLoader, new EToEConverter<>(), messageFromEntityConverter));
        return new ByIdLoadableByAllByIdsLoadable<>(allByIdsLoader, Message.class);
    }

    @Bean
    public MessageSendingBatchLoader messageSendingBatchLoader() {
        return new MessageSendingBatchLoader(messageSendingSearchLoader());
    }

    @Bean
    public Saver<Message, MessageEntity> messageSaver() {
        return saverBuilderFactory.jpaSaver(Message.class)
                .intermediateConverting(new MessageToEntityConverter(), MessageEntity.class)
                .build();
    }

    @Bean
    public PageSearchLoader<MapBackedParams, UUID, Paging> messageIdSearchLoader() {
        return new SpringDbMessageIdSearchLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public PageSearchLoader<MapBackedParams, Message, Paging> messageSearchLoader() {
        return ResultConvertingPageSearchLoader.createConvertingFromIds(messageIdSearchLoader(), messageLoader());
    }

    @Bean
    public PageSearchLoader<MapBackedParams, MessageSending, RangePaging> messageSendingSearchLoader() {
        return ResultConvertingPageSearchLoader.createConvertingFromIds(messageSendingIdSearchLoader(), messageSendingLoader());
    }

    private @NonNull SpringDbMessageSendingIdSearchLoader messageSendingIdSearchLoader() {
        return new SpringDbMessageSendingIdSearchLoader(jdbcTemplate, queryFactory);
    }

    @Bean
    public IdAndIdsLoadable<MessageSending, UUID> messageSendingLoader() {
        SpringDbMessageSendingLoader springDbMessageSendingLoader = new SpringDbMessageSendingLoader(jdbcTemplate, queryFactory);
        MessageSendingFromEntityConverter messageSendingFromEntityConverter = new MessageSendingFromEntityConverter(eventLoader);
        return new IdAndIdsLoadableConverting<>(springDbMessageSendingLoader, id -> id, messageSendingFromEntityConverter);
    }

    @Bean
    public ThreadApiController threadApiController() {
        return new ThreadApiController(threadCreator(), messageCreator(), threadLoader(), threadUpdater(), threadToResponseConverter(), userThreadsLoader(), userThreadInfoToResponseConverter(), messagePublisher());
    }

    @Bean
    public MessagePublisher messagePublisher() {
        return new MessagePublisher(messageSaver(), threadMessageNotifier(), defaultTransactionTemplateFactory.get());
    }

    @Bean
    public UserThreadInfoToResponseConverter userThreadInfoToResponseConverter() {
        return new UserThreadInfoToResponseConverter(threadToResponseConverter());
    }

    @Bean
    public UserThreadsLoader userThreadsLoader() {
        return new UserThreadsLoader(queryFactory, jdbcTemplate, recordLoader);
    }

    @Bean
    public ThreadUpdater threadUpdater() {
        return new ThreadUpdater(threadParticipantSaver(), threadParticipantRemover(), threadParticipantLoader(), recordEditationFactory, recordEditationHelper, defaultTransactionTemplateFactory.get(), securityManager);
    }

    @Bean
    public ThreadParticipantRemover threadParticipantRemover() {
        return new ThreadParticipantRemover(threadParticipantRepository());
    }

    @Bean
    public ThreadToResponseConverter threadToResponseConverter() {
        return new ThreadToResponseConverter(threadParticipantRepository(), basicUserByUuidLoader);
    }

    @Bean
    public ThreadCreator threadCreator() {
        return new ThreadCreator(threadFondProvider().throwingWhenNull(), recordEditationFactory, threadParticipantSaver(), threadParticipantLoader(), recordEditationHelper, defaultTransactionTemplateFactory.get());
    }

    @Bean
    public ThreadParticipantSaver threadParticipantSaver() {
        return new ThreadParticipantSaver(threadParticipantRepository());
    }

    @Bean
    public ThreadLoader threadLoader() {
        return new ThreadLoader(threadParticipantLoader(), recordLoader);
    }

    @Bean
    public ThreadParticipantLoader threadParticipantLoader() {
        return new ThreadParticipantLoader(threadParticipantRepository());
    }

    @Bean
    public ThreadParticipantEntityLoader threadParticipantRepository() {
        return jpaRepositoryFactory.getRepository(ThreadParticipantEntityLoader.class);
    }

    @Bean
    public Provider<@Nullable Fond> threadFondProvider() {
        return SingleMatchingProvider.of(fondLoader, FondType.THREAD::matches);
    }

    @Bean
    public Saver<MessageSending, MessageSendingEntity> messageSendingSaver() {
        return saverBuilderFactory.jpaSaver(MessageSending.class)
                .intermediateConverting(new MessageSendingToEntityConverter(), MessageSendingEntity.class)
                .build();
    }

    @Bean
    public Saver<MessageSendingEmailAddress, MessageSendingEmailAddressEntity> messageSendingEmailAddressSaver() {
        return saverBuilderFactory.jpaSaver(MessageSendingEmailAddress.class)
                .intermediateConverting(new MessageSendingEmailAddressToEntityConverter(), MessageSendingEmailAddressEntity.class)
                .build();
    }

    @Bean
    public SmsSender smsSender() {
        return new SmsMessageSender(
                messageSaver(),
                messageSendingSaver(),
                eventer,
                contactManager
        );
    }

    @Bean
    public PostSender postSender() {
        return new MessageSendingSavingPostSender(
                messageSendingSaver(),
                eventer,
                identifiedFileLoader,
                pdfByIdentifiedFilesMerger
        );
    }

    @Bean
    public MessageApiController messageApiController() {
        return new MessageApiController(messageSendingSearchLoader(), postSender());
    }

    @Bean
    public MessageCreator messageCreator() {
        return new MessageCreator(messageSaver(), messageSendingSaver(), eventer, defaultTransactionTemplateFactory.get(), messagePublisher(), parentableDirectoryCreator, fileAccessTypeLoader);
    }

    @Bean
    public ThreadMessageNotifier threadMessageNotifier() {
        return new ThreadMessageNotifier(basicUserByUuidLoader, threadParticipantLoader(), threadParticipantSaver());
    }

    @Bean
    public SmsApiController smsApiController() {
        return new SmsApiController(smsSender(), contactManager);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPublisher() {
        eventBus.registerPublisher(threadMessageNotifier());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForStringId(ThreadParticipantType.class, ThreadParticipantType.CODEBOOK)
                .registerForIntegerId(MessageTopic.class, MessageTopic.CODEBOOK)
                .registerForStringId(MessageSeverity.class, MessageSeverity.CODEBOOK)
                .registerForStringId(MessageMedium.class, MessageMedium.CODEBOOK)
                .registerForStringId(MessageSendingsSendStatus.class, MessageSendingsSendStatus.CODEBOOK)
                .registerForUuidId(Message.class, messageLoader());
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerPermissions() {
        permissionRegistry.add(MessageSecurityActions.MESSAGE_SEARCH, permissionFactory.edit());
        permissionRegistry.add(MessageSecurityActions.THREAD_MESSAGE_SEARCH, adaptingSubject(
                threadParticipantLoader()::getAllByThreadId,
                (auth, _, threadParticipants) -> {
                    if (MessageSecurityHelper.noMainThreadParticipantsPresent(threadParticipants) && MessageSecurityHelper.currentAuthIsParticipant(auth, threadParticipants)) {
                        return PermissionResult.allow();
                    }
                    return PermissionResult.cannot(auth);
                }
        ));
        permissionRegistry.add(MessageSecurityActions.EDIT_THREAD, adaptingSubject(
                threadParticipantLoader()::getAllByThreadId,
                (auth, _, threadParticipants) -> {
                    if (MessageSecurityHelper.noMainThreadParticipantsPresent(threadParticipants) && MessageSecurityHelper.currentAuthIsAdminParticipant(auth, threadParticipants)) {
                        return PermissionResult.allow();
                    }
                    return PermissionResult.cannot(auth);
                }
        ));
    }
}
