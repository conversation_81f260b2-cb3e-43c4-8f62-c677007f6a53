package cz.kpsys.portaro.messages.db;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import cz.kpsys.portaro.messages.db.entity.*;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.MessageDb.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbMessageSendingLoader implements AllByIdsLoadable<MessageSendingEntity, UUID>, RowMapper<MessageSendingEntity> {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;

    @Override
    public List<MessageSendingEntity> getAllByIds(@NonNull List<UUID> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }

        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(
                // MESSAGE_SENDING
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID)),
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID)),
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID)),
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID)),
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID)),
                AS(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID), alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID)),

                // MESSAGE_SENDING_EMAIL
                AS(TC(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.SUBJECT), alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.SUBJECT)),
                AS(TC(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.BODY), alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.BODY)),
                AS(TC(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ATTACHMENT_DIRECTORY_ID), alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ATTACHMENT_DIRECTORY_ID)),
                AS(TC(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ERROR_MESSAGE), alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ERROR_MESSAGE)),

                // MESSAGE_SENDING_INTERNAL
                AS(TC(MESSAGE_SENDING_INTERNAL.TABLE, MESSAGE_SENDING_INTERNAL.BODY), alias(MESSAGE_SENDING_INTERNAL.TABLE, MESSAGE_SENDING_INTERNAL.BODY)),

                // MESSAGE_SENDING_POST
                AS(TC(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.ADDRESS), alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.ADDRESS)),
                AS(TC(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.FK_FULLTEXT_CONTENT), alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.FK_FULLTEXT_CONTENT)),
                AS(TC(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.PRINTING_EVENT_ID), alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.PRINTING_EVENT_ID)),

                // MESSAGE_SENDING_SMS
                AS(TC(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.RECIPIENT_PHONE_NUMBER), alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.RECIPIENT_PHONE_NUMBER)),
                AS(TC(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.CONTENT), alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.CONTENT)),
                AS(TC(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.ERROR_MESSAGE), alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.ERROR_MESSAGE))
        );

        sq.from(MESSAGE_SENDING.TABLE);

        sq.joins()
                .addLeft(MESSAGE_SENDING_EMAIL.TABLE, COLSEQ(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), TC(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ID)))
                .addLeft(MESSAGE_SENDING_INTERNAL.TABLE, COLSEQ(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), TC(MESSAGE_SENDING_INTERNAL.TABLE, MESSAGE_SENDING_INTERNAL.ID)))
                .addLeft(MESSAGE_SENDING_POST.TABLE, COLSEQ(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), TC(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.ID)))
                .addLeft(MESSAGE_SENDING_SMS.TABLE, COLSEQ(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), TC(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.ID)));

        sq.where().in(TC(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID), ids);

        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    private String alias(String table, String col) {
        String alias = table + "_" + col;
        return ("A" + (alias.hashCode())).replaceAll("-", "_");
    }

    @Override
    public MessageSendingEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
        String medium = rs.getString(alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID));
        return createMessageSendingByMedium(rs, medium);
    }

    private MessageSendingEntity createMessageSendingByMedium(ResultSet rs, String medium) throws SQLException {
        if (MessageMedium.EMAIL.getId().equals(medium)) {
            return createMessageSendingEmailEntity(rs);
        }
        if (MessageMedium.INTERNAL.getId().equals(medium)) {
            return createMessageSendingInternalEntity(rs);
        }
        if (MessageMedium.POST.getId().equals(medium)) {
            return createMessageSendingPostEntity(rs);
        }
        if (MessageMedium.SMS.getId().equals(medium)) {
            return createMessageSendingSmsEntity(rs);
        }
        throw new IllegalStateException("Unknown message_sendings medium: %s".formatted(medium));
    }

    private MessageSendingSmsEntity createMessageSendingSmsEntity(ResultSet rs) throws SQLException {
        return new MessageSendingSmsEntity(
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID)),
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID)),
                rs.getString(alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID)),
                rs.getString(alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.RECIPIENT_PHONE_NUMBER)),
                rs.getString(alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.CONTENT)),
                rs.getString(alias(MESSAGE_SENDING_SMS.TABLE, MESSAGE_SENDING_SMS.ERROR_MESSAGE))
        );
    }

    private MessageSendingPostEntity createMessageSendingPostEntity(ResultSet rs) throws SQLException {
        return new MessageSendingPostEntity(
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID)),
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID)),
                rs.getString(alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID)),
                rs.getString(alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.ADDRESS)),
                DbUtils.getInteger(rs, alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.FK_FULLTEXT_CONTENT)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING_POST.TABLE, MESSAGE_SENDING_POST.PRINTING_EVENT_ID))
        );
    }

    private MessageSendingInternalEntity createMessageSendingInternalEntity(ResultSet rs) throws SQLException {
        return new MessageSendingInternalEntity(
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID)),
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID)),
                rs.getString(alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID)),
                rs.getString(alias(MESSAGE_SENDING_INTERNAL.TABLE, MESSAGE_SENDING_INTERNAL.BODY))
        );
    }

    private MessageSendingEmailEntity createMessageSendingEmailEntity(ResultSet rs) throws SQLException {
        return new MessageSendingEmailEntity(
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.ID)),
                DbUtils.uuidNotNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_ID)),
                rs.getString(alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.MESSAGE_MEDIUM_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.SENDING_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.RECEIPTION_EVENT_ID)),
                DbUtils.uuidOrNull(rs, alias(MESSAGE_SENDING.TABLE, MESSAGE_SENDING.CONFIRMATION_EVENT_ID)),
                rs.getString(alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.SUBJECT)),
                rs.getString(alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.BODY)),
                DbUtils.getInteger(rs, alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ATTACHMENT_DIRECTORY_ID)),
                rs.getString(alias(MESSAGE_SENDING_EMAIL.TABLE, MESSAGE_SENDING_EMAIL.ERROR_MESSAGE))
        );
    }
}
