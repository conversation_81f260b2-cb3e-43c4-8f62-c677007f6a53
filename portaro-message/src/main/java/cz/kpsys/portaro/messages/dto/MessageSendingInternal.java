package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;


@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public final class MessageSendingInternal extends MessageSending {

    @NonNull
    String body;

    public MessageSendingInternal(@NonNull UUID id, @NonNull UUID messageId, @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent, @NonNull String body) {
        super(id, messageId, MessageMedium.INTERNAL, sendingEvent, receiptionEvent, confirmationEvent);
        this.body = body;
    }
}
