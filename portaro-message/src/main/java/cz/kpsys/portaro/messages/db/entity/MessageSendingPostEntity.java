package cz.kpsys.portaro.messages.db.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_POST.*;

@Entity
@Table(name = TABLE)
@PrimaryKeyJoinColumn(name = ID)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class MessageSendingPostEntity extends MessageSendingEntity {

    @Column(name = ADDRESS)
    @Size(min = 1, max = 1024)
    @NotBlank
    String address;

    @Column(name = FK_FULLTEXT_CONTENT)
    Integer fkFulltextContent;

    @Column(name = PRINTING_EVENT_ID)
    UUID printingEventId;

    public MessageSendingPostEntity(@NotNull UUID id,
                                    @NotNull UUID messageId,
                                    @NotNull String messageMediumId,
                                    @NotNull UUID sendingEventId,
                                    @NotNull UUID receiptionEventId,
                                    @NotNull UUID confirmationEventId,
                                    @NotNull String address,
                                    Integer fkFulltextContent,
                                    UUID printingEventId) {
        super(id, messageId, messageMediumId, sendingEventId, receiptionEventId, confirmationEventId);
        this.address = address;
        this.fkFulltextContent = fkFulltextContent;
        this.printingEventId = printingEventId;
    }
}
