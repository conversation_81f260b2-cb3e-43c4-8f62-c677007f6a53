package cz.kpsys.portaro.messages.db.entity;

import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;

import static cz.kpsys.portaro.databasestructure.MessageDb.MESSAGE_SENDING_EMAIL.*;

@Entity
@Table(name = TABLE)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class MessageSendingEmailEntity extends MessageSendingEntity {

    @Column(name = SUBJECT)
    @Size(min = 1, max = 121)
    @NonNull
    @NotBlank
    String subject;

    @Column(name = BODY)
    @Size(min = 1, max = 1024)
    @NonNull
    @NotBlank
    String body;

    @Column(name = ATTACHMENT_DIRECTORY_ID)
    @Nullable
    Integer attachmentDirectoryId;

    @Column(name = ERROR_MESSAGE)
    @Size(min = 1, max = 1024)
    @NullableNotBlank
    String errorMessage;

    public MessageSendingEmailEntity(@NotNull UUID id,
                                     @NotNull UUID messageId,
                                     @NotNull String messageMediumId,
                                     @NotNull UUID sendingEventId,
                                     @NotNull UUID receiptionEventId,
                                     @NotNull UUID confirmationEventId,
                                     @NonNull String subject,
                                     @NonNull String body,
                                     @Nullable Integer attachmentDirectoryId,
                                     @Nullable String errorMessage) {
        super(id, messageId, messageMediumId, sendingEventId, receiptionEventId, confirmationEventId);
        this.subject = subject;
        this.body = body;
        this.attachmentDirectoryId = attachmentDirectoryId;
        this.errorMessage = errorMessage;
    }
}
