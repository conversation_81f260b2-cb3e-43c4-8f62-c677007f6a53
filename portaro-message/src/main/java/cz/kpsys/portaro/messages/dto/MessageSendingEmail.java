package cz.kpsys.portaro.messages.dto;

import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.constants.MessageMedium;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.UUID;


@FieldDefaults(level = AccessLevel.PRIVATE)
@Getter
@Setter
public final class MessageSendingEmail extends MessageSending {

    @NonNull String recipientEmailAddress;
    @NonNull String senderEmailAddress;
    @NonNull String subject;
    @NonNull String body;
    @Nullable Integer attachmentDirectoryId;
    @Nullable String errorMessage;

    public MessageSendingEmail(@NonNull UUID id, @NonNull UUID messageId, @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent, @NonNull String subject, @NonNull String body, @Nullable Integer attachmentDirectoryId, @Nullable String errorMessage) {
        super(id, messageId, MessageMedium.EMAIL, sendingEvent, receiptionEvent, confirmationEvent);
        this.subject = subject;
        this.body = body;
        this.attachmentDirectoryId = attachmentDirectoryId;
        this.errorMessage = errorMessage;
    }

    public MessageSendingEmail(@NonNull UUID messageId, @Nullable Event sendingEvent, @Nullable Event receiptionEvent, @Nullable Event confirmationEvent, @NonNull String subject, @NonNull String body, @Nullable Integer attachmentDirectoryId, @Nullable String errorMessage) {
        this(messageId, messageId, sendingEvent, receiptionEvent, confirmationEvent, subject, body, attachmentDirectoryId, errorMessage);
    }
}
