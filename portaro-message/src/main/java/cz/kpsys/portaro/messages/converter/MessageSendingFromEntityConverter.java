package cz.kpsys.portaro.messages.converter;

import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.BatchFiller;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.event.Event;
import cz.kpsys.portaro.messages.db.entity.*;
import cz.kpsys.portaro.messages.dto.*;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.*;
import java.util.function.Function;

import static cz.kpsys.portaro.commons.util.ObjectUtil.elvis;

@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class MessageSendingFromEntityConverter implements Converter<List<? extends MessageSendingEntity>, List<MessageSending>> {

    @NonNull AllByIdsLoadable<Event, UUID> eventLoader;

    @Override
    public List<MessageSending> convert(@NonNull List<? extends MessageSendingEntity> entities) {
        List<UUID> eventIds = entities.stream()
                .flatMap(entity -> {
                    List<UUID> ids = new LinkedList<>();

                    addIfNotNull(ids, entity.getReceiptionEventId());
                    addIfNotNull(ids, entity.getConfirmationEventId());
                    addIfNotNull(ids, entity.getSendingEventId());

                    if (entity instanceof MessageSendingPostEntity) {
                        addIfNotNull(ids, ((MessageSendingPostEntity) entity).getPrintingEventId());
                    }

                    return ids.stream();
                })
                .filter(Objects::nonNull)
                .toList();

        Map<UUID, Event> eventMap = BatchFiller.of(eventLoader).load(eventIds, Function.identity());

        return ListUtil.convert(entities, entity -> switch (entity) {
            case MessageSendingEmailEntity emailEntity ->
                    createMessageSendingEmail(eventMap, emailEntity);
            case MessageSendingInternalEntity internalEntity ->
                    createMessageSendingInternal(eventMap, internalEntity);
            case MessageSendingPostEntity postEntity ->
                    createMessageSendingPost(eventMap, postEntity);
            case MessageSendingSmsEntity smsEntity ->
                    createMessageSendingSms(eventMap, smsEntity);
            default -> throw new IllegalStateException("Unknown MessageSending entity");
        });
    }

    private MessageSendingEmail createMessageSendingEmail(Map<UUID, Event> eventMap, MessageSendingEmailEntity entity) {
        return new MessageSendingEmail(
                entity.getId(),
                entity.getMessageId(),
                elvis(entity.getSendingEventId(), eventMap::get),
                elvis(entity.getReceiptionEventId(), eventMap::get),
                elvis(entity.getConfirmationEventId(), eventMap::get),
                entity.getSubject(),
                entity.getBody(),
                entity.getAttachmentDirectoryId(),
                entity.getErrorMessage()
        );
    }

    private MessageSendingInternal createMessageSendingInternal(Map<UUID, Event> eventMap, MessageSendingInternalEntity entity) {
        return new MessageSendingInternal(
                entity.getId(),
                entity.getMessageId(),
                eventMap.get(Objects.requireNonNull(entity.getSendingEventId())),
                elvis(entity.getReceiptionEventId(), eventMap::get),
                elvis(entity.getConfirmationEventId(), eventMap::get),
                entity.getBody()
        );
    }

    private MessageSendingPost createMessageSendingPost(Map<UUID, Event> eventMap, MessageSendingPostEntity entity) {
        return new MessageSendingPost(
                entity.getId(),
                entity.getMessageId(),
                elvis(entity.getSendingEventId(), eventMap::get),
                elvis(entity.getReceiptionEventId(), eventMap::get),
                elvis(entity.getConfirmationEventId(), eventMap::get),
                entity.getAddress(),
                entity.getFkFulltextContent(),
                elvis(entity.getPrintingEventId(), eventMap::get)
        );
    }

    private MessageSendingSms createMessageSendingSms(Map<UUID, Event> eventMap, MessageSendingSmsEntity entity) {
        return new MessageSendingSms(
                entity.getId(),
                entity.getMessageId(),
                elvis(entity.getSendingEventId(), eventMap::get),
                elvis(entity.getReceiptionEventId(), eventMap::get),
                elvis(entity.getConfirmationEventId(), eventMap::get),
                entity.getRecipientPhoneNumber(),
                entity.getContent(),
                entity.getErrorMessage()
        );
    }

    private <T> void addIfNotNull(List<T> list, T element) {
        if (element != null) {
            list.add(element);
        }
    }
}
