package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.commons.object.Throolean;
import cz.kpsys.portaro.sip2.client.Sip2Command;
import cz.kpsys.portaro.sip2.client.model.Sip2RenewRequest;
import cz.kpsys.portaro.sip2.client.model.Sip2RenewResponse;
import cz.kpsys.portaro.sip2.client.serialize.Sip2RequestSerializerTestParent;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.CommandCharacters.NEWLINE;
import static cz.kpsys.portaro.sip2.client.TestUtils.SIP2_DATE_TIME_INSTANT;

@Tag("ci")
@Tag("unit")
public class Sip2RenewRequestSerializerTest extends Sip2RequestSerializerTestParent<Sip2RenewRequest, Sip2RenewResponse> {

    protected TypedSip2MessageSerializer<Sip2Command<Sip2RenewRequest, Sip2RenewResponse>> getSerializer() {
        return new NewlineAddingSip2MessageSerializer<>(ErrorDetectionAddingSip2RequestSerializer.withSequenceAndChecksum(Sip2RenewRequestSerializer.createDefault()));
    }

    @Test
    public void testRenewRequestWithoutErrorDetection() {
        test(
                () -> new Sip2RenewRequest(SIP2_DATE_TIME_INSTANT, SIP2_DATE_TIME_INSTANT, "institutionId", "patronId", "itemId", "titleId"),
                false,
                "29NN20210814    08345520210814    083455AOinstitutionId|AApatronId|ABitemId|AJtitleId|" + NEWLINE
        );
    }

    @Test
    public void testRenewRequestWithErrorDetection1() {
        test(
                () -> {
                    Sip2RenewRequest req = new Sip2RenewRequest(SIP2_DATE_TIME_INSTANT, SIP2_DATE_TIME_INSTANT, "patronId");
                    req.setThirdPartyAllowed(true);
                    req.setNoBlock(true);
                    return req;
                },
                true,
                "29YY20210814    08345520210814    083455AO|AApatronId|AY0AZF19D" + NEWLINE
        );
    }

    @Test
    public void testRenewRequestWithErrorDetection2() {
        test(
                () -> {
                    Sip2RenewRequest req = new Sip2RenewRequest(Instant.parse("2017-10-20T20:51:22Z"), Instant.parse("2017-11-20T21:51:21Z"), "patronId", "itemId");
                    req.setSequence(1);
                    req.setItemProperties("itemProperties");
                    return req;
                },
                true,
                "29NN20171020    22512220171120    225121AO|AApatronId|ABitemId|CHitemProperties|AY1AZE794" + NEWLINE
        );
    }

    @Test
    public void testRenewRequestWithErrorDetection3() {
        test(
                () -> {
                    Sip2RenewRequest req = new Sip2RenewRequest(SIP2_DATE_TIME_INSTANT, SIP2_DATE_TIME_INSTANT, "patronId", "itemId", "titleId");
                    req.setSequence(3);
                    req.setPatronPassword("patronPassword");
                    req.setTerminalPassword("terminalPassword");
                    req.setFeeAcknowledged(Throolean.FALSE);
                    return req;
                },
                true,
                "29NN20210814    08345520210814    083455AO|AApatronId|ADpatronPassword|ABitemId|AJtitleId|ACterminalPassword|BON|AY3AZDA8D" + NEWLINE
        );
    }

    @Test
    public void testRenewRequestWithErrorDetection4() {
        test(
                () -> {
                    Sip2RenewRequest req = new Sip2RenewRequest(SIP2_DATE_TIME_INSTANT, SIP2_DATE_TIME_INSTANT, "institutionId", "patronId", "itemId", "titleId");
                    req.setNoBlock(true);
                    req.setFeeAcknowledged(Throolean.TRUE);
                    req.setItemProperties("itemProperties");
                    return req;
                },
                true,
                "29NY20210814    08345520210814    083455AOinstitutionId|AApatronId|ABitemId|AJtitleId|CHitemProperties|BOY|AY0AZDCB7" + NEWLINE
        );
    }
}
