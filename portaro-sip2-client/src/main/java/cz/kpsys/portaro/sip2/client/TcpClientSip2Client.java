package cz.kpsys.portaro.sip2.client;

import cz.kpsys.portaro.commons.ip.IpAddress;
import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseException;
import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.tcp.SocketTcpClient;
import cz.kpsys.portaro.tcp.TcpClient;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class TcpClientSip2Client implements Sip2Client {

    @NonNull TcpClient tcpClient;
    @NonNull Sip2CommandSerializer sip2RequestSerializer;
    @NonNull Sip2CommandDeserializer sip2ResponseDeserializer;

    public static TcpClientSip2Client createStatelessOnLocalhost(@NonNull Integer port) {
        return createStateless(IpAddress.LOOPBACK_DNS, port);
    }

    public static TcpClientSip2Client createStateless(@NonNull String ip, @NonNull Integer port) {
        return new TcpClientSip2Client(
                SocketTcpClient.stateless(ip, port),
                Sip2CommandSerializer.createDefault(),
                Sip2CommandDeserializer.createDefault()
        );
    }

    public static TcpClientSip2Client createStateless(@NonNull String address) {
        return new TcpClientSip2Client(
                SocketTcpClient.stateless(address),
                Sip2CommandSerializer.createDefault(),
                Sip2CommandDeserializer.createDefault()
        );
    }

    @Override
    @NonNull
    public <RES extends Sip2MessageResponse> RES send(Sip2Command<? extends Sip2MessageRequest<RES>, RES> command) throws InvalidSip2ResponseException, InvalidSip2ResponseValueException {
        String resData = sendAndRespondWithData(command);
        return sip2ResponseDeserializer.deserialize(resData);
    }

    @NonNull
    public <RES extends Sip2MessageResponse> String sendAndRespondWithData(Sip2Command<? extends Sip2MessageRequest<RES>, RES> command) {
        String reqData = sip2RequestSerializer.serialize(command);

        log.debug("Sending SIP2 request   '{}'", reqData.replace("\r", "\\r"));
        String resData = tcpClient.sendMessage(reqData);
        log.debug("Received SIP2 response '{}'", resData.replace("\r", "\\r"));

        return resData;
    }
}
