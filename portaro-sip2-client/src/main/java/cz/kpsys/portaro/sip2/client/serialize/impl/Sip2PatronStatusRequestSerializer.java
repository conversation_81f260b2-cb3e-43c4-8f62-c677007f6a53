package cz.kpsys.portaro.sip2.client.serialize.impl;

import cz.kpsys.portaro.sip2.client.model.Language;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronStatusRequest;
import cz.kpsys.portaro.sip2.client.serialize.CommandSerializing;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2MessageSerializer;
import cz.kpsys.portaro.sip2.client.serialize.TypedSip2ValueSerializer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class Sip2PatronStatusRequestSerializer implements TypedSip2MessageSerializer<Sip2PatronStatusRequest> {

    @NonNull TypedSip2ValueSerializer<Language> languageSerializer;
    @NonNull TypedSip2ValueSerializer<Instant> instantSerializer;

    public static Sip2PatronStatusRequestSerializer createDefault() {
        return new Sip2PatronStatusRequestSerializer(new Sip2LanguageSerializer(), new Sip2InstantSerializer());
    }

    @Override
    public void serialize(@NonNull Sip2PatronStatusRequest request, @NonNull CommandSerializing ser) {
        ser.commandIdentifier(request.getType().getIdentifier());

        languageSerializer.serialize(request.getLanguage(), ser.header());
        instantSerializer.serialize(request.getTransactionDate(), ser.header());

        ser.field(AO_INSTITUTION_ID).string(request.getInstitutionId());
        ser.field(AA_PATRON_IDENTIFIER).string(request.getPatronIdentifier());
        ser.field(AC_TERMINAL_PASSWORD).string(request.getTerminalPassword());
        ser.field(AD_PATRON_PASSWORD).string(request.getPatronPassword());
    }

}
