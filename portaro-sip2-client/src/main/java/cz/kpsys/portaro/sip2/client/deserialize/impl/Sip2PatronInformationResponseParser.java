package cz.kpsys.portaro.sip2.client.deserialize.impl;

import cz.kpsys.portaro.sip2.client.deserialize.InvalidSip2ResponseValueException;
import cz.kpsys.portaro.sip2.client.deserialize.RequiredSip2ValueParsing;
import cz.kpsys.portaro.sip2.client.deserialize.Sip2ResponseParser;
import cz.kpsys.portaro.sip2.client.model.Sip2PatronInformationResponse;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import static cz.kpsys.portaro.sip2.Sip2Constants.FieldCodes.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class Sip2PatronInformationResponseParser implements Sip2ResponseParser<Sip2PatronInformationResponse> {

    @NonNull PatronStatusParser patronStatusParser;
    @NonNull LanguageParser languageParser;
    @NonNull CurrencyTypeParser currencyTypeParser;

    public static Sip2PatronInformationResponseParser createDefault() {
        return new Sip2PatronInformationResponseParser(
                new PatronStatusParser(),
                new LanguageParser(),
                new CurrencyTypeParser()
        );
    }

    @Override
    public Sip2PatronInformationResponse parse(RequiredSip2ValueParsing parsing) throws InvalidSip2ResponseValueException {
        Sip2PatronInformationResponse response = new Sip2PatronInformationResponse();

        response.setStatus(patronStatusParser.parse(parsing));
        response.setLanguage(languageParser.getLanguage(parsing.from(16).first3Chars().numericStringValue()));
        response.setTransactionDate(parsing.from(19).dateTimeInstant());
        response.setHoldItemsCount(parsing.from(37).first4Chars().notBlank().optional().asIntOr(0));
        response.setOverdueItemsCount(parsing.from(41).first4Chars().notBlank().optional().asIntOr(0));
        response.setChargedItemsCount(parsing.from(45).first4Chars().notBlank().optional().asIntOr(0));
        response.setFineItemsCount(parsing.from(49).first4Chars().notBlank().optional().asIntOr(0));
        response.setRecallItemsCount(parsing.from(53).first4Chars().notBlank().optional().asIntOr(0));
        response.setUnavailableHoldsCount(parsing.from(57).first4Chars().notBlank().optional().asIntOr(0));

        RequiredSip2ValueParsing fieldsParsing = parsing.from(61);

        response.setInstitutionId(fieldsParsing.field(AO_INSTITUTION_ID).stringValue());
        response.setPatronIdentifier(fieldsParsing.field(AA_PATRON_IDENTIFIER).stringValue());
        response.setPersonalName(fieldsParsing.field(AE_PERSONAL_NAME).stringValue());
        response.setHoldItemsLimit(fieldsParsing.field(BZ_HOLD_ITEMS_LIMIT).optional().all4Chars().asInt());
        response.setOverdueItemsLimit(fieldsParsing.field(CA_OVERDUE_ITEMS_LIMIT).optional().all4Chars().asInt());
        response.setChargedItemsLimit(fieldsParsing.field(CB_CHARGED_ITEMS_LIMIT).optional().all4Chars().asInt());
        response.setValidPatron(fieldsParsing.field(BL_VALID_PATRON).optional().asNYMissingThroolean());
        response.setValidPatronPassword(fieldsParsing.field(CQ_VALID_PATRON_PASSWORD).optional().asNYMissingThroolean());
        response.setCurrencyType(fieldsParsing.field(BH_CURRENCY_TYPE).optional().map(currencyTypeParser::getCurrencyType));
        response.setFeeAmount(fieldsParsing.field(BV_FEE_AMOUNT).optional().notBlank().stringValue());
        response.setFeeLimit(fieldsParsing.field(CC_FEE_LIMIT).optional().notBlank().stringValue());

        response.setHoldItems(fieldsParsing.fieldValues(AS_HOLD_ITEM_TYPE));
        response.setOverdueItems(fieldsParsing.fieldValues(AT_OVERDUE_ITEM_TYPE));
        response.setChargedItems(fieldsParsing.fieldValues(AU_CHARGED_ITEM_TYPE));
        response.setFineItems(fieldsParsing.fieldValues(AV_FINE_ITEM_TYPE));
        response.setRecallItems(fieldsParsing.fieldValues(BU_RECALL_ITEM_TYPE));
        response.setUnavailableHoldItems(fieldsParsing.fieldValues(CD_UNAVAILABLE_HOLD_ITEM_TYPE));

        response.setHomeAddress(fieldsParsing.field(BD_HOME_ADDRESS).optional().notBlank().trimmed().stringValue());
        response.setEmail(fieldsParsing.field(BE_EMAIL).optional().notBlank().trimmed().stringValue());
        response.setPhone(fieldsParsing.field(BF_PHONE).optional().notBlank().trimmed().stringValue());

        /* SIP2 Extensions - Begin */
        response.setBirthDate(fieldsParsing.field(PB_BIRTH_DATE).optional().notBlank().stringValue());
        response.setPacAccessType(fieldsParsing.field(PA_PAC_ACCESS_TYPE).optional().notBlank().stringValue());
        response.setPatronType(fieldsParsing.field(ZY_PATRON_TYPE).optional().notBlank().stringValue());
        /* SIP2 Extensions - End */

        /* Voyager ESIP extensions - Begin */
        response.setPatronGroup(fieldsParsing.field(PT_PATRON_GROUP).optional().notBlank().stringValue());
        /* Voyager ESIP extensions - End */

        response.setScreenMessage(fieldsParsing.fieldValues(AF_SCREEN_MESSAGE));
        response.setPrintLine(fieldsParsing.fieldValues(AG_PRINT_LINE));

        fieldsParsing.sequence().ifPresent(response::setSequence);

        return response;
    }
}
