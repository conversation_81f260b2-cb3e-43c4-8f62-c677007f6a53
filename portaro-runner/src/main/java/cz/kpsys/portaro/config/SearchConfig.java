package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualFunction;
import cz.kpsys.portaro.auth.context.AuthenticatedContextualProvider;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheBackedRepository;
import cz.kpsys.portaro.commons.cache.CacheCleaningSaver;
import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.contextual.CompositeAllValuesContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.localization.Translator;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.core.editor.DispatchingSearchValueEditorLoader;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.DepartmentAccessor;
import cz.kpsys.portaro.exemplar.BasicExemplar;
import cz.kpsys.portaro.exemplar.ExemplarConstants;
import cz.kpsys.portaro.exemplar.discard.Discardion;
import cz.kpsys.portaro.file.FileConstants;
import cz.kpsys.portaro.file.FileSearchParams;
import cz.kpsys.portaro.inventory.InventoryConstants;
import cz.kpsys.portaro.inventory.match.Match;
import cz.kpsys.portaro.loan.LoanConstants;
import cz.kpsys.portaro.loan.reminder.LoanReminderConstants;
import cz.kpsys.portaro.location.Location;
import cz.kpsys.portaro.messages.constants.MessageConstants;
import cz.kpsys.portaro.payment.PaymentConstants;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordHeaderToLuceneValueConverter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.detail.constraints.LinkConstraintsResolver;
import cz.kpsys.portaro.record.document.SpringDbKat14YearExtremesLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.isbn.IsbnChecker;
import cz.kpsys.portaro.record.isbn.IsbnDisjunctionAddingTermConverter;
import cz.kpsys.portaro.record.search.*;
import cz.kpsys.portaro.record.search.restriction.*;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.field.*;
import cz.kpsys.portaro.search.history.SearchHistoryImpl;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsLoader;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsSaver;
import cz.kpsys.portaro.search.keywords.SpringDbSearchedKeywordsLoader;
import cz.kpsys.portaro.search.keywords.SpringDbSearchedKeywordsSaver;
import cz.kpsys.portaro.search.lucene.FacetKeyIdentified;
import cz.kpsys.portaro.search.lucene.KeywordsParser;
import cz.kpsys.portaro.search.lucene.QToLuceneQueryConverterByPattern;
import cz.kpsys.portaro.search.lucene.StringQueryBuilder;
import cz.kpsys.portaro.search.lucene.facets.*;
import cz.kpsys.portaro.search.params.DefaultParamsModifier;
import cz.kpsys.portaro.search.params.ParamsModifier;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.restriction.convert.RestrictionDeserializerWithSearchFields;
import cz.kpsys.portaro.search.restriction.convert.RestrictionSerializer;
import cz.kpsys.portaro.search.restriction.convert.SingleStringMatcherValueTermMatcher;
import cz.kpsys.portaro.search.restriction.deserialize.json.RestrictionDeserializerPostModifyingDecorator;
import cz.kpsys.portaro.search.restriction.serialize.lucene.*;
import cz.kpsys.portaro.search.view.*;
import cz.kpsys.portaro.setting.FondedValues;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.stats.StatsConstants;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.UserConstants;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.web.rest.FacetTypeCreationRequest;
import cz.kpsys.portaro.view.web.rest.FacetTypeEditationRequest;
import cz.kpsys.portaro.view.web.rest.FacetTypeEditationRequestToFacetTypeConverter;
import cz.kpsys.portaro.web.ResponseHelper;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Range;
import org.jspecify.annotations.Nullable;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.web.context.WebApplicationContext;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import static cz.kpsys.portaro.app.CatalogConstants.Search.Lucene.*;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SearchConfig {

    @NonNull QueryFactory queryFactory;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull NamedParameterJdbcOperations notCriticalJdbcTemplate;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull ExecutorService executorService;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;
    @NonNull CacheService cacheService;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SimpleModule objectMapperModule;
    @NonNull SettingLoader settingLoader;
    @NonNull ByIdLoadable<@Nullable AllValuesProvider<? extends LabeledIdentified<?>>, ScalarDatatype> acceptableValuesProviderLoader;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull DepartmentAccessor departmentAccessor;
    @NonNull ContextualProvider<Department, List<Location>> readableLocationsContextualProvider;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull AllValuesProvider<Fond> enabledAuthorityFondsProvider;
    @NonNull AllValuesProvider<Fond> enabledDocumentFondsProvider;
    @NonNull Translator<Department> translator;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull FormattingConversionService conversionService;
    @NonNull ObjectMapper objectMapper;
    @NonNull CacheManager cacheManager;
    @NonNull Function<String, List<Fond>> kindToEnabledFondsExpander;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull LinkConstraintsResolver linkConstraintsResolver;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull EntityManager entityManager;
    @NonNull Codebook<Fond, Integer> fondLoader;

    @Bean
    public SearchFactoryResolver searchFactoryResolver() {
        return new SearchFactoryResolver();
    }

    @Bean
    public SearchedKeywordsLoader searchedKeywordsLoader() {
        return new SpringDbSearchedKeywordsLoader(
                notCriticalJdbcTemplate,
                queryFactory,
                StaticProvider.of(3)
        );
    }

    @Bean
    public SearchedKeywordsSaver searchedKeywordsSaver() {
        return new SpringDbSearchedKeywordsSaver(notCriticalJdbcTemplate, queryFactory, executorService);
    }

    @Bean
    public KeywordsParser keywordsParser() {
        return new KeywordsParserAppserver(
                mappingAppserver,
                ExemplarConstants.Datatype.SIGNATURE.getName(),
                ExemplarConstants.Datatype.ACCESS_NUMBER.getName()
        );
    }

    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES)
    public Codebook<SearchField, String> searchFieldLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(CompositeAllValuesProvider.of(
                        customSearchFieldLoader(),
                        ConvertingAllValuesProvider.byItemConverter(facetTypeLoader(), FacetType::toSearchField),
                        StaticAllValuesProvider.of(StaticSearchFields.LIST)
                ))
                .build();
    }

    @Bean
    public Codebook<CustomSearchField, String> customSearchFieldLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedByJpa(CustomSearchFieldEntity.class)
                .convertedEachBy(new EntityToCustomSearchFieldConverter())
                .staticCached(CustomSearchField.class.getSimpleName())
                .build();
    }

    @Bean
    public AuthenticatedContextualProvider<Department, List<ViewableSearchField>> viewableSearchFieldsProvider() {
        return new ViewableSearchFieldContextualProvider<>(
                settingLoader.getDepartmentedProvider(SettingKeys.FORM_SEARCH_FIELDS),
                searchFieldLoader(),
                departmentedSearchFieldToViewableSearchFieldConverter()
        );
    }

    @Bean
    public AuthenticatedContextualFunction<SearchField, Department, ViewableSearchField> departmentedSearchFieldToViewableSearchFieldConverter() {
        return new DepartmentedSearchFieldToViewableSearchFieldConverter<>(searchValueEditorLoader(), datatypeBySearchFieldLoader());
    }

    @Bean
    @Scope(proxyMode = ScopedProxyMode.INTERFACES)
    public QueryFieldsBySearchFieldLoader<LuceneQueryField> luceneQueryFieldLoader() {
        Map<String, List<LuceneQueryField>> staticFields = new HashMap<>();
        staticFields.put(StaticSearchFields.WHATEVER.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_WHATEVER, CoreConstants.Datatype.TEXT)));
        staticFields.put(StaticSearchFields.NAME.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_NAME, CoreConstants.Datatype.TEXT)));
        staticFields.put(StaticSearchFields.FOND.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.FOND)));
        staticFields.put(StaticSearchFields.RECORD_RELATED_RECORD.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_RELATED_RECORD, RecordConstants.Datatype.RECORD_HEADER)));
        staticFields.put(StaticSearchFields.DEPARTMENT.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(StaticSearchFields.UNRESTRICTED_DEPARTMENT.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_UNRESTRICTED_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(StaticSearchFields.DRAFT_DEPARTMENT.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DRAFT_DEP, CoreConstants.Datatype.DEPARTMENT)));
        staticFields.put(StaticSearchFields.LOCATION.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_LOCATION, ExemplarConstants.Datatype.LOCATION)));
        staticFields.put(StaticSearchFields.DOCUMENT_FOND.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.DOCUMENT_FOND)));
        staticFields.put(StaticSearchFields.RECORD_STATUS.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_STATUS, RecordConstants.Datatype.RECORD_STATUS)));
        staticFields.put(StaticSearchFields.DOCUMENT_ISBN.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_ISBN, RecordConstants.Datatype.ISBN)));
        staticFields.put(StaticSearchFields.DOCUMENT_ISSN.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_ISSN, RecordConstants.Datatype.ISSN)));
        staticFields.put(StaticSearchFields.DOCUMENT_ISBN_OR_ISSN.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_ISBN, RecordConstants.Datatype.ISBN),
                SimpleLuceneQueryField.create(FIELD_ISSN, RecordConstants.Datatype.ISSN)
        ));
        staticFields.put(StaticSearchFields.DOCUMENT_NAME.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_NAME, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.DOCUMENT_SIGNATURE.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_SIGNATURE, ExemplarConstants.Datatype.SIGNATURE)
        ));
        staticFields.put(StaticSearchFields.DOCUMENT_ACCESS_NUMBER.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_ACCESS_NUMBER, ExemplarConstants.Datatype.ACCESS_NUMBER)
        ));
        staticFields.put(StaticSearchFields.RECORD_SOURCE_RECORD.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_SOURCE_DOCUMENT_NAME, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.AUTHOR.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_AUTHOR, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_AUTHOR_CORPORATION, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_AUTHOR_ACTION, CoreConstants.Datatype.TEXT)));
        staticFields.put(StaticSearchFields.DOCUMENT_YEAR.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_YEAR, CoreConstants.Datatype.NUMBER)
        ));
        staticFields.put(StaticSearchFields.PUBLISHER.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_PUBLISHER_OLD_FORM, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_PUBLISHER, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.NOTE.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_DOCUMENT_NOTE, CoreConstants.Datatype.TEXT) // Warn! field 500 is note only in documents - in authorities field 500 is "see also"
        ));
        staticFields.put(StaticSearchFields.AUTHORITY_NAME.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_PERSON_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_CORPORATION_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_ACTION_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_KEYWORD_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_GEOGRAPHICS_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_UNIFIED_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_CHRONOLOGIC_KEYWORD_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_RECORD_SOURCE_DOCUMENT_NAME, CoreConstants.Datatype.TEXT),
                SimpleLuceneQueryField.create(FIELD_AUTHORITY_CONSPECT, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.CNA.getId(), List.of(SimpleLuceneQueryField.create(FIELD_AUTHORITY_CNA, CoreConstants.Datatype.TEXT)));
        staticFields.put(StaticSearchFields.AUTHORITY_FOND.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_FOND, RecordConstants.Datatype.AUTHORITY_FOND)
        ));
        staticFields.put(StaticSearchFields.RECORD_ID.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_ID, RecordConstants.Datatype.RECORD)
        ));
        staticFields.put(StaticSearchFields.RECORD_CREATION_DATE.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_CREATION_DATE, CoreConstants.Datatype.DATETIME)
        ));
        staticFields.put(StaticSearchFields.RECORD_FILE_CATEGORY.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_RECORD_FILE_CATEGORY, FileConstants.Datatype.FILE_CATEGORY)
        ));
        staticFields.put(StaticSearchFields.RECORD_LOAN_SOURCE.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_LOAN_SOURCE, CoreConstants.Datatype.TEXT)
        ));
        staticFields.put(StaticSearchFields.TABLE_OF_CONTENT.getId(), List.of(
                SimpleLuceneQueryField.create(FIELD_TABLE_OF_CONTENT, CoreConstants.Datatype.TEXT)
        ));
        return new LuceneQueryFieldLoaderDispatching(staticFields, customSearchFieldLoader());
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public AllProvidingRepository<Search<MapBackedParams, ?, Paging>, UUID> searchHistory() {
        return new SearchHistoryImpl();
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public SameTypeSettableProvider<@NonNull Integer> documentDefaultPageSizeProvider() {
        return new FallbackingSettableProvider<>(settingLoader.getOnRootProvider(SettingKeys.DOCUMENT_SEARCH_PAGE_SIZE));
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_SESSION, proxyMode = ScopedProxyMode.INTERFACES)
    public SameTypeSettableProvider<Integer> authorityPageSizeProvider() {
        return new FallbackingSettableProvider<>(settingLoader.getOnRootProvider(SettingKeys.AUTHORITY_SEARCH_PAGE_SIZE));
    }

    @Bean
    public Codebook<FacetType, Integer> facetTypeLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbFacetTypeLoader(jdbcTemplate, queryFactory, facetTypeSortingLoader()))
                .staticCached(FacetType.class.getSimpleName())
                .build();
    }

    @Bean
    public Saver<FacetType, FacetType> facetTypeSaver() {
        return new CacheCleaningSaver<>(new PostConvertingSaver<>(
                new PreConvertingSaver<>(
                        new FacetTypeToEntityConverter(),
                        new FlushingJpaSaver<>(new SimpleJpaRepository<>(FacetTypeEntity.class, entityManager))
                ),
                new FacetTypeFromEntityConverter(facetTypeSortingLoader(), facetDefinitionTypeLoader(), facetScopeLoader())
        ), cacheService.createCleanerFor(FacetType.class.getSimpleName())
        );
    }

    @Bean
    public FacetTypeCreator facetTypeCreator() {
        return new FacetTypeCreator(
                facetTypeSaver(),
                defaultTransactionTemplateFactory.get(),
                facetTypeLoader()
        );
    }

    @Bean
    public FacetTypeUpdater facetTypeUpdater() {
        return new FacetTypeUpdater(
                facetTypeSaver(),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<FacetTypeCreationRequest> facetTypeRequestDefaulter() {
        return new FacetTypeCreationRequest.FacetTypeRequestDefaulter(facetTypeLoader());
    }

    @Bean
    public Converter<FacetTypeEditationRequest, FacetType> facetTypeRequestToFacetTypeConverter() {
        return new FacetTypeEditationRequestToFacetTypeConverter();
    }

    @Bean
    public CompositeSearchFormFactory searchFormFactory() {
        return new CompositeSearchFormFactory();
    }

    @Bean
    public ViewableItemsConverter viewableItemsConverter() {
        return new ViewableItemsConverter();
    }

    @Bean
    public SearchTextResolver searchTitleResolver() {
        return new SearchTitleResolver(translator);
    }

    @Bean
    public SearchTextResolver searchSubtitleResolver() {
        return new EmptySearchTextResolver();
    }

    @Bean
    public Repository<Search<? extends MapBackedParams, ?, Paging>, UUID> searchRepository() {
        return CacheBackedRepository.ofIdentified(GuavaTimedDynamicCache.ofIdentified(Duration.ofMinutes(10), false));
    }

    @Bean
    public SearchValueEditorLoader<Department> searchValueEditorLoader() {
        return new DispatchingSearchValueEditorLoader(
                acceptableValuesProviderLoader,
                readableLocationsContextualProvider,
                currentAuthShowableFondsLoader,
                departmentAccessor
        );
    }

    @Bean
    public DatatypeBySearchFieldLoader datatypeBySearchFieldLoader() {
        Map<SearchField, ScalarDatatype> staticFieldsToDatatypes = new HashMap<>();
        staticFieldsToDatatypes.put(StaticSearchFields.FOND, RecordConstants.Datatype.FOND);
        staticFieldsToDatatypes.put(StaticSearchFields.RECORD_RELATED_RECORD, RecordConstants.Datatype.RECORD_HEADER);
        staticFieldsToDatatypes.put(StaticSearchFields.DEPARTMENT, CoreConstants.Datatype.DEPARTMENT);
        staticFieldsToDatatypes.put(StaticSearchFields.UNRESTRICTED_DEPARTMENT, CoreConstants.Datatype.DEPARTMENT);
        staticFieldsToDatatypes.put(StaticSearchFields.DRAFT_DEPARTMENT, CoreConstants.Datatype.DEPARTMENT);
        staticFieldsToDatatypes.put(StaticSearchFields.LOCATION, ExemplarConstants.Datatype.LOCATION);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT, RecordConstants.Datatype.DOCUMENT);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_FOND, RecordConstants.Datatype.DOCUMENT_FOND);
        staticFieldsToDatatypes.put(StaticSearchFields.RECORD_STATUS, RecordConstants.Datatype.RECORD_STATUS);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_ISBN, RecordConstants.Datatype.ISBN);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_ISSN, RecordConstants.Datatype.ISSN);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_ISBN_OR_ISSN, RecordConstants.Datatype.ISBN_OR_ISSN);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_NAME, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_SIGNATURE, ExemplarConstants.Datatype.SIGNATURE);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_ACCESS_NUMBER, ExemplarConstants.Datatype.ACCESS_NUMBER);
        staticFieldsToDatatypes.put(StaticSearchFields.AUTHOR, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.DOCUMENT_YEAR, CoreConstants.Datatype.YEAR);
        staticFieldsToDatatypes.put(StaticSearchFields.PUBLISHER, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.NOTE, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.AUTHORITY_NAME, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.CNA, CoreConstants.Datatype.TEXT);
        staticFieldsToDatatypes.put(StaticSearchFields.AUTHORITY_FOND, RecordConstants.Datatype.AUTHORITY_FOND);
        staticFieldsToDatatypes.put(StaticSearchFields.RECORD_CREATION_DATE, CoreConstants.Datatype.DATETIME);
        staticFieldsToDatatypes.put(StaticSearchFields.RECORD_FILE_CATEGORY, FileConstants.Datatype.FILE_CATEGORY);
        staticFieldsToDatatypes.put(StaticSearchFields.RECORD_LOAN_SOURCE, LoanConstants.Datatype.EXTERNAL_LOAN_SERVICE);
        staticFieldsToDatatypes.put(StaticSearchFields.TABLE_OF_CONTENT, CoreConstants.Datatype.TEXT);
        return new DatatypeBySearchFieldLoaderByLuceneQueryFieldLoader(staticFieldsToDatatypes, customSearchFieldLoader(), facetTypeLoader());
    }

    @Bean
    public AppserverSearchEngine appserverSearchEngine() {
        return new AppserverSearchEngine(
                mappingAppserver,
                settingLoader.getDepartmentedProvider(SettingKeys.FACETS),
                settingLoader.getDepartmentedProvider(SettingKeys.FACET_KEYS_MAX_COUNT),
                departmentAccessor
        );
    }

    @Bean
    public CacheResolver rawableSearchServiceCacheResolver() {
        return new RawableSearchServiceCacheResolver(cacheManager);
    }

    @Bean
    public ParamsModifier<DefaultGettableAndSettableSearchParams> fondRelatedParamsExpander() {
        return new DefaultParamsModifier<>()
                .withExpandingParam(CoreSearchParams.KIND, RecordConstants.SearchParams.FOND, kindToEnabledFondsExpander)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);
    }

    @Bean
    public SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService() {
        AppserverLuceneSearchService pure = new AppserverLuceneSearchService(
                appserverSearchEngine(),
                new SearchResponseToSearchResultConverter(
                        datatypableStringConverter,
                        recordFieldStringToInstantConverter
                ),
                luceneSearchQueryBuilder()
        );

        var paramsConvertingService = new ParametersConvertingSearchService<>(pure)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);

        CachingSearchService<RangePaging> cached = new CachingSearchService<>(paramsConvertingService);
        cacheService.registerSpringCacheCleaner(PagedSearchResult.class.getSimpleName(), CachingSearchService.LONG_TERM_CACHE_NAME);
        cacheService.registerCleaner(PagedSearchResult.class.getSimpleName(), cached);
        return cached;
    }

    @Bean
    public ContextualProvider<Department, Range<Integer>> documentYearExtremeRangeProvider() {
        return settingLoader.getDepartmentedProvider(SettingKeys.DOCUMENT_MIN_YEAR)
                .andThenFastReturningNull(minYear -> Range.of(minYear, LocalDate.now().getYear()))
                .fallbacked(new SpringDbKat14YearExtremesLoader(jdbcTemplate, queryFactory).cached());
    }

    @Bean
    public Codebook<SortingItem, String> facetTypeSortingLoader() {
        return new StaticCodebook<>(
                FacetTypeSorting.SORTING_FREQ_DESC,
                FacetTypeSorting.SORTING_ALPH_ASC,
                FacetTypeSorting.SORTING_ALPH_DESC,
                FacetTypeSorting.SORTING_CHRONO_ASC,
                FacetTypeSorting.SORTING_CHRONO_DESC,
                FacetTypeSorting.SORTING_NUM_ASC,
                FacetTypeSorting.SORTING_NUM_DESC
        );
    }

    @Bean
    public Codebook<FacetScope, Integer> facetScopeLoader() {
        return FacetScope.CODEBOOK;
    }

    @Bean
    public Codebook<FacetDefinitionType, Integer> facetDefinitionTypeLoader() {
        return FacetDefinitionType.CODEBOOK;
    }

    @Bean
    public IdAndIdsLoadable<SortingItem, String> searchSortingLoader() {
        return new CompositeByIdLoader<>(
                recordSearchSortingLoader(),
                authoritySearchSortingLoader(),
                exemplarSearchSortingLoader(),
                facetTypeSortingLoader(),
                matchSearchSortingLoader(),
                new AllByIdsLoadableByIdLoaderAdapter<>(SortingItem::ofDefaultMessage) //fallback
        );
    }

    @Bean
    public Provider<SortingItem> defaultRecordSearchSortingProvider() {
        return DefaultProvider.byFirst(settingLoader.getOnRootProvider(SettingKeys.SEARCH_SORTINGS));
    }

    @Bean
    public Codebook<SortingItem, String> recordSearchSortingLoader() {
        return AllValuesProvidedCodebook.ofIdentified(() -> settingLoader.getOnRootProvider(SettingKeys.SEARCH_SORTINGS).get());
    }

    @Bean
    public Provider<SortingItem> defaultAuthoritySearchSortingProvider() {
        return DefaultProvider.byFirst(authoritySearchSortingLoader());
    }

    @Bean
    public Codebook<SortingItem, String> authoritySearchSortingLoader() {
        return AllValuesProvidedCodebook.ofIdentified(() -> settingLoader.getOnRootProvider(SettingKeys.AUTHORITY_SEARCH_SORTINGS).get());
    }

    @Bean
    public ContextualProvider<Department, @NonNull FondedValues<@NonNull List<SortingItem>>> departmentedFondedRecordSearchSortingsLoader() {
        return settingLoader.getDepartmentedFondedValuesProvider(SettingKeys.SEARCH_SORTINGS);
    }

    @Bean
    public ByIdLoadable<Datasource, String> datasourceLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(allDatasetsProvider())
                .build();
    }

    @Bean
    public CompositeAddableAllValuesProvider<Datasource> allDatasetsProvider() {
        return CompositeAddableAllValuesProvider.ofEmpty();
    }

    @Bean
    public CompositeAllValuesContextualProvider<Department, Datasource> allowedDatasetsProvider() {
        return new CompositeAllValuesContextualProvider<>();
    }

    @Bean
    public Provider<SortingItem> defaultExemplarSearchSortingProvider() {
        return DefaultProvider.byId(exemplarSearchSortingLoader(), BasicExemplar.Fields.accessNumber);
    }

    @Bean
    public Codebook<SortingItem, String> exemplarSearchSortingLoader() {
        return new StaticCodebook<>(
                SortingItem.ofDefaultMessage(BasicExemplar.Fields.accessNumber, true),
                SortingItem.ofDefaultMessage(BasicExemplar.Fields.accessNumber, false),
                SortingItem.ofDefaultMessage(BasicExemplar.Fields.signature, false),
                SortingItem.ofDefaultMessage(Discardion.Fields.discardNumber, true)
        );
    }

    @Bean
    public Provider<SortingItem> defaultMatchSearchSortingProvider() {
        return DefaultProvider.byId(matchSearchSortingLoader(), Match.Fields.accessNumber);
    }

    @Bean
    public Codebook<SortingItem, String> matchSearchSortingLoader() {
        return new StaticCodebook<>(
                SortingItem.ofDefaultMessage(Match.Fields.accessNumber, true),
                SortingItem.ofDefaultMessage(Match.Fields.accessNumber, false),
                SortingItem.ofDefaultMessage(Match.Fields.signature, true),
                SortingItem.ofDefaultMessage(Match.Fields.signature, false),
                SortingItem.ofDefaultMessage(Match.Fields.barCode, true),
                SortingItem.ofDefaultMessage(Match.Fields.barCode, false)
        );
    }

    @Bean
    public MapToMapSearchParamsConverter mapToMapSearchParamsConverter() {
        Set<String> notMappableButAllowedQueryParams = Set.of(
                SearchViewConstants.PAGE_NUMBER_PARAM,
                SearchViewConstants.PAGE_SIZE_PARAM,
                SearchViewConstants.EXPORTS_PARAM,
                SearchViewConstants.FOCUSED_FIELD_TYPE_ID_PARAM,
                ResponseHelper.FORMAT_PARAMETER_NAME,
                CatalogWebConstants.LOCALE_URL_PARAMETER_NAME,
                SearchViewConstants.CACHE,
                SearchViewConstants.AFTER_POSITION,
                SearchViewConstants.BEFORE_POSITION,
                SearchViewConstants.SEARCH_DATE_PARAM,
                SearchViewConstants.SORTING,
                CatalogWebConstants.ACTIVE_TAB_NAME
        );

        return new MapToMapSearchParamsConverter(conversionService, mappableSearchParams(), notMappableButAllowedQueryParams);
    }

    @Bean
    public Set<JavatypedDatatypedProperty<?>> mappableSearchParams() {
        Set<JavatypedDatatypedProperty<?>> mappableSearchParams = new HashSet<>();

        mappableSearchParams.add(CoreSearchParams.KIND);
        mappableSearchParams.add(CoreSearchParams.SUBKIND);

        mappableSearchParams.add(CoreSearchParams.DEPARTMENT);

        mappableSearchParams.add(CoreSearchParams.NAME);

        mappableSearchParams.add(CoreSearchParams.Q);

        mappableSearchParams.add(CoreSearchParams.INITIATOR);

        mappableSearchParams.add(RecordConstants.SearchParams.DATASOURCE);
        mappableSearchParams.add(RecordConstants.SearchParams.DATASOURCE_GROUP);
        mappableSearchParams.add(CoreSearchParams.CENTRAL_INDEX_ENABLED);

        mappableSearchParams.add(InventoryConstants.SearchParams.INVENTORY);

        mappableSearchParams.add(CoreSearchParams.TYPE);
        mappableSearchParams.add(CoreSearchParams.TITLE);

        mappableSearchParams.add(CoreSearchParams.FROM_DATE);
        mappableSearchParams.add(CoreSearchParams.INCLUDE_DRAFT);
        mappableSearchParams.add(CoreSearchParams.ONLY_DRAFTS);
        mappableSearchParams.add(CoreSearchParams.INCLUDE_DELETED);
        mappableSearchParams.add(CoreSearchParams.INCLUDE_ACTIVE);
        mappableSearchParams.add(CoreSearchParams.TO_DATE);
        mappableSearchParams.add(CoreSearchParams.QT);

        mappableSearchParams.add(FileSearchParams.DIRECTORY);
        mappableSearchParams.add(FileSearchParams.ROOT_DIRECTORY);
        mappableSearchParams.add(FileSearchParams.FILE_CATEGORY);
        mappableSearchParams.add(FileSearchParams.FILE_PROCESSING_STATE);
        mappableSearchParams.add(FileSearchParams.FILENAME_EXTENSION);
        mappableSearchParams.add(FileSearchParams.FILENAME);
        mappableSearchParams.add(FileSearchParams.FORBIDDEN_FILE);
        mappableSearchParams.add(FileSearchParams.FORBIDDEN_FILE_CATEGORY);
        mappableSearchParams.add(FileSearchParams.MINIMAL_FILE_SIZE_KB);

        mappableSearchParams.add(RecordConstants.SearchParams.OPERATED_SUBKIND);
        mappableSearchParams.add(RecordConstants.SearchParams.OPERATED_ROOT_FOND);
        if (false) {
            // only here to explicitly say that we do not use operatedFond in frontend
            mappableSearchParams.add(RecordConstants.SearchParams.OPERATED_FOND);
        }
        mappableSearchParams.add(RecordConstants.SearchParams.OPERATION_TYPE);

        mappableSearchParams.add(PaymentConstants.SearchParams.INCLUSIVE_FROM_CREATION_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.EXCLUSIVE_TO_CREATION_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.CREATION_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.INCLUSIVE_FROM_PAY_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.EXCLUSIVE_TO_PAY_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.PAY_DATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.PAYMENT_STATE);
        mappableSearchParams.add(PaymentConstants.SearchParams.PROVIDER);
        mappableSearchParams.add(PaymentConstants.SearchParams.TRANSACTION_ID);

        mappableSearchParams.add(PaymentConstants.SearchParams.OWNER);
        mappableSearchParams.add(PaymentConstants.SearchParams.AMOUNT_TYPE);

        mappableSearchParams.add(UserConstants.SearchParams.CREATOR);

        mappableSearchParams.add(CoreSearchParams.RAW_QUERY);
        mappableSearchParams.add(CoreSearchParams.FINAL_RAW_QUERY);

        mappableSearchParams.add(CoreSearchParams.FACET_RESTRICTION);
        mappableSearchParams.add(CoreSearchParams.FACETS_ENABLED);
        mappableSearchParams.add(CoreSearchParams.RIGHT_HAND_EXTENSION);
        mappableSearchParams.add(CoreSearchParams.FIELD);

        mappableSearchParams.add(RecordConstants.SearchParams.PREFIX);
        mappableSearchParams.add(RecordConstants.SearchParams.DIACRITICAL_PREFIX);
        mappableSearchParams.add(RecordConstants.SearchParams.FORBIDDEN_RECORD);
        mappableSearchParams.add(RecordConstants.SearchParams.RECORD);
        mappableSearchParams.add(ExemplarConstants.SearchParams.EXEMPLAR_STATUS);
        mappableSearchParams.add(ExemplarConstants.SearchParams.FORBIDDEN_EXEMPLAR);
        mappableSearchParams.add(ExemplarConstants.SearchParams.ACQUISITION_WAY);
        mappableSearchParams.add(RecordConstants.SearchParams.ROOT_FOND);
        mappableSearchParams.add(RecordConstants.SearchParams.FOND);
        mappableSearchParams.add(RecordConstants.SearchParams.FOND_TYPE);
        mappableSearchParams.add(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE);
        mappableSearchParams.add(RecordConstants.SearchParams.RECORD_RELATED_RECORD);
        mappableSearchParams.add(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND);
        mappableSearchParams.add(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS);
        mappableSearchParams.add(RecordConstants.SearchParams.INCLUDE_EXCLUDED);
        mappableSearchParams.add(RecordConstants.SearchParams.FIELD_ID);

        mappableSearchParams.add(ExemplarConstants.SearchParams.INCLUDE_FAKE);
        mappableSearchParams.add(ExemplarConstants.SearchParams.LOCATION);
        mappableSearchParams.add(ExemplarConstants.SearchParams.EXEMPLAR_TYPE);
        mappableSearchParams.add(ExemplarConstants.SearchParams.INCREASE_YEAR);
        mappableSearchParams.add(ExemplarConstants.SearchParams.BAR_CODE);
        mappableSearchParams.add(ExemplarConstants.SearchParams.ACCESS_NUMBER);
        mappableSearchParams.add(ExemplarConstants.SearchParams.ACCESS_NUMBER_START);
        mappableSearchParams.add(ExemplarConstants.SearchParams.ACCESS_NUMBER_END);
        mappableSearchParams.add(ExemplarConstants.SearchParams.SIGNATURE);
        mappableSearchParams.add(ExemplarConstants.SearchParams.SIGNATURE_START);
        mappableSearchParams.add(ExemplarConstants.SearchParams.SIGNATURE_END);
        mappableSearchParams.add(ExemplarConstants.SearchParams.DISCARD_NUMBER_START);
        mappableSearchParams.add(ExemplarConstants.SearchParams.DISCARD_NUMBER_END);
        mappableSearchParams.add(ExemplarConstants.SearchParams.BUNDLED_VOLUME_YEAR);

        mappableSearchParams.add(UserConstants.SearchParams.USERNAME);
        mappableSearchParams.add(UserConstants.SearchParams.USERNAME_IGNORE_CASE);
        mappableSearchParams.add(UserConstants.SearchParams.EMAIL);
        mappableSearchParams.add(UserConstants.SearchParams.LAST_NAME);
        mappableSearchParams.add(UserConstants.SearchParams.CARD_NUMBER);
        mappableSearchParams.add(UserConstants.SearchParams.BAR_CODE);
        mappableSearchParams.add(UserConstants.SearchParams.CASE_INSENSITIVE_NET_ID);
        mappableSearchParams.add(UserConstants.SearchParams.OPENID);
        mappableSearchParams.add(UserConstants.SearchParams.RFID_USER_ID);
        mappableSearchParams.add(UserConstants.SearchParams.READER_CATEGORY);
        mappableSearchParams.add(UserConstants.SearchParams.USER_TYPE);
        mappableSearchParams.add(UserConstants.SearchParams.INCLUDE_ANONYMIZED);
        mappableSearchParams.add(UserConstants.SearchParams.EDIT_LEVEL);
        mappableSearchParams.add(UserConstants.SearchParams.USER_SERVICE_PROP);

        mappableSearchParams.add(LoanConstants.SearchParams.LENDER);
        mappableSearchParams.add(LoanConstants.SearchParams.LOAN_STATE);
        mappableSearchParams.add(LoanConstants.SearchParams.EXEMPLAR);
        mappableSearchParams.add(LoanConstants.SearchParams.LOAN_REQUEST);
        mappableSearchParams.add(LoanConstants.SearchParams.SHIPMENT_ITEM);
        mappableSearchParams.add(LoanConstants.SearchParams.LEND_FROM_DATE);
        mappableSearchParams.add(LoanConstants.SearchParams.LEND_TO_DATE);
        mappableSearchParams.add(LoanConstants.SearchParams.INCLUDE_NOT_SHIPPING);
        mappableSearchParams.add(LoanConstants.SearchParams.EXEMPLAR_Q);

        mappableSearchParams.add(LoanReminderConstants.SearchParams.LOAN_REMINDER_TYPE);
        mappableSearchParams.add(LoanReminderConstants.SearchParams.SENT_INCLUDED);

        mappableSearchParams.add(MessageConstants.SearchParams.CREATE_DATE);
        mappableSearchParams.add(MessageConstants.SearchParams.SENDER_USER);
        mappableSearchParams.add(MessageConstants.SearchParams.TARGET_USER);
        mappableSearchParams.add(MessageConstants.SearchParams.SEVERITY);
        mappableSearchParams.add(MessageConstants.SearchParams.TOPIC);
        mappableSearchParams.add(MessageConstants.SearchParams.MESSAGE_MEDIUM);
        mappableSearchParams.add(MessageConstants.SearchParams.MESSAGE);
        mappableSearchParams.add(MessageConstants.SearchParams.MESSAGES);
        mappableSearchParams.add(MessageConstants.SearchParams.MESSAGE_STATUS);
        mappableSearchParams.add(MessageConstants.SearchParams.THREAD);

        mappableSearchParams.add(RecordConstants.SearchParams.CNA);

        mappableSearchParams.add(RecordConstants.SearchParams.ISBN);
        mappableSearchParams.add(RecordConstants.SearchParams.ISSN);
        mappableSearchParams.add(RecordConstants.SearchParams.ISBN_OR_ISSN);
        mappableSearchParams.add(RecordConstants.SearchParams.AUTHOR);
        mappableSearchParams.add(RecordConstants.SearchParams.PUBLICATION_YEAR);

        mappableSearchParams.add(InventoryConstants.SearchParams.MATCH_STATE);

        mappableSearchParams.add(StatsConstants.SearchParams.TIME_GRANULARITY);

        mappableSearchParams.add(InventoryConstants.SearchParams.INVENTORY_STATE);

        return mappableSearchParams;
    }


    @Bean
    public StringQueryBuilder<MapBackedParams> luceneSearchQueryBuilder() {
        return new RecordSearchParamsStringQueryBuilder<>(restrictionToLuceneQueryConverter(), fondLoader)
                .withDocumentConjunctionModifier(MapBackedParams.class, new CommonDocumentParamsConjunctionModifier(departmentAccessor, enabledFondsProvider, fondLoader))
                .withDocumentConjunctionModifier(MapBackedParams.class, new DocumentFieldsSearchParamsRestrictionModifier(new QToLuceneQueryConverterByPattern(settingLoader.getDepartmentedProvider(SettingKeys.GLOBAL_SEARCH_TEMPLATE_1), keywordsParser())))
                .withAuthorityConjunctionModifier(MapBackedParams.class, new CommonAuthorityParamsConjunctionModifier(enabledFondsProvider))
                .withAuthorityConjunctionModifier(MapBackedParams.class, new AuthorityFieldsSearchParamsRestrictionModifier(new QToLuceneQueryConverterByPattern(settingLoader.getDepartmentedProvider(SettingKeys.AUTHORITY_GLOBAL_SEARCH_QUERY_TEMPLATE), keywordsParser())))
                .withRestrictionModifierFor(MapBackedParams.class, new CommonRecordSearchParamsRestrictionModifier(linkConstraintsResolver, enabledLoadableFondsExpander))
                .withRestrictionModifierFor(MapBackedParams.class, new RawQueryAddingConjunctionModifier())
                .withRestrictionModifierFor(MapBackedParams.class, new FieldedQAddingConjunctionModifier());
    }


    @Bean
    public Converter<Restriction<? extends SearchField>, String> restrictionToLuceneQueryConverter() {
        Converter<Restriction<? extends LuceneQueryField>, String> luceneFieldRestrictionToQuery = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .registerEqValueConverter(Integer.class, new IdToLuceneValueConverter())
                .registerEqValueConverter(String.class, new StringToLuceneValueConverter())
                .registerEqValueConverter(LocalDate.class, new LocalDateToLuceneValueConverter())
                .registerEqValueConverter(RecordIdFondPair.class, new RecordHeaderToLuceneValueConverter())
                .registerBetweenValueItemConverter(Identified.class, new IdentifiedToLuceneValueConverter())
                .registerEqValueConverter(Identified.class, new IdentifiedToLuceneValueConverter())
                .buildLuceneFieldRestrictionToQuery();


        Converter<Restriction<? extends SearchField>, Restriction<? extends LuceneQueryField>> simpleSearchFieldRestrictionToLuceneFieldRestriction = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .buildSearchFieldRestrictionToLuceneFieldRestriction();


        Converter<Restriction<? extends SearchField>, Restriction<? extends LuceneQueryField>> searchFieldRestrictionToLuceneFieldRestriction = new RestrictionToLuceneQueryConverterBuilder(keywordsParser(), luceneQueryFieldLoader())
                .configureRecursiveSearchFieldTermConverter((searchFieldTermConverter, finalConverter) -> {
                    searchFieldTermConverter.addByField(StaticSearchFields.SUBKIND, new SubkindFieldTermToFondListConverter(enabledDocumentFondsProvider, enabledAuthorityFondsProvider));
                })
                .configureNonRecursiveSearchFieldTermConverter((searchFieldTermConverter, finalConverter) -> {
                    searchFieldTermConverter.addFirst(new SingleStringMatcherValueTermMatcher<>(IsbnChecker::isValidIsbn), new IsbnDisjunctionAddingTermConverter<SearchField>(StaticSearchFields.DOCUMENT_ISBN).andThen(simpleSearchFieldRestrictionToLuceneFieldRestriction));
                    searchFieldTermConverter.addFirst(new SingleStringMatcherValueTermMatcher<>(IsbnChecker::isValidIssn), new IsbnDisjunctionAddingTermConverter<SearchField>(StaticSearchFields.DOCUMENT_ISSN).andThen(simpleSearchFieldRestrictionToLuceneFieldRestriction));
                })
                .buildSearchFieldRestrictionToLuceneFieldRestriction();

        return searchFieldRestrictionToLuceneFieldRestriction
                .andThen(luceneFieldRestrictionToQuery);
    }


    @EventListener(ApplicationReadyEvent.class)
    public void registerConverters() {
        converterRegisterer
                .registerForStringId(CacheMode.class, CacheMode.CODEBOOK)
                .registerForStringId(Datasource.class, datasourceLoader())
                .registerForStringId(FacetKeyIdentified.class, id -> new FacetKeyIdentified.StringIdToFacetKeyIdentifiedConverter().convert(id))
                .registerForIntegerId(FacetDefinitionType.class, FacetDefinitionType.CODEBOOK)
                .registerForIntegerId(FacetScope.class, FacetScope.CODEBOOK)
                .registerForStringId(SearchField.class, searchFieldLoader())
                .registerForStringId(SortingItem.class, searchSortingLoader())
                .registerForUuidId(Search.class, searchRepository());

        conversionService.addConverter(String.class, Restriction.class, source -> {
            try {
                return objectMapper.readValue(source, Restriction.class);
            } catch (IOException e) {
                throw new RuntimeException("Cannot deserialize json to %s".formatted(Restriction.class.getSimpleName()), e);
            }
        });

        objectMapperModule
                .addSerializer(Restriction.class, new RestrictionSerializer())
                .addDeserializer(Restriction.class, new RestrictionDeserializerPostModifyingDecorator(new RestrictionDeserializerWithSearchFields(), datatypableStringConverter, datatypeBySearchFieldLoader()));
    }
}
