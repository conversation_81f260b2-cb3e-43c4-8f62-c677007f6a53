import {someSpecifiedParamsAreEmpty, specifiedParamsHaveChanged} from './search-params-utils';
import type {SearchParams} from 'typings/portaro.be.types';
import {Kind, SearchType} from 'shared/constants/portaro.constants';


describe('search-params-utils tests', () => {

    describe('someSpecifiedParamsAreEmpty tests', () => {

        const emptyObj: SearchParams = {};
        const emptyStringParam: SearchParams = {q: ''};
        const nullObjParam: SearchParams = {type: null}
        const emptyArrayParam: SearchParams = {department: []};

        const combinedEmptyParams: SearchParams = {...emptyArrayParam, ...emptyStringParam, ...nullObjParam};
        const emptyParamsNames = Object.keys(combinedEmptyParams); // ['q', 'type', 'department']
        const emptyAndNonEmptyParams: SearchParams = {...combinedEmptyParams, kind: [Kind.KIND_RECORD], pageNumber: 1, pageSize: 20}

        it('should return true for single param objects', () => {
            expect(someSpecifiedParamsAreEmpty(emptyObj)).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyStringParam)).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(nullObjParam)).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyArrayParam)).toBeTrue();
        });

        it('should return true for multi param objects', () => {
            expect(someSpecifiedParamsAreEmpty(combinedEmptyParams)).toBeTrue();
        });

        it('should  return true for missing params', () => {
            expect(someSpecifiedParamsAreEmpty(emptyObj, ['q'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty({q: 'query'}, ['type', 'kind'])).toBeTrue()
        });


        it('should return true for specified empty params', () => {
            expect(someSpecifiedParamsAreEmpty(combinedEmptyParams, ['q'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(combinedEmptyParams, ['type'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(combinedEmptyParams, ['department'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(combinedEmptyParams, emptyParamsNames)).toBeTrue();
        });

        it('should return true only for specified empty params', () => {
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['q'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['type'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['department'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, emptyParamsNames)).toBeTrue();
        });

        it('should return true for combined empty and non empty params without specifying empty ones', () => {
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams)).toBeTrue();
        });

        it('should return false when specified non empty params', () => {
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['kind'])).toBeFalse();
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['pageNumber', 'pageSize'])).toBeFalse();
        });

        it('should return false when specified empty and non empty params', () => {
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['kind', 'type'])).toBeTrue();
            expect(someSpecifiedParamsAreEmpty(emptyAndNonEmptyParams, ['q', 'pageSize'])).toBeTrue();
        });
    });


    describe('specifiedParamsHaveChanged tests', () => {

        const testParams: SearchParams = {
            type: SearchType.TYPE_SEARCH_SELECTION,
            kind: [Kind.KIND_RECORD],
            pageNumber: 1,
            pageSize: 20
        };

        const equalParams: SearchParams = {
            type: SearchType.TYPE_SEARCH_SELECTION,
            kind: [Kind.KIND_RECORD],
            pageNumber: 1,
            pageSize: 20
        };

        const changedParams: SearchParams = {
            type: SearchType.TYPE_SEARCH_SELECTION,
            kind: [Kind.KIND_RECORD],
            pageNumber: 4,
            pageSize: 20
        };

        const extendedParams: SearchParams = {
            type: SearchType.TYPE_SEARCH_SELECTION,
            kind: [Kind.KIND_RECORD],
            pageNumber: 1,
            pageSize: 20,
            q: 'query'
        };

        const differentParams: SearchParams = {
            type: SearchType.TYPE_FILE_SEARCH,
            kind: [Kind.KIND_FILE],
            pageNumber: 2,
            pageSize: 40
        };

        it('should return false for comparison with itself', () => {
            expect(specifiedParamsHaveChanged(testParams, testParams, Object.keys(testParams))).toBeFalse();
            expect(specifiedParamsHaveChanged(testParams, testParams, ['type'])).toBeFalse();
            expect(specifiedParamsHaveChanged(testParams, testParams, ['type', 'pageSize'])).toBeFalse();
        });

        it('should return false for comparison with equal params', () => {
            expect(specifiedParamsHaveChanged(testParams, equalParams, Object.keys(testParams))).toBeFalse();
            expect(specifiedParamsHaveChanged(testParams, equalParams, ['type'])).toBeFalse();
            expect(specifiedParamsHaveChanged(testParams, equalParams, ['type', 'pageSize'])).toBeFalse();
        });

        it('should return false for subset of extended params, equals to itself', () => {
            expect(specifiedParamsHaveChanged(testParams, extendedParams, Object.keys(testParams))).toBeFalse();
        });


        it('should return true for extended params with additional params', () => {
            expect(specifiedParamsHaveChanged(testParams, extendedParams, Object.keys(extendedParams))).toBeTrue();
            expect(specifiedParamsHaveChanged(testParams, extendedParams, ['q'])).toBeTrue();
        });


        it('should return false for unchanged subset of params', () => {
            expect(specifiedParamsHaveChanged(testParams, changedParams, ['pageSize', 'type'])).toBeFalse();
        });

        it('should return true for changed subset of params', () => {
            expect(specifiedParamsHaveChanged(testParams, changedParams, ['pageNumber', 'kind'])).toBeTrue();
        });

        it('should return true for completely different params', () => {
            expect(specifiedParamsHaveChanged(testParams, differentParams, Object.keys(testParams))).toBeTrue();
            expect(specifiedParamsHaveChanged(testParams, differentParams, ['pageNumber', 'kind'])).toBeTrue();
            expect(specifiedParamsHaveChanged(testParams, differentParams, ['pageSize', 'type'])).toBeTrue();
        });

    });
});