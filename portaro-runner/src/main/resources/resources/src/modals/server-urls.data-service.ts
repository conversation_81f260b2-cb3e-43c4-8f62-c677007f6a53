import {ngAsync} from 'shared/utils/ng-@decorators';
import type {Department} from 'typings/portaro.be.types';
import type {AjaxService} from 'core/data-services/ajax.service';

export default class ServerUrlsDataService {
    public static readonly serviceName = 'serverUrlsDataService';

    private static readonly SERVER_URLS_ROUTE = 'server-urls';

    /*@ngInject*/
    constructor(private ajaxService: AjaxService) {
    }

    @ngAsync()
    public async getDepartmentUrl(): Promise<{[url: string]: Department}> {
        return this.ajaxService
            .createRequest(ServerUrlsDataService.SERVER_URLS_ROUTE)
            .get();
    }
}