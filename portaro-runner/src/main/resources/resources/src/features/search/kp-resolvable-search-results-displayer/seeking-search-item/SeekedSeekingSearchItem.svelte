<script lang="ts">
    import type {SeekedSeeking, Seeking, SeekingProvision} from 'typings/portaro.be.types';
    import type {SearchManager} from '../../search-manager/search-manager';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {byId, findFirst} from 'shared/utils/array-utils';
    import {SeekingIntentId, SeekingProvisionIntentId} from '../../../ill/seeking-constants';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {Kind} from 'shared/constants/portaro.constants';
    import SeekingService from '../../../ill/seeking.service';
    import DesiredExemplar from '../../../user/DesiredExemplar.svelte';
    import KpPrice from 'shared/components/kp-price/KpPrice.svelte';
    import KpCollapsibleMenuWrapper from 'shared/ui-widgets/menu-wrapper/KpCollapsibleMenuWrapper.svelte';
    import Label from 'shared/components/kp-label/Label.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let seeking: SeekedSeeking;
    export let refreshSearchManager: SearchManager<SeekedSeeking>;

    const localize = getLocalization();
    const dateFormatter = getDateFormatter();
    const seekingService = getInjector().getByToken<SeekingService>(SeekingService.serviceName);
    let processing = false;

    function refreshAll() {
        refreshSearchManager.refreshSearch();
    }

    async function refreshSingle() {
        seeking = await seekingService.getById<SeekedSeeking>(seeking.id);
    }

    function startProcessing() {
        processing = true;
    }

    function endProcessing() {
        processing = false;
    }

    async function doSomething(fn: () => Promise<any>, refreshWhole: boolean = false) {
        startProcessing();
        try {
            await fn();
            if (refreshWhole) {
                refreshAll();
            } else {
                await refreshSingle();
            }
        } finally {
            endProcessing();
        }
    }

    async function createSeekingZiskejLink() {
        await doSomething(() => seekingService.createSeekingZiskejLink(seeking));
    }

    async function editSeekingData() {
        await doSomething(() => seekingService.editSeekingData(seeking));
    }

    async function cancelSeeking() {
        await doSomething(() => seekingService.cancelSeeking(seeking), true);
    }

    async function commenceSeeking() {
        await doSomething(() => seekingService.commenceIllSeeking(seeking));
    }

    async function createSeekingProvision() {
        await doSomething(() => seekingService.createSeekingProvision(seeking));
    }

    async function autoSearchSeekingProvisions() {
        await doSomething(() => seekingService.autoSearchSeekingProvisions(seeking));
    }

    async function editSeekingProvision(seekingProvision: SeekingProvision) {
        await doSomething(() => seekingService.editSeekingProvision(seeking, seekingProvision));
    }

    async function cancelSeekingProvision(seekingProvision: SeekingProvision) {
        await doSomething(() => seekingService.cancelSeekingProvision(seeking, seekingProvision));
    }

    async function editSeekedSeekingActiveProvisionData() {
        await doSomething(() => seekingService.editSeekedSeekingActiveProvisionData(seeking));
    }

    async function activateSeekingUpcomingProvision() {
        await doSomething(() => seekingService.activateSeekingUpcomingProvision(seeking));
    }

    async function sendSeekingActiveProvisionMessage() {
        await doSomething(() => seekingService.sendSeekingActiveProvisionMessage(seeking));
    }

    async function acceptSeekingActiveProvisionCondition() {
        await doSomething(() => seekingService.acceptSeekingActiveProvisionCondition(seeking));
    }

    async function receiveSeekingActiveProvisionExemplar() {
        await doSomething(() => seekingService.receiveSeekingActiveProvisionExemplar(seeking));
    }

    async function sendBackSeekingActiveProvisionExemplar() {
        await doSomething(() => seekingService.sendBackSeekingActiveProvisionExemplar(seeking));
    }

    async function seekerCancelSeekingActiveProvision() {
        await doSomething(() => seekingService.seekerCancelSeekingActiveProvision(seeking));
    }

    function isSyncedWithZiskej(s: Seeking): boolean {
        return !!s.ziskejSync;
    }

    function isIntentAllowed(s: Seeking, intentId: SeekingIntentId) {
        return findFirst(s.intents, byId(intentId)).permission.allowed;
    }

    function isProvisionIntentAllowed(provision: SeekingProvision, intentId: SeekingProvisionIntentId) {
        return findFirst(provision.intents, byId(intentId)).permission.allowed;
    }
</script>

<KpCollapsibleMenuWrapper>
    <div slot="main" class="list-item-seeking" data-id={seeking.id}>
        <div class="list-item-seeking-main">
            <div class="seeking-requester">
                <Label labeled={seeking.requester} explicitKind={Kind.KIND_USER}/>
                <span class="text-muted">Žádá o</span>
                {#if seeking.requesterObtainDeadlineDate}
                    <span class="text-muted">(do {pipe(seeking.requesterObtainDeadlineDate, dateFormatter('d.M.yyyy'))})</span>
                {/if}
            </div>

            <div class="seeking-request-exemplar">
                <DesiredExemplar desiredExemplar={seeking.desiredExemplar}/>
            </div>

            {#if seeking.activeProvision}
                <div class="seeking-processed-provision">
                    <div>
                        <span class="text-muted">Vyřizuje</span>
                        <Label labeled={seeking.activeProvision.provider} explicitKind={Kind.KIND_USER}/>
                    </div>
                    {#if seeking.activeProvision.providerReferenceId}
                        <div>
                            <kbd>{seeking.activeProvision.providerReferenceId}</kbd>
                        </div>
                    {/if}
                    {#if seeking.activeProvision.seekerActivateDate}
                        <div>
                            <span class="text-muted">{localize(/* @kp-localization ill.seeking.RemoteLibraryOrderDate */ 'ill.seeking.RemoteLibraryOrderDate')}</span>
                            <span>{pipe(seeking.activeProvision.seekerActivateDate, dateFormatter('d.M.yyyy'))}</span>
                        </div>
                    {/if}
                    {#if seeking.activeProvision.exemplarIdentifiers}
                        <div>
                            <span class="text-muted">Exemplář</span>
                            {#each seeking.activeProvision.exemplarIdentifiers as exemplarIdentifier}
                                {exemplarIdentifier.value}
                            {/each}
                        </div>
                    {/if}
                    {#if seeking.activeProvision.providerAcceptDate}
                        <div>
                            <span class="text-muted">Schváleno dodávajícím</span>
                            <span>{pipe(seeking.activeProvision.providerAcceptDate, dateFormatter('d.M.yyyy'))}</span>
                        </div>
                        {#if seeking.activeProvision.providerAcceptCondition}
                            <div>
                                <span class="text-muted">S podmínkou: </span>
                                <span>{seeking.activeProvision.providerAcceptCondition}</span>
                            </div>
                        {/if}
                    {/if}
                    {#if seeking.activeProvision.seekerAcceptDate}
                        <div>
                            <span class="text-muted">Schváleno námi</span>
                            <span>{pipe(seeking.activeProvision.seekerAcceptDate, dateFormatter('d.M.yyyy'))}</span>
                        </div>
                    {/if}
                    {#if seeking.activeProvision.providerSendDate}
                        <div>
                            <span class="text-muted">Odesláno protistranou</span>
                            <span>{pipe(seeking.activeProvision.providerSendDate, dateFormatter('d.M.yyyy'))}</span>
                        </div>
                    {/if}
                    {#if seeking.activeProvision.seekerReceiveDate}
                        <div>
                            <span class="text-muted">{localize(/* @kp-localization ill.seeking.ExemplarReceiveDate */ 'ill.seeking.ExemplarReceiveDate')}</span>
                            <span>{pipe(seeking.activeProvision.seekerReceiveDate, dateFormatter('d.M.yyyy'))}</span>
                        </div>
                    {/if}
                    {#if seeking.activeProvision.seekerCancelDate || seeking.activeProvision.providerCancelDate}
                        <div>
                            <div>
                                {#if seeking.activeProvision.seekerCancelDate && seeking.activeProvision.providerCancelDate}
                                    <span>Zrušeno</span>
                                {:else}
                                    <span>Zažádáno o zrušení</span>
                                {/if}
                            </div>
                            <div>
                                {#if seeking.activeProvision.seekerCancelDate}
                                    <span class="text-muted">Zrušení jsme potvrdili</span>
                                    <span>{pipe(seeking.activeProvision.seekerCancelDate, dateFormatter('d.M.yyyy'))}</span>
                                {:else}
                                    <span class="text-muted">Zrušení jsme zatím nepotvrdili</span>
                                {/if}
                            </div>
                            <div>
                                {#if seeking.activeProvision.providerCancelDate}
                                    <span class="text-muted">Zrušení protistrana potvrdila</span>
                                    <span>{pipe(seeking.activeProvision.providerCancelDate, dateFormatter('d.M.yyyy'))}</span>
                                {:else}
                                    <span class="text-muted">Zrušení zatím protistrana nepotvrdila</span>
                                {/if}
                            </div>
                        </div>
                    {/if}
                </div>
            {/if}

            {#if seeking.endedProvisions.length > 0}
                <div class="seeking-provision-list">
                    <h5>Ukončené</h5>
                    <ul>
                        {#each seeking.endedProvisions as provision (provision.id)}
                            <li class="seeking-provision-list-item">
                                <div></div>
                                <div>
                                    <Label labeled={provision.provider} explicitKind={Kind.KIND_USER}/>
                                </div>
                                <div>
                                    {pipe(provision.deliveryChannel, loc())}
                                </div>
                                <div></div>
                            </li>
                        {/each}
                    </ul>
                </div>
            {/if}

            {#if seeking.pendingProvisions.length > 0}
                <div class="seeking-provision-list">
                    <h5>Pořadí navržených knihoven</h5>
                    <ul>
                        {#each seeking.pendingProvisions as provision, i (provision.id)}
                            <li class="seeking-provision-list-item">
                                <div>
                                    {i + 1}
                                </div>
                                <div>
                                    <Label labeled={provision.provider} explicitKind={Kind.KIND_USER}/>
                                </div>
                                <div>
                                    {pipe(provision.deliveryChannel, loc())}
                                </div>
                                <div>
                                    {#if isProvisionIntentAllowed(provision, SeekingProvisionIntentId.seekingProvisionDataEdit)}
                                        <button class="btn btn-xs btn-default" disabled={processing} on:click={() => editSeekingProvision(provision)}>
                                            <UIcon icon="edit"/>
                                        </button>
                                    {/if}
                                    {#if isProvisionIntentAllowed(provision, SeekingProvisionIntentId.seekingProvisionCancel)}
                                        <button class="btn btn-xs btn-default" disabled={processing} on:click={() => cancelSeekingProvision(provision)}>
                                            <UIcon icon="cross-circle"/>
                                        </button>
                                    {/if}
                                </div>
                            </li>
                        {/each}
                    </ul>
                </div>
            {/if}
        </div>

        <div class="list-item-seeking-side">
            {#if seeking.cancelled}
                <div>
                    <strong>ZRUŠENO</strong>
                </div>
            {/if}
            <div>
                <strong>{pipe(seeking.state, loc())}</strong>
            </div>
            {#if seeking.seekerReferenceId || seeking.ziskejSync}
                <div>
                    {#if seeking.seekerReferenceId}
                        <kbd>{seeking.seekerReferenceId}</kbd>
                    {/if}
                    {#if seeking.ziskejSync}
                        <span class="label label-warning">Získej</span>
                    {/if}
                </div>
            {/if}
            <div>
                <span class="text-muted">Oddělení</span>
                <span><Label labeled={seeking.department}/></span>
            </div>
            {#if seeking.note}
                <div>
                    <span class="text-muted">Poznámka</span>
                    <span>{seeking.note}</span>
                </div>
            {/if}
            <div>
                <div>
                    <span class="text-muted">{localize(/* @kp-localization commons.Vytvoreno */ 'commons.Vytvoreno')}</span>
                    <span>{pipe(seeking.createDate, dateFormatter('d.M.yyyy'))}</span>
                </div>
                {#if seeking.commenceDate}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization ill.CommenceDate */ 'ill.CommenceDate')}</span>
                        <span>{pipe(seeking.commenceDate, dateFormatter('d.M.yyyy'))}</span>
                    </div>
                {/if}
                {#if seeking.activeProvision?.seekerReceiveDate}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization ill.seeking.ExemplarReceiveDate */ 'ill.seeking.ExemplarReceiveDate')}</span>
                        <span>{pipe(seeking.activeProvision.seekerReceiveDate, dateFormatter('d.M.yyyy'))}</span>
                    </div>
                {/if}
                {#if seeking.activeProvision?.seekerSendDate}
                    <div>
                        <span class="text-muted">{localize(/* @kp-localization ill.seeking.ExemplarSendBackDate */ 'ill.seeking.ExemplarSendBackDate')}</span>
                        <span>{pipe(seeking.activeProvision.seekerSendDate, dateFormatter('d.M.yyyy'))}</span>
                    </div>
                {/if}
            </div>
            {#if seeking.activeProvision?.price}
                <div>
                    <KpPrice price={seeking.activeProvision.price}/>
                </div>
            {/if}
        </div>
    </div>
    <div slot="menu" let:collapsed>
        {#if isSyncedWithZiskej(seeking)}
            <li class="collapsible-menu-item">
                <a class="btn btn-block btn-warning" href={seeking.ziskejSync.webUrl} target="_blank" role="button">
                    <span class:sr-only={!collapsed} aria-hidden="true"><strong>Z</strong></span>
                    <span class:sr-only={collapsed}>Zobrazit na Získej</span>
                </a>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingSyncLinkCreate)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => createSeekingZiskejLink()}>
                    <span class:sr-only={!collapsed} aria-hidden="true"><strong>Z</strong></span>
                    <span class:sr-only={collapsed}>Odeslat do Získej</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingActiveProvisionExemplarSendBack)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => sendBackSeekingActiveProvisionExemplar()}>
                    <UIcon icon="share-square"/>
                    <span class:sr-only={collapsed}>Poslat zpět protistraně</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingActiveProvisionReceive)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => receiveSeekingActiveProvisionExemplar()}>
                    <UIcon icon="download"/>
                    <span class:sr-only={collapsed}>Převzít exemplář</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingActiveProvisionConditionAccept)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => acceptSeekingActiveProvisionCondition()}>
                    <UIcon icon="social-network"/>
                    <span class:sr-only={collapsed}>Přijmout podmínku</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingActiveProvisionMessageSend)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => sendSeekingActiveProvisionMessage()}>
                    <UIcon icon="envelope"/>
                    <span class:sr-only={collapsed}>Poslat zprávu</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingUpcomingProvisionActivate)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => activateSeekingUpcomingProvision()}>
                    <UIcon icon="check-circle"/>
                    <span class:sr-only={collapsed}>Aktivovat první na řadě</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingProvisionsAutoSearch)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => autoSearchSeekingProvisions()}>
                    <UIcon icon="search"/>
                    <span class:sr-only={collapsed}>Automaticky najít knihovny</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingProvisionCreate)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} class:btn-xs={seeking.pendingProvisions.length > 0} on:click={() => createSeekingProvision()}>
                    <UIcon icon="list"/>
                    {#if seeking.pendingProvisions.length === 0}
                        <span class:sr-only={collapsed}>Vybrat knihovnu</span>
                    {:else}
                        <span class:sr-only={collapsed}>Vybrat další knihovnu</span>
                    {/if}
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingCommence)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-default" disabled={processing} on:click={() => commenceSeeking()}>
                    <UIcon icon="check-circle"/>
                    <span class:sr-only={collapsed}>Začít zpracovávat</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingDataEdit)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-xs btn-default" disabled={processing} on:click={() => editSeekingData()}>
                    <UIcon icon="edit"/>
                    <span class:sr-only={collapsed}>Upravit objednávku</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingSeekedActiveProvisionDataEdit)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-xs btn-default" disabled={processing} on:click={() => editSeekedSeekingActiveProvisionData()}>
                    <UIcon icon="edit"/>
                    <span class:sr-only={collapsed}>Upravit požadavek</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingActiveProvisionSeekerCancel)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-xs btn-default" disabled={processing} on:click={() => seekerCancelSeekingActiveProvision()}>
                    <UIcon icon="cross-circle"/>
                    <span class:sr-only={collapsed}>Zrušit protistranu</span>
                </button>
            </li>
        {/if}
        {#if isIntentAllowed(seeking, SeekingIntentId.seekingCancel)}
            <li class="collapsible-menu-item">
                <button class="btn btn-block btn-xs btn-default" disabled={processing} on:click={() => cancelSeeking()}>
                    <UIcon icon="cross-circle"/>
                    <span class:sr-only={collapsed}>Zrušit</span>
                </button>
            </li>
        {/if}
    </div>
</KpCollapsibleMenuWrapper>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "src/shared/ui-widgets/collapsible-button-menu/collapsible-menu.constants.less";

    :global {
        .menu-wrapper-main:has(.list-item-seeking) {
            background-color: #f8f8f8;
        }
    }

    .list-item-seeking {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 10px;
    }

    .seeking-requester {
        margin-bottom: 4px;
    }

    .seeking-request-exemplar {
        margin-top: 6px;
    }

    .seeking-processed-provision {
        margin-top: 14px;
    }

    .seeking-provision-list {
        margin-top: 14px;

        .seeking-provision-list-item {
            display: grid;
            grid-template-columns: 4% auto 18% 14%;
            gap: 6px;
        }
    }
</style>