<script lang="ts">
    import type {ViewableSearchField} from 'typings/portaro.be.types';
    import type {CriterionsFormManagerState} from '../../search-criteria/criterions-form-manager';
    import type {SearchStringsState} from '../types';
    import type {Criterion} from '../../search-criteria/criteria.types';
    import type {KpSearchFormManager} from '../kp-search-form.manager';
    import {CRITERION_OPERATORS} from '../../search-criteria/criterions/criterion-operators';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {flip} from 'svelte/animate';
    import {popInAnim, popOutAnim} from 'shared/animations/pop-animations';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let manager: KpSearchFormManager;
    export let expertSearch: boolean;
    export let criterionsState: CriterionsFormManagerState;
    export let searchStringsState: SearchStringsState;

    const localize = getLocalization();
    const flipAnimationsParams = {duration: 250};

    const criterionOperators = ['AND', 'OR', 'AND_NOT'];
    const operatorInfos = {
        AND: {
            inputId: 'radioAnd',
            value: CRITERION_OPERATORS.AND,
            label: localize(/* @kp-localization hledani.aZaroven */ 'hledani.aZaroven')
        },
        OR: {
            inputId: 'radioOr',
            value: CRITERION_OPERATORS.OR,
            label: localize(/* @kp-localization hledani.nebo */ 'hledani.nebo')
        },
        AND_NOT: {
            inputId: 'radioNot',
            value: CRITERION_OPERATORS.AND_NOT,
            label: localize(/* @kp-localization hledani.aZarovenNeni */ 'hledani.aZarovenNeni')
        }
    };

    let newFieldValue: ViewableSearchField | null = null;

    const handleAddNewCriterionSelected = (event: Event) => {
        const selectElement = event.target as HTMLSelectElement;
        const field = JSON.parse(selectElement.value) as ViewableSearchField;

        manager.addCriterion(field);
        newFieldValue = null;
    }

    const handleCriterionTypeChange = (event: Event, criterion: Criterion) => {
        const selectElement = event.target as HTMLSelectElement;
        const field = JSON.parse(selectElement.value) as ViewableSearchField;

        manager.updateCriterionByField(criterion, field);
    }
</script>

<form class="form-horizontal" on:submit|preventDefault={() => manager.submitForm(criterionsState.isAnyCriterionFilled)}>
    <KpGenericPanel>
        {#each criterionsState.criterions as criterion, i (criterion)}
            <div class="criterion-container"
                 animate:flip={flipAnimationsParams}
                 in:popInAnim={{key: criterion}}
                 out:popOutAnim={{key: criterion}}>

                <!-- AND/OR/NOT -->
                {#if expertSearch && i !== 0}
                    <div class="row">
                        <div class="col-sm-12 operators-container">
                            {#each criterionOperators as operator}
                                {@const operatorInfo = operatorInfos[operator]}

                                <span class="operator">
                                    <input type="radio"
                                           id="{operatorInfo.inputId}_{i}"
                                           value="{operatorInfo.value}"
                                           on:change={() => manager.buildQueryIfAdmin()}
                                           bind:group={criterion.operator}/>

                                    <label for="{operatorInfo.inputId}_{i}">{operatorInfo.label}</label>
                                </span>
                            {/each}
                        </div>
                    </div>
                {/if}

                <!-- Row with label and value editor -->
                <div class="row editor-row">
                    {#if !expertSearch}
                        <label class="col-sm-3 control-label"
                               id="{criterion.field.id}-label"
                               for="criterion-editor-{criterion.field.id}">

                            {pipe(criterion.field, loc())}
                        </label>
                    {/if}

                    {#if expertSearch}
                        <div class="col-sm-3">
                            <select class="form-control input-sm"
                                    value="{JSON.stringify(criterion.field)}"
                                    on:change={(event) => handleCriterionTypeChange(event, criterion)}>
                                {#each manager.pageData.searchFields as field(field.id)}
                                    <option value="{JSON.stringify(field)}">{pipe(field, loc())}</option>
                                {/each}
                            </select>
                        </div>
                    {/if}

                    <div class="col-sm-8" id="criterion-editor-{criterion.field.id}">
                        <KpValueEditor {...criterion.field.editor}
                                       model="{criterion.value}"
                                       editorId="{criterion.field.id}"
                                       on:model-change={(event) => manager.updateCriterionValue(criterion, event.detail)}/>
                    </div>

                    {#if expertSearch}
                        <div class="col-sm-1">
                            {#if i !== 0}
                                <button type="button"
                                        class="remove-btn"
                                        on:click={() => manager.removeCriterion(criterion)}>

                                    <UIcon icon="trash"/>
                                </button>
                            {/if}
                        </div>
                    {/if}
                </div>
            </div>
        {/each}

        <!-- Add criteria in expert search -->
        {#if expertSearch}
            <div class="row">
                <div class="col-sm-3">
                    <select class="form-control input-sm" bind:value={newFieldValue}
                            on:change={handleAddNewCriterionSelected}>
                        <option value="{null}">+ {localize(/* @kp-localization hledani.pridatKriterium */ 'hledani.pridatKriterium')}</option>

                        {#each manager.pageData.searchFields as field(field.id)}
                            <option value="{JSON.stringify(field)}">{pipe(field, loc())}</option>
                        {/each}
                    </select>
                </div>
            </div>
        {/if}

        <!-- Submit button and restrictions/lucene query strings -->
        <div class="form-submit">
            <div class="col-sm-6 col-sm-offset-3">
                <KpButton isBlock
                          buttonType="submit"
                          buttonStyle="accent-blue-new"
                          dataQa="search-documents-button">

                    <IconedContent icon="search">
                        {localize(/* @kp-localization commons.hledat */ 'commons.hledat')}
                    </IconedContent>
                </KpButton>
            </div>

            {#if manager.isAdministrator}
                <div>
                    <code class="col-sm-12 text-muted" style="margin-top: 20px; white-space: pre-wrap;">
                        {searchStringsState.restrictionString}
                    </code>

                    <code class="col-sm-12 text-muted" style="margin-top: 20px;">
                        {searchStringsState.luceneQueryString}
                    </code>
                </div>
            {/if}
        </div>
    </KpGenericPanel>
</form>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .operators-container {
        display: flex;
        align-items: center;
        gap: @spacing-m;
        margin-bottom: @spacing-xs;

        input,
        label {
            padding: 0;
            margin: 0;
        }

        .operator {
            display: flex;
            align-items: center;
            gap: @spacing-s;
            cursor: pointer;
            padding: @spacing-xs @spacing-s;
            border-radius: @border-radius-small;
            transition: background-color 0.2s ease-in-out;

            &:hover {
                background-color: #EAEEFF;
            }
        }
    }

    .editor-row {
        margin-bottom: @spacing-sm;
    }

    .remove-btn {
        width: 32px;
        height: 32px;
        border-radius: @border-radius-default;
        border: rgba(0, 0, 0, 0.25);
        background-color: var(--danger-red);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.3s ease-in-out;

        &:hover {
            opacity: 0.75;
        }
    }
</style>