<script lang="ts">
    import type {AuthorityDetailView} from '../kp-authority-detail-page/types';
    import type {DocumentDetailView} from '../kp-document-detail-page/types';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onDestroy, onMount} from 'svelte';
    import KpDocumentDetailPage from '../kp-document-detail-page/KpDocumentDetailPage.svelte';
    import KpAuthorityDetailPage from '../kp-authority-detail-page/KpAuthorityDetailPage.svelte';

    export let detailView: AuthorityDetailView | DocumentDetailView;

    function isAuthorityDetail(view: AuthorityDetailView | DocumentDetailView): view is AuthorityDetailView {
        return 'authorityTabsConfiguration' in view && exists(view.authorityTabsConfiguration);
    }

    function isDocumentDetail(view: AuthorityDetailView | DocumentDetailView): view is DocumentDetailView {
        return 'documentTabsConfiguration' in view && exists(view.documentTabsConfiguration);
    }

    let previousPageTitle: string;

    onMount(() => {
        previousPageTitle = document.title;
        document.title = pipe(detailView.record, loc());
    });

    onDestroy(() => {
        document.title = previousPageTitle;
    });
</script>

{#if isDocumentDetail(detailView)}
    <KpDocumentDetailPage view="{detailView}"/>
{/if}

{#if isAuthorityDetail(detailView)}
    <KpAuthorityDetailPage view="{detailView}"/>
{/if}