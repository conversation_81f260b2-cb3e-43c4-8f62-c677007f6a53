<script lang="ts">
    import type * as Pdfjs from 'pdfjs-dist/types/src/pdf';
    import type {PdfRotation} from 'src/features/media-viewer/lib/mv-viewed-content';
    import type {MvPdfRenderService} from 'src/features/media-viewer/services/mv-pdf-render.service';
    import {onDestroy, onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {getViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';

    export let pdfDocument: Pdfjs.PDFDocumentProxy;
    export let pageNumber: number;
    export let viewerZoom: number;
    export let pdfRotation: PdfRotation;
    export let pdfRenderer: MvPdfRenderService;

    const viewedContentContext = getViewedContentContext();

    let currentViewport: Pdfjs.PageViewport;
    let page: Pdfjs.PDFPageProxy;

    let isMounted = false;
    let isVisible = false;
    let intersectionObserver: IntersectionObserver;
    let wrapperElement: HTMLDivElement;

    let oldZoom = -1;
    let oldRotation = -1;
    let oldVisibility = false;
    $: updateProps(viewerZoom, pdfRotation, isMounted, isVisible);

    onMount(async () => {
        page = await pdfDocument.getPage(pageNumber);
        currentViewport = page.getViewport({scale: viewerZoom, rotation: pdfRotation});

        intersectionObserver = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
            isVisible = entries.some((entry) => entry.isIntersecting);
        }, {
            rootMargin: '500px',
            threshold: 0.01
        });

        intersectionObserver.observe(wrapperElement);
        isMounted = true;
    });

    onDestroy(() => {
        if (exists(intersectionObserver)) {
            intersectionObserver.disconnect();
        }
    });

    async function renderPage() {
        const renderedCanvasElement = await pdfRenderer.renderPage(page, currentViewport);
        const additionalLayersElement = await pdfRenderer.createAdditionalLayersElement(page, currentViewport, viewedContentContext);

        clearPage();
        wrapperElement.appendChild(renderedCanvasElement);
        wrapperElement.appendChild(additionalLayersElement);
    }

    async function updateProps(newZoom: number, newRotation: PdfRotation, mounted: boolean, visible: boolean) {
        if (!mounted) {
            return;
        }

        const propsChanged = oldZoom !== newZoom || oldRotation !== newRotation || oldVisibility !== visible;
        if (propsChanged) {
            oldZoom = newZoom;
            oldRotation = newRotation;
            oldVisibility = visible;
        }

        currentViewport = page.getViewport({scale: viewerZoom, rotation: pdfRotation});

        if (visible && propsChanged) {
            await renderPage();
        }

        if (!visible) {
            clearPage();
        }
    }

    function clearPage() {
        wrapperElement.innerHTML = '';
    }
</script>

<div id="mv-pdf-page-{pageNumber}"
     class="mv-pdf-page"
     data-page-number={pageNumber}
     bind:this={wrapperElement}
     style:--width="{currentViewport?.width ?? 100}px"
     style:--height="{currentViewport?.height ?? 100}px">
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-page {
        position: relative;
        width: var(--width);
        height: var(--height);
        margin-left: auto;
        margin-right: auto;
        overflow: hidden;
        isolation: isolate;
        border-radius: @border-radius-large;
        outline: 1px solid var(--viewer-default-border);
        background-color: var(--viewer-bg);
    }
</style>