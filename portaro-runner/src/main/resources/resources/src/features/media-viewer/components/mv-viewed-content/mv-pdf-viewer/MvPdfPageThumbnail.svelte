<script lang="ts">
    import type * as Pdfjs from 'pdfjs-dist';
    import type {PdfRotation} from 'src/features/media-viewer/lib/mv-viewed-content';
    import type {MvPdfRenderService} from 'src/features/media-viewer/services/mv-pdf-render.service';
    import {getViewedContentContext} from 'src/features/media-viewer/lib/mv-viewed-content';
    import {GO_TO_PAGE_EVENT} from 'src/features/media-viewer/lib/mv-constants';

    export let pdfDocument: Pdfjs.PDFDocumentProxy;
    export let pageNumber: number;
    export let pdfRenderer: MvPdfRenderService;
    export let pdfRotation: PdfRotation;

    const viewedContent = getViewedContentContext();

    const handleClick = () => {
        viewedContent.eventBus.dispatchEvent(new CustomEvent<number>(GO_TO_PAGE_EVENT, {detail: pageNumber}));
    };
</script>

<button class="mv-pdf-page-thumbnail"
        data-page-number={pageNumber}
        on:click={handleClick}>

    <div class="placeholder"></div>
    <span class="page-title">{pageNumber}</span>
</button>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-page-thumbnail {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: @spacing-s;
        outline: none;
        background: none;
        border: none;
        cursor: pointer;

        .placeholder {
            width: 86px;
            height: 110px;
            border-radius: @border-radius-default;
            background-color: var(--viewer-bg);
            border: 1px solid var(--viewer-default-border);
        }

        .page-title {
            font-size: @font-size-small;
            color: var(--viewer-label-text-color);
        }
    }
</style>