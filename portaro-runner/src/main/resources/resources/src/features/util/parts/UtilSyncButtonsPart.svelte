<script lang="ts">
    import {getLocalization} from 'core/svelte-context/context';
    import KpActionRequestButton from 'shared/components/kp-action-request-button/KpActionRequestButton.svelte';
    import KpUtilButtonsPanel from '../components/KpUtilButtonsPanel.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const localize = getLocalization();
</script>

<KpUtilButtonsPanel
        title="{localize(/* @kp-localization util.ExternalSystemsSyncButtons */ 'util.ExternalSystemsSyncButtons')}">

    <KpActionRequestButton isBlock path="api/sync/edupage" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.EdupageUsersSync */ 'util.ExternalSystemsSyncButtons.EdupageUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/bakalari" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.BakalariUsersSync */ 'util.ExternalSystemsSyncButtons.BakalariUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/edookit" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.EdookitUsersSync */ 'util.ExternalSystemsSyncButtons.EdookitUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/sol" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.SolUsersSync */ 'util.ExternalSystemsSyncButtons.SolUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/sutin" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.SutinUsersSync */ 'util.ExternalSystemsSyncButtons.SutinUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/unis" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.UnisUsersSync */ 'util.ExternalSystemsSyncButtons.UnisUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/unob" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.UnobUsersSync */ 'util.ExternalSystemsSyncButtons.UnobUsersSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/shipment-items" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.ShipmentItemsSync */ 'util.ExternalSystemsSyncButtons.ShipmentItemsSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/ziskej-seeking" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.ZiskejSeekingsSync */ 'util.ExternalSystemsSyncButtons.ZiskejSeekingsSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sync/ziskej-provision" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.ZiskejProvisionsSync */ 'util.ExternalSystemsSyncButtons.ZiskejProvisionsSync')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/bankid/reload" requestMethod="post">
        <IconedContent icon="refresh">
            {localize(/* @kp-localization util.ExternalSystemsSyncButtons.SingleBankID */ 'util.ExternalSystemsSyncButtons.SingleBankID')}
        </IconedContent>
    </KpActionRequestButton>

    <KpActionRequestButton isBlock path="api/sutin/link-company" requestMethod="post">
        <IconedContent icon="refresh">
            Link company to department
        </IconedContent>
    </KpActionRequestButton>
</KpUtilButtonsPanel>