<script lang="ts">
    import type {BarcodeCardSettings} from 'src/features/user/kp-user-barcode-card/types';
    import type {User} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onMount, tick} from 'svelte';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import {WalletBarcodeCardService} from 'src/features/user/kp-user-barcode-card/wallet-barcode-card.service';
    import {isAppleDevice, exists} from 'shared/utils/custom-utils';
    import googlePayIcon from './assets/googlepay.png';
    import appleWalletIcon from './assets/applewallet.png';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import Loc from 'shared/components/kp-markdown/Loc.svelte';

    export let user: User;

    const service = getInjector().getByClass(WalletBarcodeCardService);
    const logger = getLogger();
    const barCode = user.readerAccounts[0]?.barCode;

    let cardElement: HTMLDivElement;
    let barcodeElement: SVGSVGElement;
    let cardSettings: BarcodeCardSettings;
    let barcodeLoading = true;
    let downloadLoading = false;

    let supportsAppleWallet = false;

    onMount(async () => {
        supportsAppleWallet = isAppleDevice();

        cardSettings = await service.getCardSettings();

        if (!exists(barCode) || !exists(cardSettings) || cardSettings?.enabled === false) {
            return;
        }

        // Wait for the elements to be rendered and bound to variables
        await tick();

        try {
            const JsBarcode = await import(/* webpackChunkName: "jsbarcode" */ 'jsbarcode');

            JsBarcode.default(barcodeElement, barCode, {
                format: 'CODE128',
                lineColor: '#000',
                background: '#********',
                margin: 0,
                height: 50,
                displayValue: false
            });

            barcodeLoading = false;
        } catch (error) {
            logger.error('Failed to load JsBarcode library:', error);
            barcodeLoading = false;
        }
    });

    const handleCardDownload = async () => {
        if (downloadLoading) {
            return;
        }

        try {
            downloadLoading = true;

            const {toCanvas} = await import(/* webpackChunkName: "html-to-image" */ 'html-to-image');

            // Create a canvas element from the card element
            const canvas = await toCanvas(cardElement, {
                backgroundColor: null
            });
            const image = canvas.toDataURL('image/png');

            // Create a temporary link and click it to trigger the download
            const link = document.createElement('a');
            link.href = image;
            link.download = 'karticka-ctenare.png';
            link.click();

            // Cleanup
            link.remove();
            canvas.remove();
        } catch (error) {
            logger.error('Failed to download card:', error);
        } finally {
            downloadLoading = false;
        }
    };

    const handleGooglePayClick = async () => {
        const gpLink = await service.generateGoogleWalletCardLink();
        if (!exists(gpLink)) {
            return;
        }

        window.open(gpLink, '_blank');
    };

    const handleAppleWalletClick = async () => {
        await service.generateAndDownloadAppleWalletCard();
    };
</script>

{#if exists(barCode) && exists(cardSettings) && cardSettings.enabled}
    <Flex class="kp-barcode-card-container" direction="column" gap="ml">
        <div class="user-barcode-card"
             class:barcode-loading={barcodeLoading}
             style:--background-color="{cardSettings.backgroundColor}"
             style:--text-color="{cardSettings.textColor}"
             style:--label-color="{cardSettings.labelColor}"
             bind:this={cardElement}>

            {#if exists(cardSettings.backgroundEncoded) && cardSettings.backgroundEncoded}
                <img alt="card-background" src="{`data:image/png;base64,${cardSettings.backgroundEncoded}`}" class="card-background"/>
            {/if}

            <span class="card-title">
                <Loc code="barcodeCards.logoText"/>
            </span>
            <span class="name">{pipe(user, loc())}</span>

            <div class="barcode-container">
                <div class="barcode-image-container">
                    <svg class="barcode" bind:this={barcodeElement}></svg>
                </div>

                <span class="barcode-value">{barCode}</span>
            </div>

            {#if exists(cardSettings.logoEncoded) && cardSettings.logoEncoded}
                <img src="{`data:image/png;base64,${cardSettings.logoEncoded}`}" height="24px" class="logo" alt="Logo"/>
            {/if}
        </div>

        <Flex alignItems="center" columnGap="sm" rowGap="s" wrap="wrap">
            <KpButton buttonSize="sm" buttonStyle="primary" on:click={handleCardDownload} isDisabled={downloadLoading || barcodeLoading}>
                <IconedContent icon="download">
                    <Loc code="barcodeCards.downloadCardImage"/>
                </IconedContent>
            </KpButton>

            {#if supportsAppleWallet}
                <KpButton buttonSize="sm" on:click={handleAppleWalletClick}>
                    <IconedContent>
                        <svelte:fragment slot="icon">
                            <img class="wallet-icon" src="{appleWalletIcon}" alt="Apple Wallet"/>
                        </svelte:fragment>

                        <Loc code="barcodeCards.addCardToAppleWallet"/>
                    </IconedContent>
                </KpButton>
            {/if}

            {#if !supportsAppleWallet}
                <KpButton buttonSize="sm" on:click={handleGooglePayClick}>
                    <IconedContent icon="print">
                        <svelte:fragment slot="icon">
                            <img class="wallet-icon" src="{googlePayIcon}" alt="Google Pay"/>
                        </svelte:fragment>

                        <Loc code="barcodeCards.addCardToGooglePay"/>
                    </IconedContent>
                </KpButton>
            {/if}
        </Flex>
    </Flex>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @card-padding: 24px;

    .user-barcode-card {
        max-width: 400px;
        max-height: 220px;
        width: 100%;
        height: 220px;
        border-radius: 12px;
        overflow: hidden;
        isolation: isolate;
        padding: @card-padding;
        position: relative;
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
        background-color: var(--background-color);
        color: var(--text-color);
        transition: opacity 0.3s ease-in-out;
        box-shadow: rgba(0, 0, 0, 0.07) 0 1px 2px, rgba(0, 0, 0, 0.07) 0 2px 4px, rgba(0, 0, 0, 0.07) 0 4px 8px, rgba(0, 0, 0, 0.07) 0 8px 16px, rgba(0, 0, 0, 0.07) 0 16px 32px, rgba(0, 0, 0, 0.07) 0 32px 64px;

        &.barcode-loading {
            opacity: 0;
        }

        .card-title {
            font-size: @font-size-small;
            color: var(--label-color);
        }

        .name {
            font-size: @font-size-xl;
            font-weight: 500;
        }

        .barcode-container {
            display: flex;
            flex-direction: column;
            gap: @spacing-s;
            margin-top: auto;

            .barcode-image-container {
                padding: @spacing-s;
                background-color: white;
                border-radius: @border-radius-large;
                display: flex;
                width: min-content;
            }

            .barcode-value {
                font-size: @font-size-small;
            }
        }

        .card-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }

        .logo {
            height: 24px;
            position: absolute;
            top: @card-padding;
            right: @card-padding;
        }
    }

    .wallet-icon {
        height: 11px;
    }
</style>