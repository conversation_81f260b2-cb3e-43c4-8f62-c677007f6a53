<script lang="ts">
    import type {OverallOptimisationOverviewResponse} from 'src/features/sutor/pages/project-detail/reports-optimisation/types';
    import {priceFormatter} from 'shared/utils/pipes';
    import {getCurrentLanguage} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {onMount} from 'svelte';
    import {getSutorReportsOptimisationContext} from 'src/features/sutor/pages/project-detail/reports-optimisation/sutor-reports-optimisation-context';
    import {exists} from 'shared/utils/custom-utils';
    import OptimisationOverviewContainer from '../components/OptimisationOverviewContainer.svelte';
    import OptimisationOverviewHeading from '../components/OptimisationOverviewHeading.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import ColoredLabel from '../../../../../components/ColoredLabel.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const context = getSutorReportsOptimisationContext();
    const priceFormat = priceFormatter(getCurrentLanguage());

    let loading = true;
    let overallOptimisationOverview: OverallOptimisationOverviewResponse | null;

    onMount(async () => {
        overallOptimisationOverview = await context.service.getOverallOverview(context.projectRecord);
        loading = false;
    });

    function getTotalReportedHours(): number {
        return overallOptimisationOverview.jobTypeSummaries.reduce((total, jobTypeSummary) => total + jobTypeSummary.reportedStandardHours + jobTypeSummary.reportedOvertimeHours, 0);
    }

    function getTotalChargedHours(): number {
        return overallOptimisationOverview.jobTypeSummaries.reduce((total, jobTypeSummary) => total + jobTypeSummary.chargedStandardHours + jobTypeSummary.chargedOvertimeHours, 0);
    }

    function getTotalReportedPrice(): number {
        return overallOptimisationOverview.jobTypeSummaries.reduce((total, jobTypeSummary) => total + jobTypeSummary.reportedPrice.amount, 0);
    }

    function getTotalChargedPrice(): number {
        return overallOptimisationOverview.jobTypeSummaries.reduce((total, jobTypeSummary) => total + jobTypeSummary.chargedPrice.amount, 0);
    }
</script>

<OptimisationOverviewContainer>
    <OptimisationOverviewHeading heading="Předávací protokol"
                                 printDisabled="{!exists(overallOptimisationOverview)}"
                                 printButtonLabel="Tisk předávacího protokolu"/>

    <KpLoadableContainer fillAvailableSpace {loading} loadError="{!loading && !exists(overallOptimisationOverview)}">
        {#if overallOptimisationOverview.jobTypeSummaries.length === 0}
            <Flex direction="column" alignItems="center" justifyContent="center" fillAvailableSpace>
                <IconedContent icon="info" orientation="vertical" align="center" justify="center">
                    <span class="load-error-label">Nejsou k dispozici žádná data</span>
                </IconedContent>
            </Flex>
        {:else}
            <KpBarebonesTable rowsTopBordered headerFooterDivided fontSize="12px">
                <tr slot="header">
                    <th>Profese</th>
                    <th>Vykázáno (S + P)</th>
                    <th>Účtováno (S + P)</th>
                    <th>Vykázané náklady</th>
                    <th>Účtované náklady</th>
                </tr>

                <svelte:fragment slot="body">
                    {#each overallOptimisationOverview.jobTypeSummaries as jobTypeSummary}
                        <tr>
                            <td>
                                <ColoredLabel label="{jobTypeSummary.name}"/>
                            </td>
                            <td>{jobTypeSummary.reportedStandardHours}h + {jobTypeSummary.reportedOvertimeHours}h</td>
                            <td>{jobTypeSummary.chargedStandardHours}h + {jobTypeSummary.chargedOvertimeHours}h</td>
                            <td>{pipe(jobTypeSummary.reportedPrice, priceFormat)}</td>
                            <td>{pipe(jobTypeSummary.chargedPrice, priceFormat)}</td>
                        </tr>
                    {/each}
                </svelte:fragment>

                <svelte:fragment slot="footer">
                    <tr class="footer-row">
                        <td></td>
                        <td>{getTotalReportedHours()} hod.</td>
                        <td>{getTotalChargedHours()} hod.</td>
                        <td>{getTotalReportedPrice()} Kč</td>
                        <td>{getTotalChargedPrice()} Kč</td>
                    </tr>
                </svelte:fragment>
            </KpBarebonesTable>
        {/if}
    </KpLoadableContainer>
</OptimisationOverviewContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .footer-row {
        font-weight: 500;
        font-size: @font-size-large;
    }
</style>