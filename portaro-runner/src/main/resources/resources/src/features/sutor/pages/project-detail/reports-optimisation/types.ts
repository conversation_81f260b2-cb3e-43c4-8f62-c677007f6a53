import type {RecordRow} from 'src/features/record-grid/lib/types';
import type {Price, SimpleFond} from 'typings/portaro.be.types';

export type ProjectType = 'contracted' | 'reported';

// Overall overview
export interface OverallOptimisationOverviewResponse {
    jobTypeSummaries: OverallOptimisationJobTypeSummary[];
}

export interface OverallOptimisationJobTypeSummary {
    name: string;

    reportedStandardHours: number;
    reportedOvertimeHours: number;
    chargedStandardHours: number;
    chargedOvertimeHours: number;

    reportedPrice: Price;
    chargedPrice: Price;
}

// Day overview
export interface DayOptimisationOverviewResponse {
    jobTypeSummaries: DayOptimisationJobTypeSummary[];
}

export interface DayOptimisationJobTypeSummary {
    name: string;

    reportedWorkersCount: number;
    chargedWorkersCount: number;

    reportedStandardHours: number;
    reportedOvertimeHours: number;
    chargedStandardHours: number;
    chargedOvertimeHours: number;

    reportedPrice: Price;
    chargedPrice: Price;
}

export interface OptimisationSumsResponse {
    reportedPrice: Price;
    chargedPrice: Price;
}

export interface GroupedProjectItemsResponse {
    uniqueItemFonds: SimpleFond[];
    itemsWithoutDate: RecordRow[];
    groupedInstallationLogbooks: Record<string, RecordRow[]>;
}