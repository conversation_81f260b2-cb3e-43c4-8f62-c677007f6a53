import type {Observable} from 'rxjs';
import {fromEvent, merge, Subject, withLatestFrom} from 'rxjs';
import {debounceTime, distinctUntilChanged, filter, map} from 'rxjs/operators';
import type {FormControl} from './form-control';


export function formControls(inputElement: InputElement, formControl: FormControl<unknown>) {

    const RESET_SYMBOL = Symbol('reset-notification');

    function extractCurrentViewValue(): string | number {
        if (inputElement instanceof HTMLInputElement && (inputElement.type === 'number' || inputElement.type === 'range')) {
            return inputElement.validity.badInput ? NaN : parseNumber(inputElement.value);
        }
        return inputElement.value;
    }

    function renderViewValue(viewValue: string | number, errors: string[]) {
        if (errors.some((error) => ['number', 'date'].includes(error))) {
            // viewValue is null if there is a parsing error,
            // so we should not overwrite users input with empty string :-)
            return;
        }

        if (inputElement.value.trim() === viewValue?.toString()) {
            // do not overwrite value if viewValue is just trimmed version of actual input value
            return;
        }

        inputElement.value = viewValue?.toString() ?? '';
        resetNotification$.next(RESET_SYMBOL);
    }

    function createInputValueStreamFromEvent(eventName: string, debounce: number): Observable<string | number> {
        return fromEvent(inputElement, eventName).pipe(map(() => extractCurrentViewValue()), debounceTime(debounce))
    }

    // to synchronously flush changes before form submit
    function createInputValueStreamFromEnterKeypress() {
        return fromEvent<KeyboardEvent>(inputElement, 'keydown').pipe(
            filter((event) => event.key === 'Enter'),
            map(() => extractCurrentViewValue()))
    }

    const inputValueStreams = formControl.formControlOptions.updateTriggers.map(({eventName, debounceValue = 0}) => createInputValueStreamFromEvent(eventName, debounceValue));

    const resetNotification$ = new Subject();

    const inputValuesSubscription = merge(...inputValueStreams, createInputValueStreamFromEnterKeypress(), resetNotification$)
        .pipe(
            distinctUntilChanged(NaNAwareCompare),
            filter((value) => value !== RESET_SYMBOL),
            filter((value) => NaNAwareCompare(value, extractCurrentViewValue()))) // ignore debounced value if model was changed in the meantime from outside
        .subscribe((value) => formControl.setViewValue(value));

    const viewValueAndErrorsSubscription = formControl.getViewValue$().pipe(withLatestFrom(formControl.getErrors$())) // for every new view value add the latest (most current) errors
        .subscribe(([viewValue, errors]) => renderViewValue(viewValue, errors));

    return {
        destroy() {
            inputValuesSubscription.unsubscribe(); // event listeners and rxjs cleanup
            viewValueAndErrorsSubscription.unsubscribe();
        }
    }
}

function parseNumber(value: string): number {
    return value === '' ? null : +value; // copied from svelte/internals
}

function NaNAwareCompare(a: unknown, b: unknown): boolean {
    return a === b || (a !== a && b !== b); // NaN === NaN
}

type InputElement = HTMLInputElement | HTMLTextAreaElement