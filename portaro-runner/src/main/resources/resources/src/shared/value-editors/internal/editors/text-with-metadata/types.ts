import type {ValueEditorOptions} from '../../../kp-value-editor/types';
import type {TextValueEditorValidations} from '../text/types';
import type {CommonValueEditorLocalizations} from '../_shared/common-types';

/**
 * @ngdoc type
 * @name TextWithMetadataValueEditorOptions
 * @module portaro.value-editors.text-with-metadata
 *
 * @description
 * Extends {@link type:ValueEditorOptions}
 *
 * Default value: {@link TEXT_WITH_METADATA_VALUE_EDITOR_DEFAULT_OPTIONS}
 */
export type TextWithMetadataValueEditorOptions = ValueEditorOptions

/**
 * @ngdoc type
 * @name TextWithMetadataValueEditorLocalizations
 * @module portaro.value-editors.text-with-metadata
 *
 * @property {string} startsWith Item in combobox.
 * @property {string} equals Item in combobox.
 * @property {string} metadataSelectLabel description of select part
 * @property {string} currentSelectValue currently selected value description
 * @property {string} mainInputLabel description of text-box part
 *
 * @description
 * Extends {@link type:CommonValueEditorLocalizations}
 *
 * Default localizations: {@link TEXT_WITH_METADATA_VALUE_EDITOR_DEFAULT_LOCALIZATIONS}
 */
export interface TextWithMetadataValueEditorLocalizations extends CommonValueEditorLocalizations {
    startsWith: string;
    equals: string;
    metadataSelectLabel: string;
    currentSelectValue: string;
    mainInputLabel: string;
}

/**
 * @ngdoc type
 * @name TextWithMetadataValueEditorModelExtension
 * @module portaro.value-editors.text-with-metadata
 *
 * @description
 * Enum of possible `extensions`.
 *
 * ```
 * enum SearchTextValueEditorModelExtension {
 *      STARTS_WITH = 'startsWith',
 *      EQUALS = 'equals'
 *  }
 * ```
 */
export enum TextWithMetadataValueEditorModelExtension {
    STARTS_WITH = 'startsWith',
    EQUALS = 'equals'
}

/**
 * @ngdoc type
 * @name TextWithMetadataValueEditorModel
 * @module portaro.value-editors.text-with-metadata
 *
 * @requires TextWithMetadataValueEditorModelExtension
 *
 * @description
 * ```
 * interface TextWithMetadataValueEditorModel {
 *      extension: TextWithMetadataValueEditorModelExtension;
 *      row: string;
 *  }
 * ```
 *
 */
export interface TextWithMetadataValueEditorModel {
    extension: TextWithMetadataValueEditorModelExtension;
    row: string;
}

export interface TextWithMetadataValueEditorTypeMap {
    'text-with-metadata': {options: TextWithMetadataValueEditorOptions, validations: TextValueEditorValidations, localizations: TextWithMetadataValueEditorLocalizations};
}