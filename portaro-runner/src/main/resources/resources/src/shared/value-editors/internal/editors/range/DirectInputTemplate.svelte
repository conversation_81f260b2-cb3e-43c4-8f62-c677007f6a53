<script lang="ts">
    import type {RangeValueEditorOptions} from './types';
    import type {EditorsLocalizationFunction} from '../../../localizations/types';
    import type {RangeValueEditorLocalizations} from './types';
    import type {ValueEditorSize} from '../../../types';
    import {createEventDispatcher, getContext} from 'svelte';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import {getValueEditorLabelContext} from '../../label/value-editor-label-context';
    import {ariaLabelledby} from '../../label/use.aria-labelledby';
    import {debounce} from 'lodash-es';
    import {EDITORS_LOCALIZE} from '../../../context-keys';

    export let editorId: string;
    export let editorName: string;

    export let from: number;
    export let to: number;

    export let size: ValueEditorSize;
    export let isDisabled: boolean;
    export let options: RangeValueEditorOptions;

    ignoreUnusedProperties(editorId, editorName);

    const localize: EditorsLocalizationFunction<RangeValueEditorLocalizations> = getContext(EDITORS_LOCALIZE);
    const dispatch = createEventDispatcher<Record<'set-from' | 'set-to', number>>();
    const labelContext = getValueEditorLabelContext();
    const debounceDuration = 1_500; // 1.5s

    const debouncedSetFrom = debounce(() => dispatch('set-from', fromValue), debounceDuration);
    const debouncedSetTo = debounce(() => dispatch('set-to', toValue), debounceDuration);

    const setFrom = () => debouncedSetFrom();
    const setTo = () => debouncedSetTo();

    $: fromValue = from;
    $: toValue = to;
</script>

<div class="size-{size}">
    <div class="range-value-editor-direct-input row" role="group" use:ariaLabelledby={labelContext}>
        <div class="col-xs-3 col-sm-5 col-md-4">
            <input type="number"
                   class="form-control input-{size}"
                   bind:value={fromValue}
                   min={options.min}
                   max={toValue ?? options.max}
                   step={options.step}
                   disabled={isDisabled}
                   on:input={setFrom}
                   aria-label={localize('from')}
                   data-direct-input-from>
        </div>

        <span>-</span>

        <div class="col-xs-3 col-sm-5 col-md-4">
            <input type="number"
                   class="form-control input-{size}"
                   bind:value={toValue}
                   disabled={isDisabled}
                   min={fromValue ?? options.min}
                   max={options.max}
                   step={options.step}
                   on:input={setTo}
                   aria-label={localize('to')}
                   data-direct-input-to>
        </div>
    </div>
</div>

<style lang="less">
    .range-value-editor-direct-input {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }
</style>