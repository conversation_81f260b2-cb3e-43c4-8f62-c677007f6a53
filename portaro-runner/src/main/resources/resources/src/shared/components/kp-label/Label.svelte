<script lang="ts">
    import type {Labeled} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from '../../utils/pipes';
    import {asIdentified, asLabeledReference, isIdentified, isLabeledReference} from '../../utils/types-utils';
    import {Kind} from '../../constants/portaro.constants';
    import {getSanitize} from 'core/svelte-context/context';
    import {isNullOrUndefined} from 'shared/utils/custom-utils';

    const sanitizeHtml = getSanitize();

    export let labeled: Labeled;
    export let linkifyUrls = false;
    export let explicitKind: Kind = null;
    export let additionalClasses = '';
    export let target: '_self' | '_blank' | '_parent' | '_top' = '_self';

    function getUrl(kind: Kind, id: any) {
        switch (kind) {
            case Kind.KIND_RECORD:
                return `/#!/records/${id}`;
            case Kind.KIND_USER:
                return `/#!/users/${id}`;
            default:
                throw new Error(`Kind ${kind} is not supported in Label component`);
        }
    }

    export function urlToLink(): (_: string) => string | null {
        return (text: string) => {
            if (isNullOrUndefined(text)) {
                return null;
            }

            if (text.startsWith('http://') || text.startsWith('https://')) {
                return generateLink(text, text);
            }

            if (text.startsWith('www.')) {
                return generateLink(`http://${text}`, text);
            }

            return text;
        };
    }

    function generateLink(url: string, text: string) {
        return `<a href="${sanitizeHtml(url)}">${sanitizeHtml(text)}</a>`;
    }
</script>

<span class={additionalClasses}>
    {#if isIdentified(labeled) && explicitKind !== null}
        {@const identified = asIdentified(labeled)}
        <a target="{target}" href={getUrl(explicitKind, identified.id)}>{pipe(labeled, loc())}</a>
    {:else if isLabeledReference(labeled)}
        {@const labeledRef = asLabeledReference(labeled)}
        <a target="{target}" href={getUrl(labeledRef.kind, labeledRef.id)}>{pipe(labeled, loc())}</a>
    {:else if linkifyUrls}
        {@html pipe(labeled, loc(), urlToLink()) ?? ''}
    {:else}
        {pipe(labeled, loc())}
    {/if}
</span>
