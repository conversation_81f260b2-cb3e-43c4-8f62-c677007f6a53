<script lang="ts">
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import {getMainMenuContext} from 'shared/components/kp-main-menu/main-menu-context';
    import {onDestroy} from 'svelte';
    import KpGlobalSearchInput from 'shared/global-search/KpGlobalSearchInput.svelte';
    import {getLocalization} from 'core/svelte-context/context';
    import {fade} from 'svelte/transition';
    import {exists} from 'shared/utils/custom-utils';
    import {RECALCULATE_NAV_OVERFLOW_EVENT} from 'shared/components/kp-main-menu/types';

    const context = getMainMenuContext();
    const localize = getLocalization();

    let searchInputElement: HTMLInputElement | null = null;
    let searchOpen = false;
    const widthChangeAnimationDuration = 200;
    const searchOpenUnsubscribe = context.isNavSearchOpen.subscribe((currentSearchOpen) => searchOpen = currentSearchOpen);
    const isStuckToTopUnsubscribe = context.isStuckToTop.subscribe((currentStuckToTop) => {
        if (!currentStuckToTop) {
            context.toggleNavSearchOpen(false);
        }
    });

    onDestroy(() => {
        searchOpenUnsubscribe();
        isStuckToTopUnsubscribe();
    });

    const handleSearchInputFocus = () => {
        context.toggleNavSearchOpen(true);
    }

    const handleSearchInputBlur = () => {
        context.toggleNavSearchOpen(false);
    }

    const handleSearchClick = () => {
        if (!exists(searchInputElement) || searchOpen) {
            return;
        }

        searchInputElement.focus();
    }

    function triggerRecalculateNavOverflow() {
        context.eventBus.dispatchEvent(new CustomEvent<void>(RECALCULATE_NAV_OVERFLOW_EVENT));
    }
</script>

<button type="button"
        class="kp-main-menu-search theme-{context.backgroundTheme}"
        style="--animationDuration: {widthChangeAnimationDuration}ms"
        on:transitionend={() => triggerRecalculateNavOverflow()}
        on:click={handleSearchClick}
        class:opened={searchOpen}
        transition:fade={{duration: context.searchFadeAnimDuration}}>

    {#if !searchOpen}
        <div class="icon-container">
            <UIcon icon="search"/>
        </div>
    {/if}

    <KpGlobalSearchInput inputId="searchStringInputSmall"
                         placeholder="{localize(/* @kp-localization hledani.hledatKnihyAutoryTemata */ 'hledani.hledatKnihyAutoryTemata')}"
                         compact="{true}"
                         additionalButtonClasses="kp-main-menu-search-button"
                         additionalInputClasses="kp-main-menu-search-input text-ellipsis"
                         autofocusOrCompactOpened="{searchOpen}"
                         bind:searchInputElement={searchInputElement}
                         on:focus={handleSearchInputFocus}
                         on:blur={handleSearchInputBlur}/>
</button>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    @search-height: 40px;
    @search-small-width: 120px;

    .kp-main-menu-search {
        max-width: @search-small-width;
        flex: 1;
        border: none;
        height: @search-height;
        display: flex;
        color: @input-color;
        background-color: @body-bg;
        align-items: center;
        align-self: center;
        margin-left: @padding-base-horizontal;
        gap: @padding-xs-horizontal;
        border-radius: @border-radius-base;
        cursor: text;
        padding: 0;
        overflow: hidden;
        transition: max-width var(--animationDuration) ease-in-out;

        &.theme-light {
            border: 1px solid rgba(0, 0, 0, 0.15);
        }

        &.opened {
            max-width: 1000px;

            @media screen and (min-width: 721px) {
                margin-left: auto;
            }

            .icon-container {
                display: none;
            }
        }

        .icon-container {
            font-size: 16px;
            padding-left: @padding-base-horizontal;
        }
    }

    :global {
        .kp-main-menu-search-button {
            height: @search-height;
            padding: 0 @padding-large-horizontal;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            border-width: 1px;
            border-left-width: 0;
            border-radius: 0 @input-border-radius @input-border-radius 0;
            font-size: 16px;
            transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
        }

        .kp-main-menu-search-input {
            width: 100%;
            font-weight: normal;
            padding: 0 @padding-base-horizontal;
            height: @search-height;
            flex: 1;
            outline: none;
            border: none;
        }

        .kp-main-menu-search .input-group {
            flex: 1;
        }
    }
</style>