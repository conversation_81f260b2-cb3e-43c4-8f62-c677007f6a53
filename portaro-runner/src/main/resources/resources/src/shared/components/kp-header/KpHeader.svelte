<script lang="ts">
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {KpHeaderService} from 'shared/components/kp-header/kp-header.service';
    import KpSubSearchLinks from 'shared/components/kp-sub-search-links/KpSubSearchLinks.svelte';
    import KpGlobalSearchInput from 'shared/global-search/KpGlobalSearchInput.svelte';

    export let searchAutofocusOrCompactOpened: boolean;

    const service = getInjector().getByToken<KpHeaderService>(KpHeaderService.serviceName);
    const localize = getLocalization();
</script>

<header class="kp-header logo-stripe">
    <div class="logo-content-container container">
        <div class="logo-search-row row">
            <!-- Custom logo -->
            <div class="customLogoArea custom-logo-area col-xs-12 col-sm-12 col-md-8 col-lg-7">
                <slot name="custom-logo"/>
            </div>

            <!-- Global search with links below -->
            {#if service.isGlobalSearchInputEnabled}
                <div class="logo-search pull-right col-xs-12 col-sm-12 col-md-4 col-lg-5">
                    <KpGlobalSearchInput inputId="searchStringInputVLogu"
                                         additionalInputClasses="form-control searchStringInput"
                                         placeholder="{localize(/* @kp-localization hledani.hledatKnihyAutoryTemata */ 'hledani.hledatKnihyAutoryTemata')}"
                                         autofocusOrCompactOpened="{searchAutofocusOrCompactOpened}"
                                         additionalButtonClasses="searchStringSubmit"/>
                    <KpSubSearchLinks/>
                    <div class="cleaner"></div>
                </div>
            {/if}
        </div>

        <!-- Custom logo footer -->
        <div class="custom-logo-footer-area row">
            <slot name="custom-logo-footer"/>
        </div>
    </div>
</header>