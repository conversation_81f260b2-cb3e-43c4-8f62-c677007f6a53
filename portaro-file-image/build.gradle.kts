dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    implementation(project(":portaro-commons-image"))
    api(project(":portaro-file"))

    implementation("org.slf4j:slf4j-api:+")
    implementation("org.springframework:spring-tx:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")
}
